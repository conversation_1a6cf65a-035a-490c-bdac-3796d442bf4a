// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoMapprGenerator
// **************************************************************************

// ignore_for_file: type=lint, unnecessary_cast, unused_local_variable

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i2;
import 'package:gp_core/models/assignee.dart' as _i9;
import 'package:gp_core/models/bot/bot_response.dart' as _i20;
import 'package:gp_core/models/conversation.dart' as _i14;
import 'package:gp_core/models/orgchat/organization_department.dart' as _i16;
import 'package:gp_core/models/orgchat/organization_role.dart' as _i18;
import 'package:gp_core/models/orgchat/select_invitees_model.dart' as _i22;
import 'package:gp_shared/domain/entity/assignee/assignee.entity.dart' as _i11;
import 'package:gp_shared/domain/entity/assignee/assignee_info.entity.dart'
    as _i13;
import 'package:gp_shared/domain/entity/assignee/assignee_work.entity.dart'
    as _i12;
import 'package:gp_shared/domain/entity/chatbot/chatbot.entity.dart' as _i21;
import 'package:gp_shared/domain/entity/conversation/conversation.entity.dart'
    as _i15;
import 'package:gp_shared/domain/entity/department/department.entity.dart'
    as _i17;
import 'package:gp_shared/domain/entity/role/role.entity.dart' as _i19;
import 'package:gp_shared/domain/entity/select_invite_options.entity.dart'
    as _i23;
import 'package:gp_shared/domain/entity/user/gp_user.entity.dart' as _i10;
import 'package:gp_shared/mapper/entity/assignee_entity_mapper.dart' as _i1;

import '../../data/model/response/coin_qr_code_state_response.dart' as _i7;
import '../../data/model/response/coin_transaction_response.dart' as _i5;
import '../../data/model/response/wallet_info_response.dart' as _i3;
import '../../domain/entity/coin_qr_code.entity.dart' as _i8;
import '../../domain/entity/coin_transaction.entity.dart' as _i6;
import '../../domain/entity/wallet_info.entity.dart' as _i4;
import 'coin_entity_mapper.dart' as _i24;

/// Available mappings:
/// - `WalletInfoResponse` → `WalletInfoEntity`.
/// - `CoinTransactionResponse` → `CoinTransactionEntity`.
/// - `CoinQrCodeStateResponse` → `CoinQrCodeEntity`.
/// - `Assignee` → `GPUserEntity`.
/// - `AssigneeEntity` → `GPUserEntity`.
/// - `GPUserEntity` → `AssigneeEntity`.
/// - `Assignee` → `AssigneeEntity`.
/// - `Work` → `WorkEntity`.
/// - `Info` → `InfoEntity`.
/// - `Conversation` → `ConversationEntity`.
/// - `OrganizationDepartment` → `OrganizationDepartmentEntity`.
/// - `OrganizationRole` → `OrganizationRoleEntity`.
/// - `ChatBotModel` → `ChatBotEntity`.
/// - `SelectInviteesOptions` → `SelectMemberEntity`.
/// - `AssigneeEntity` → `Assignee`.
/// - `WorkEntity` → `Work`.
/// - `InfoEntity` → `Info`.
/// - `ConversationEntity` → `Conversation`.
/// - `OrganizationRoleEntity` → `OrganizationRole`.
/// - `OrganizationDepartmentEntity` → `OrganizationDepartment`.
/// - `ChatBotEntity` → `ChatBotModel`.
///
/// Used delegates: [_i1.AssigneeEntityMapper]
class $CoinEntityMapper implements _i2.AutoMapprInterface {
  const $CoinEntityMapper();

  Type _typeOf<T>() => T;

  List<_i2.AutoMapprInterface> get _delegates =>
      const [const _i1.AssigneeEntityMapper()];

  
  
  ///
  /// Used delegates: [_i1.AssigneeEntityMapper]
  @override
  bool canConvert<SOURCE, TARGET>({bool recursive = true}) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i3.WalletInfoResponse>() ||
            sourceTypeOf == _typeOf<_i3.WalletInfoResponse?>()) &&
        (targetTypeOf == _typeOf<_i4.WalletInfoEntity>() ||
            targetTypeOf == _typeOf<_i4.WalletInfoEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i5.CoinTransactionResponse>() ||
            sourceTypeOf == _typeOf<_i5.CoinTransactionResponse?>()) &&
        (targetTypeOf == _typeOf<_i6.CoinTransactionEntity>() ||
            targetTypeOf == _typeOf<_i6.CoinTransactionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i7.CoinQrCodeStateResponse>() ||
            sourceTypeOf == _typeOf<_i7.CoinQrCodeStateResponse?>()) &&
        (targetTypeOf == _typeOf<_i8.CoinQrCodeEntity>() ||
            targetTypeOf == _typeOf<_i8.CoinQrCodeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.Assignee>() ||
            sourceTypeOf == _typeOf<_i9.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i10.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i10.GPUserEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i11.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i11.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i10.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i10.GPUserEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i10.GPUserEntity>() ||
            sourceTypeOf == _typeOf<_i10.GPUserEntity?>()) &&
        (targetTypeOf == _typeOf<_i11.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i11.AssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.Assignee>() ||
            sourceTypeOf == _typeOf<_i9.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i11.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i11.AssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.Work>() ||
            sourceTypeOf == _typeOf<_i9.Work?>()) &&
        (targetTypeOf == _typeOf<_i12.WorkEntity>() ||
            targetTypeOf == _typeOf<_i12.WorkEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.Info>() ||
            sourceTypeOf == _typeOf<_i9.Info?>()) &&
        (targetTypeOf == _typeOf<_i13.InfoEntity>() ||
            targetTypeOf == _typeOf<_i13.InfoEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i14.Conversation>() ||
            sourceTypeOf == _typeOf<_i14.Conversation?>()) &&
        (targetTypeOf == _typeOf<_i15.ConversationEntity>() ||
            targetTypeOf == _typeOf<_i15.ConversationEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i16.OrganizationDepartment>() ||
            sourceTypeOf == _typeOf<_i16.OrganizationDepartment?>()) &&
        (targetTypeOf == _typeOf<_i17.OrganizationDepartmentEntity>() ||
            targetTypeOf == _typeOf<_i17.OrganizationDepartmentEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i18.OrganizationRole>() ||
            sourceTypeOf == _typeOf<_i18.OrganizationRole?>()) &&
        (targetTypeOf == _typeOf<_i19.OrganizationRoleEntity>() ||
            targetTypeOf == _typeOf<_i19.OrganizationRoleEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i20.ChatBotModel>() ||
            sourceTypeOf == _typeOf<_i20.ChatBotModel?>()) &&
        (targetTypeOf == _typeOf<_i21.ChatBotEntity>() ||
            targetTypeOf == _typeOf<_i21.ChatBotEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i22.SelectInviteesOptions>() ||
            sourceTypeOf == _typeOf<_i22.SelectInviteesOptions?>()) &&
        (targetTypeOf == _typeOf<_i23.SelectMemberEntity>() ||
            targetTypeOf == _typeOf<_i23.SelectMemberEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i11.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i11.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i9.Assignee>() ||
            targetTypeOf == _typeOf<_i9.Assignee?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i12.WorkEntity>() ||
            sourceTypeOf == _typeOf<_i12.WorkEntity?>()) &&
        (targetTypeOf == _typeOf<_i9.Work>() ||
            targetTypeOf == _typeOf<_i9.Work?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i13.InfoEntity>() ||
            sourceTypeOf == _typeOf<_i13.InfoEntity?>()) &&
        (targetTypeOf == _typeOf<_i9.Info>() ||
            targetTypeOf == _typeOf<_i9.Info?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.ConversationEntity>() ||
            sourceTypeOf == _typeOf<_i15.ConversationEntity?>()) &&
        (targetTypeOf == _typeOf<_i14.Conversation>() ||
            targetTypeOf == _typeOf<_i14.Conversation?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i19.OrganizationRoleEntity>() ||
            sourceTypeOf == _typeOf<_i19.OrganizationRoleEntity?>()) &&
        (targetTypeOf == _typeOf<_i18.OrganizationRole>() ||
            targetTypeOf == _typeOf<_i18.OrganizationRole?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i17.OrganizationDepartmentEntity>() ||
            sourceTypeOf == _typeOf<_i17.OrganizationDepartmentEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.OrganizationDepartment>() ||
            targetTypeOf == _typeOf<_i16.OrganizationDepartment?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i21.ChatBotEntity>() ||
            sourceTypeOf == _typeOf<_i21.ChatBotEntity?>()) &&
        (targetTypeOf == _typeOf<_i20.ChatBotModel>() ||
            targetTypeOf == _typeOf<_i20.ChatBotModel?>())) {
      return true;
    }
    if (recursive) {
      for (final mappr in _delegates) {
        if (mappr.canConvert<SOURCE, TARGET>()) {
          return true;
        }
      }
    }
    return false;
  }

  
  
  ///
  /// Used delegates: [_i1.AssigneeEntityMapper]
  @override
  TARGET convert<SOURCE, TARGET>(SOURCE? model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _convert(model)!;
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convert(model)!;
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  ///
  /// Used delegates: [_i1.AssigneeEntityMapper]
  @override
  TARGET? tryConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _safeConvert(
        model,
        onMappingError: onMappingError,
      );
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvert(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    return null;
  }

  
  
  ///
  /// Used delegates: [_i1.AssigneeEntityMapper]
  @override
  Iterable<TARGET> convertIterable<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET>((item) => _convert(item)!);
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertIterable(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  ///
  /// Used delegates: [_i1.AssigneeEntityMapper]
  @override
  Iterable<TARGET?> tryConvertIterable<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET?>(
          (item) => _safeConvert(item, onMappingError: onMappingError));
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertIterable(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  ///
  /// Used delegates: [_i1.AssigneeEntityMapper]
  @override
  List<TARGET> convertList<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertList(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  ///
  /// Used delegates: [_i1.AssigneeEntityMapper]
  @override
  List<TARGET?> tryConvertList<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertList(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  ///
  /// Used delegates: [_i1.AssigneeEntityMapper]
  @override
  Set<TARGET> convertSet<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertSet(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  ///
  /// Used delegates: [_i1.AssigneeEntityMapper]
  @override
  Set<TARGET?> tryConvertSet<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertSet(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  TARGET? _convert<SOURCE, TARGET>(
    SOURCE? model, {
    bool canReturnNull = false,
  }) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i3.WalletInfoResponse>() ||
            sourceTypeOf == _typeOf<_i3.WalletInfoResponse?>()) &&
        (targetTypeOf == _typeOf<_i4.WalletInfoEntity>() ||
            targetTypeOf == _typeOf<_i4.WalletInfoEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i3$WalletInfoResponse_To__i4$WalletInfoEntity(
          (model as _i3.WalletInfoResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i5.CoinTransactionResponse>() ||
            sourceTypeOf == _typeOf<_i5.CoinTransactionResponse?>()) &&
        (targetTypeOf == _typeOf<_i6.CoinTransactionEntity>() ||
            targetTypeOf == _typeOf<_i6.CoinTransactionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i5$CoinTransactionResponse_To__i6$CoinTransactionEntity(
          (model as _i5.CoinTransactionResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i7.CoinQrCodeStateResponse>() ||
            sourceTypeOf == _typeOf<_i7.CoinQrCodeStateResponse?>()) &&
        (targetTypeOf == _typeOf<_i8.CoinQrCodeEntity>() ||
            targetTypeOf == _typeOf<_i8.CoinQrCodeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i7$CoinQrCodeStateResponse_To__i8$CoinQrCodeEntity(
          (model as _i7.CoinQrCodeStateResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.Assignee>() ||
            sourceTypeOf == _typeOf<_i9.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i10.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i10.GPUserEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$Assignee_To__i10$GPUserEntity((model as _i9.Assignee?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i11.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i11.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i10.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i10.GPUserEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i11$AssigneeEntity_To__i10$GPUserEntity(
          (model as _i11.AssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i10.GPUserEntity>() ||
            sourceTypeOf == _typeOf<_i10.GPUserEntity?>()) &&
        (targetTypeOf == _typeOf<_i11.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i11.AssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i10$GPUserEntity_To__i11$AssigneeEntity(
          (model as _i10.GPUserEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.Assignee>() ||
            sourceTypeOf == _typeOf<_i9.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i11.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i11.AssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$Assignee_To__i11$AssigneeEntity((model as _i9.Assignee?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.Work>() ||
            sourceTypeOf == _typeOf<_i9.Work?>()) &&
        (targetTypeOf == _typeOf<_i12.WorkEntity>() ||
            targetTypeOf == _typeOf<_i12.WorkEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$Work_To__i12$WorkEntity((model as _i9.Work?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.Info>() ||
            sourceTypeOf == _typeOf<_i9.Info?>()) &&
        (targetTypeOf == _typeOf<_i13.InfoEntity>() ||
            targetTypeOf == _typeOf<_i13.InfoEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$Info_To__i13$InfoEntity((model as _i9.Info?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i14.Conversation>() ||
            sourceTypeOf == _typeOf<_i14.Conversation?>()) &&
        (targetTypeOf == _typeOf<_i15.ConversationEntity>() ||
            targetTypeOf == _typeOf<_i15.ConversationEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i14$Conversation_To__i15$ConversationEntity(
          (model as _i14.Conversation?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i16.OrganizationDepartment>() ||
            sourceTypeOf == _typeOf<_i16.OrganizationDepartment?>()) &&
        (targetTypeOf == _typeOf<_i17.OrganizationDepartmentEntity>() ||
            targetTypeOf == _typeOf<_i17.OrganizationDepartmentEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i16$OrganizationDepartment_To__i17$OrganizationDepartmentEntity(
          (model as _i16.OrganizationDepartment?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i18.OrganizationRole>() ||
            sourceTypeOf == _typeOf<_i18.OrganizationRole?>()) &&
        (targetTypeOf == _typeOf<_i19.OrganizationRoleEntity>() ||
            targetTypeOf == _typeOf<_i19.OrganizationRoleEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i18$OrganizationRole_To__i19$OrganizationRoleEntity(
          (model as _i18.OrganizationRole?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i20.ChatBotModel>() ||
            sourceTypeOf == _typeOf<_i20.ChatBotModel?>()) &&
        (targetTypeOf == _typeOf<_i21.ChatBotEntity>() ||
            targetTypeOf == _typeOf<_i21.ChatBotEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i20$ChatBotModel_To__i21$ChatBotEntity(
          (model as _i20.ChatBotModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i22.SelectInviteesOptions>() ||
            sourceTypeOf == _typeOf<_i22.SelectInviteesOptions?>()) &&
        (targetTypeOf == _typeOf<_i23.SelectMemberEntity>() ||
            targetTypeOf == _typeOf<_i23.SelectMemberEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i22$SelectInviteesOptions_To__i23$SelectMemberEntity(
          (model as _i22.SelectInviteesOptions?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i11.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i11.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i9.Assignee>() ||
            targetTypeOf == _typeOf<_i9.Assignee?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i11$AssigneeEntity_To__i9$Assignee(
          (model as _i11.AssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i12.WorkEntity>() ||
            sourceTypeOf == _typeOf<_i12.WorkEntity?>()) &&
        (targetTypeOf == _typeOf<_i9.Work>() ||
            targetTypeOf == _typeOf<_i9.Work?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i12$WorkEntity_To__i9$Work((model as _i12.WorkEntity?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i13.InfoEntity>() ||
            sourceTypeOf == _typeOf<_i13.InfoEntity?>()) &&
        (targetTypeOf == _typeOf<_i9.Info>() ||
            targetTypeOf == _typeOf<_i9.Info?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i13$InfoEntity_To__i9$Info((model as _i13.InfoEntity?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.ConversationEntity>() ||
            sourceTypeOf == _typeOf<_i15.ConversationEntity?>()) &&
        (targetTypeOf == _typeOf<_i14.Conversation>() ||
            targetTypeOf == _typeOf<_i14.Conversation?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$ConversationEntity_To__i14$Conversation(
          (model as _i15.ConversationEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i19.OrganizationRoleEntity>() ||
            sourceTypeOf == _typeOf<_i19.OrganizationRoleEntity?>()) &&
        (targetTypeOf == _typeOf<_i18.OrganizationRole>() ||
            targetTypeOf == _typeOf<_i18.OrganizationRole?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i19$OrganizationRoleEntity_To__i18$OrganizationRole(
          (model as _i19.OrganizationRoleEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i17.OrganizationDepartmentEntity>() ||
            sourceTypeOf == _typeOf<_i17.OrganizationDepartmentEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.OrganizationDepartment>() ||
            targetTypeOf == _typeOf<_i16.OrganizationDepartment?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i17$OrganizationDepartmentEntity_To__i16$OrganizationDepartment(
          (model as _i17.OrganizationDepartmentEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i21.ChatBotEntity>() ||
            sourceTypeOf == _typeOf<_i21.ChatBotEntity?>()) &&
        (targetTypeOf == _typeOf<_i20.ChatBotModel>() ||
            targetTypeOf == _typeOf<_i20.ChatBotModel?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i21$ChatBotEntity_To__i20$ChatBotModel(
          (model as _i21.ChatBotEntity?)) as TARGET);
    }
    throw Exception('No ${model.runtimeType} -> $targetTypeOf mapping.');
  }

  TARGET? _safeConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (!useSafeMapping<SOURCE, TARGET>()) {
      return _convert(
        model,
        canReturnNull: true,
      );
    }
    try {
      return _convert(
        model,
        canReturnNull: true,
      );
    } catch (e, s) {
      onMappingError?.call(e, s, model);
      return null;
    }
  }

  
  
  ///
  /// Used delegates: [_i1.AssigneeEntityMapper]
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return false;
  }

  _i4.WalletInfoEntity _map__i3$WalletInfoResponse_To__i4$WalletInfoEntity(
      _i3.WalletInfoResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WalletInfoResponse → WalletInfoEntity failed because WalletInfoResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WalletInfoResponse, WalletInfoEntity> to handle null values during mapping.');
    }
    return _i4.WalletInfoEntity(
      walletBalance: model.walletBalance,
      userId: model.userId,
      workspaceId: model.workspaceId,
      walletCurrency: model.walletCurrency,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i6.CoinTransactionEntity
      _map__i5$CoinTransactionResponse_To__i6$CoinTransactionEntity(
          _i5.CoinTransactionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping CoinTransactionResponse → CoinTransactionEntity failed because CoinTransactionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<CoinTransactionResponse, CoinTransactionEntity> to handle null values during mapping.');
    }
    return _i6.CoinTransactionEntity(
      transactionId: model.transactionId,
      transactionType: model.transactionType,
      transactionStatus: model.transactionStatus,
      contextType: model.contextType,
      walletBalanceChanged: model.walletBalanceChanged,
      contextName: model.contextName,
      fromUserId: model.fromUserId,
      toUserId: model.toUserId,
      walletCurrency: model.walletCurrency,
      createdAt: model.createdAt,
      message: model.message,
      fromUserInfo:
          _map__i9$Assignee_To__i11$AssigneeEntity_Nullable(model.fromUserInfo),
      toUserInfo:
          _map__i9$Assignee_To__i11$AssigneeEntity_Nullable(model.toUserInfo),
      contextId: model.contextId,
      exchangeInfo: _i24.CoinEntityMapper.mapExchangeInfo(model),
    );
  }

  _i8.CoinQrCodeEntity _map__i7$CoinQrCodeStateResponse_To__i8$CoinQrCodeEntity(
      _i7.CoinQrCodeStateResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping CoinQrCodeStateResponse → CoinQrCodeEntity failed because CoinQrCodeStateResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<CoinQrCodeStateResponse, CoinQrCodeEntity> to handle null values during mapping.');
    }
    return _i8.CoinQrCodeEntity()
      ..id = model.id
      ..point = model.point
      ..contextType = model.contextType
      ..ownerBy = model.ownerBy
      ..createdAt = model.createdAt
      ..updatedAt = model.updatedAt;
  }

  _i10.GPUserEntity _map__i9$Assignee_To__i10$GPUserEntity(
      _i9.Assignee? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Assignee → GPUserEntity failed because Assignee was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Assignee, GPUserEntity> to handle null values during mapping.');
    }
    return _i10.GPUserEntity(
      id: _i1.AssigneeEntityMapper.mapIntIdToString(model),
      name: model.displayName,
      avatar: model.avatar,
      displayName: model.displayName,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i10.GPUserEntity _map__i11$AssigneeEntity_To__i10$GPUserEntity(
      _i11.AssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AssigneeEntity → GPUserEntity failed because AssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AssigneeEntity, GPUserEntity> to handle null values during mapping.');
    }
    return _i10.GPUserEntity(
      id: _i1.AssigneeEntityMapper.mapAssigneeEntityId(model),
      avatar: model.avatar,
      displayName: model.displayName,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i11.AssigneeEntity _map__i10$GPUserEntity_To__i11$AssigneeEntity(
      _i10.GPUserEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GPUserEntity → AssigneeEntity failed because GPUserEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GPUserEntity, AssigneeEntity> to handle null values during mapping.');
    }
    return _i11.AssigneeEntity(
      id: _i1.AssigneeEntityMapper.mapGPUserEntityId(model),
      displayName: _i1.AssigneeEntityMapper.mapGPUserEntityDisplayName(model),
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i11.AssigneeEntity _map__i9$Assignee_To__i11$AssigneeEntity(
      _i9.Assignee? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Assignee → AssigneeEntity failed because Assignee was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Assignee, AssigneeEntity> to handle null values during mapping.');
    }
    return _i11.AssigneeEntity(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i9$Info_To__i13$InfoEntity_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i12.WorkEntity _map__i9$Work_To__i12$WorkEntity(_i9.Work? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Work → WorkEntity failed because Work was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Work, WorkEntity> to handle null values during mapping.');
    }
    return _i12.WorkEntity(
      company: model.company,
      department: model.department,
      title: model.title,
      departmentId: model.departmentId,
      departments: model.departments,
      departmentIds: model.departmentIds,
      roleId: model.roleId,
      privacy: model.privacy,
    );
  }

  _i13.InfoEntity _map__i9$Info_To__i13$InfoEntity(_i9.Info? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Info → InfoEntity failed because Info was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Info, InfoEntity> to handle null values during mapping.');
    }
    return _i13.InfoEntity(
        work: model.work
            ?.map<_i12.WorkEntity>(
                (value) => _map__i9$Work_To__i12$WorkEntity(value))
            .toList());
  }

  _i15.ConversationEntity _map__i14$Conversation_To__i15$ConversationEntity(
      _i14.Conversation? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Conversation → ConversationEntity failed because Conversation was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Conversation, ConversationEntity> to handle null values during mapping.');
    }
    return _i15.ConversationEntity(
      id: model.id,
      name: model.name,
      role: model.role,
      avatar: model.avatar,
      folder: model.folder,
      type: model.type,
      groupLevel: model.groupLevel,
      messageCount: model.messageCount,
      memberCount: model.memberCount,
    );
  }

  _i17.OrganizationDepartmentEntity
      _map__i16$OrganizationDepartment_To__i17$OrganizationDepartmentEntity(
          _i16.OrganizationDepartment? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationDepartment → OrganizationDepartmentEntity failed because OrganizationDepartment was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationDepartment, OrganizationDepartmentEntity> to handle null values during mapping.');
    }
    return _i17.OrganizationDepartmentEntity(
      id: model.id,
      name: model.name,
      children: model.children
          .map<_i17.OrganizationDepartmentEntity>((value) =>
              _map__i16$OrganizationDepartment_To__i17$OrganizationDepartmentEntity(
                  value))
          .toList(),
      groupId: model.groupId,
      treeId: model.treeId,
      threadId: model.threadId,
      isPrimary: model.isPrimary,
    );
  }

  _i19.OrganizationRoleEntity
      _map__i18$OrganizationRole_To__i19$OrganizationRoleEntity(
          _i18.OrganizationRole? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationRole → OrganizationRoleEntity failed because OrganizationRole was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationRole, OrganizationRoleEntity> to handle null values during mapping.');
    }
    return _i19.OrganizationRoleEntity(
      name: model.name,
      workspaceId: model.workspaceId,
      id: model.id,
    );
  }

  _i21.ChatBotEntity _map__i20$ChatBotModel_To__i21$ChatBotEntity(
      _i20.ChatBotModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ChatBotModel → ChatBotEntity failed because ChatBotModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ChatBotModel, ChatBotEntity> to handle null values during mapping.');
    }
    return _i21.ChatBotEntity(
      id: model.id,
      name: model.name,
      avatar: model.avatar,
      description: model.description,
      workspaceId: model.workspaceId,
      creatorId: model.creatorId,
      createdAt: model.createdAt,
      botCreatorType: model.botCreatorType,
    );
  }

  _i23.SelectMemberEntity
      _map__i22$SelectInviteesOptions_To__i23$SelectMemberEntity(
          _i22.SelectInviteesOptions? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping SelectInviteesOptions → SelectMemberEntity failed because SelectInviteesOptions was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<SelectInviteesOptions, SelectMemberEntity> to handle null values during mapping.');
    }
    return _i23.SelectMemberEntity(
      assigneeEntities: model.selectedMembers
          ?.map<_i11.AssigneeEntity>(
              (value) => _map__i9$Assignee_To__i11$AssigneeEntity(value))
          .toList(),
      conversationEntities: model.selectedThreads
          ?.map<_i15.ConversationEntity>((value) =>
              _map__i14$Conversation_To__i15$ConversationEntity(value))
          .toList(),
      departmentEntities: model.selectedDepartments
          ?.map<_i17.OrganizationDepartmentEntity>((value) =>
              _map__i16$OrganizationDepartment_To__i17$OrganizationDepartmentEntity(
                  value))
          .toList(),
      roleEntities: model.selectedRoles
          ?.map<_i19.OrganizationRoleEntity>((value) =>
              _map__i18$OrganizationRole_To__i19$OrganizationRoleEntity(value))
          .toList(),
      chatBotEntities: model.selectedBots
          ?.map<_i21.ChatBotEntity>(
              (value) => _map__i20$ChatBotModel_To__i21$ChatBotEntity(value))
          .toList(),
    );
  }

  _i9.Assignee _map__i11$AssigneeEntity_To__i9$Assignee(
      _i11.AssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AssigneeEntity → Assignee failed because AssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AssigneeEntity, Assignee> to handle null values during mapping.');
    }
    return _i9.Assignee(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i13$InfoEntity_To__i9$Info_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i9.Work _map__i12$WorkEntity_To__i9$Work(_i12.WorkEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkEntity → Work failed because WorkEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkEntity, Work> to handle null values during mapping.');
    }
    return _i9.Work(
      company: model.company,
      department: model.department,
      title: model.title,
      departmentId: model.departmentId,
      departments: model.departments,
      departmentIds: model.departmentIds,
      roleId: model.roleId,
      privacy: model.privacy,
    );
  }

  _i9.Info _map__i13$InfoEntity_To__i9$Info(_i13.InfoEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping InfoEntity → Info failed because InfoEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<InfoEntity, Info> to handle null values during mapping.');
    }
    return _i9.Info(
        work: model.work
            ?.map<_i9.Work>((value) => _map__i12$WorkEntity_To__i9$Work(value))
            .toList());
  }

  _i14.Conversation _map__i15$ConversationEntity_To__i14$Conversation(
      _i15.ConversationEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ConversationEntity → Conversation failed because ConversationEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ConversationEntity, Conversation> to handle null values during mapping.');
    }
    return _i14.Conversation(
      name: model.name,
      role: model.role,
      avatar: model.avatar,
      folder: model.folder,
      type: model.type,
      id: model.id,
      groupLevel: model.groupLevel,
      memberCount: model.memberCount,
    )..messageCount = model.messageCount;
  }

  _i18.OrganizationRole
      _map__i19$OrganizationRoleEntity_To__i18$OrganizationRole(
          _i19.OrganizationRoleEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationRoleEntity → OrganizationRole failed because OrganizationRoleEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationRoleEntity, OrganizationRole> to handle null values during mapping.');
    }
    return _i18.OrganizationRole(
      name: model.name,
      workspaceId: model.workspaceId,
      id: model.id,
    );
  }

  _i16.OrganizationDepartment
      _map__i17$OrganizationDepartmentEntity_To__i16$OrganizationDepartment(
          _i17.OrganizationDepartmentEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationDepartmentEntity → OrganizationDepartment failed because OrganizationDepartmentEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationDepartmentEntity, OrganizationDepartment> to handle null values during mapping.');
    }
    return _i16.OrganizationDepartment(
      id: model.id,
      name: model.name,
      children: model.children
          .map<_i16.OrganizationDepartment>((value) =>
              _map__i17$OrganizationDepartmentEntity_To__i16$OrganizationDepartment(
                  value))
          .toList(),
      isPrimary: model.isPrimary,
      threadId: model.threadId,
      treeId: model.treeId,
    )..groupId = model.groupId;
  }

  _i20.ChatBotModel _map__i21$ChatBotEntity_To__i20$ChatBotModel(
      _i21.ChatBotEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ChatBotEntity → ChatBotModel failed because ChatBotEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ChatBotEntity, ChatBotModel> to handle null values during mapping.');
    }
    return _i20.ChatBotModel(
      name: _i1.AssigneeEntityMapper.mapChatBotEntityName(model),
      id: _i1.AssigneeEntityMapper.mapChatBotEntityId(model),
      avatar: model.avatar,
      description: model.description,
      workspaceId: model.workspaceId,
      creatorId: model.creatorId,
      createdAt: model.createdAt,
      botCreatorType: model.botCreatorType,
    );
  }

  _i11.AssigneeEntity? _map__i9$Assignee_To__i11$AssigneeEntity_Nullable(
      _i9.Assignee? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i11.AssigneeEntity(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i9$Info_To__i13$InfoEntity_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i13.InfoEntity? _map__i9$Info_To__i13$InfoEntity_Nullable(_i9.Info? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i13.InfoEntity(
        work: model.work
            ?.map<_i12.WorkEntity>(
                (value) => _map__i9$Work_To__i12$WorkEntity(value))
            .toList());
  }

  _i9.Info? _map__i13$InfoEntity_To__i9$Info_Nullable(_i13.InfoEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i9.Info(
        work: model.work
            ?.map<_i9.Work>((value) => _map__i12$WorkEntity_To__i9$Work(value))
            .toList());
  }
}
