// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'workflow_detail_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkFlowDetailParams {
  String get workflowId;
  @JsonKey(name: 'with_start_node')
  bool get withStartNode;

  /// Create a copy of WorkFlowDetailParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkFlowDetailParamsCopyWith<WorkFlowDetailParams> get copyWith =>
      _$WorkFlowDetailParamsCopyWithImpl<WorkFlowDetailParams>(
          this as WorkFlowDetailParams, _$identity);

  /// Serializes this WorkFlowDetailParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkFlowDetailParams &&
            (identical(other.workflowId, workflowId) ||
                other.workflowId == workflowId) &&
            (identical(other.withStartNode, withStartNode) ||
                other.withStartNode == withStartNode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, workflowId, withStartNode);

  @override
  String toString() {
    return 'WorkFlowDetailParams(workflowId: $workflowId, withStartNode: $withStartNode)';
  }
}

/// @nodoc
abstract mixin class $WorkFlowDetailParamsCopyWith<$Res> {
  factory $WorkFlowDetailParamsCopyWith(WorkFlowDetailParams value,
          $Res Function(WorkFlowDetailParams) _then) =
      _$WorkFlowDetailParamsCopyWithImpl;
  @useResult
  $Res call(
      {String workflowId,
      @JsonKey(name: 'with_start_node') bool withStartNode});
}

/// @nodoc
class _$WorkFlowDetailParamsCopyWithImpl<$Res>
    implements $WorkFlowDetailParamsCopyWith<$Res> {
  _$WorkFlowDetailParamsCopyWithImpl(this._self, this._then);

  final WorkFlowDetailParams _self;
  final $Res Function(WorkFlowDetailParams) _then;

  /// Create a copy of WorkFlowDetailParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workflowId = null,
    Object? withStartNode = null,
  }) {
    return _then(_self.copyWith(
      workflowId: null == workflowId
          ? _self.workflowId
          : workflowId // ignore: cast_nullable_to_non_nullable
              as String,
      withStartNode: null == withStartNode
          ? _self.withStartNode
          : withStartNode // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _WorkFlowDetailParams implements WorkFlowDetailParams {
  _WorkFlowDetailParams(
      {required this.workflowId,
      @JsonKey(name: 'with_start_node') this.withStartNode = true});

  @override
  final String workflowId;
  @override
  @JsonKey(name: 'with_start_node')
  final bool withStartNode;

  /// Create a copy of WorkFlowDetailParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkFlowDetailParamsCopyWith<_WorkFlowDetailParams> get copyWith =>
      __$WorkFlowDetailParamsCopyWithImpl<_WorkFlowDetailParams>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkFlowDetailParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkFlowDetailParams &&
            (identical(other.workflowId, workflowId) ||
                other.workflowId == workflowId) &&
            (identical(other.withStartNode, withStartNode) ||
                other.withStartNode == withStartNode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, workflowId, withStartNode);

  @override
  String toString() {
    return 'WorkFlowDetailParams(workflowId: $workflowId, withStartNode: $withStartNode)';
  }
}

/// @nodoc
abstract mixin class _$WorkFlowDetailParamsCopyWith<$Res>
    implements $WorkFlowDetailParamsCopyWith<$Res> {
  factory _$WorkFlowDetailParamsCopyWith(_WorkFlowDetailParams value,
          $Res Function(_WorkFlowDetailParams) _then) =
      __$WorkFlowDetailParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String workflowId,
      @JsonKey(name: 'with_start_node') bool withStartNode});
}

/// @nodoc
class __$WorkFlowDetailParamsCopyWithImpl<$Res>
    implements _$WorkFlowDetailParamsCopyWith<$Res> {
  __$WorkFlowDetailParamsCopyWithImpl(this._self, this._then);

  final _WorkFlowDetailParams _self;
  final $Res Function(_WorkFlowDetailParams) _then;

  /// Create a copy of WorkFlowDetailParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? workflowId = null,
    Object? withStartNode = null,
  }) {
    return _then(_WorkFlowDetailParams(
      workflowId: null == workflowId
          ? _self.workflowId
          : workflowId // ignore: cast_nullable_to_non_nullable
              as String,
      withStartNode: null == withStartNode
          ? _self.withStartNode
          : withStartNode // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
