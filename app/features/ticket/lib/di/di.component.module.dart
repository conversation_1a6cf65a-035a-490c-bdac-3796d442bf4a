//@GeneratedMicroModule;GpFeatTicketPackageModule;package:gp_feat_ticket/di/di.component.module.dart
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i687;

import 'package:dio/dio.dart' as _i361;
import 'package:flutter/material.dart' as _i409;
import 'package:gp_feat_ticket/app/bloc/app_bloc.dart' as _i210;
import 'package:gp_feat_ticket/data/data.dart' as _i165;
import 'package:gp_feat_ticket/data/data_source/remote/ticket/ticket.service.dart'
    as _i116;
import 'package:gp_feat_ticket/data/data_source/remote/workflow/workflow.service.dart'
    as _i485;
import 'package:gp_feat_ticket/data/repository/ticket_repo_impl.dart' as _i492;
import 'package:gp_feat_ticket/data/repository/workflow_repo_impl.dart'
    as _i131;
import 'package:gp_feat_ticket/di/modules/navigator.module.dart' as _i286;
import 'package:gp_feat_ticket/di/modules/url.module.dart' as _i814;
import 'package:gp_feat_ticket/domain/domain.dart' as _i1041;
import 'package:gp_feat_ticket/domain/repository/ticket_repo.dart' as _i241;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_accept_onhold.usecase.dart'
    as _i787;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_activity_detail.usecase.dart'
    as _i300;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_add_follower.usecase.dart'
    as _i16;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_additional_requests.usecase.dart'
    as _i239;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_cancel.usecase.dart'
    as _i812;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_cancel_onhold.usecase.dart'
    as _i566;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_close.usecase.dart'
    as _i848;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_create.usecase.dart'
    as _i712;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_delete.usecase.dart'
    as _i1024;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_delete_comment.usecase.dart'
    as _i12;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_details.usecase.dart'
    as _i7;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_edit_comment.usecase.dart'
    as _i518;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_flowchart.usecase.dart'
    as _i885;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_follow_members.usecase.dart'
    as _i757;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_get_activities.usecase.dart'
    as _i916;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_get_additional_requests.usecase.dart'
    as _i961;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_get_can_redo_end_node.usecase.dart'
    as _i662;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_get_can_redo_nodes.usecase.dart'
    as _i70;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_get_comment.usecase.dart'
    as _i605;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_get_onhold.usecase.dart'
    as _i517;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_is_assignee.usecase.dart'
    as _i355;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_label.usecase.dart'
    as _i153;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_list.usecase.dart'
    as _i1035;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_members.usecase.dart'
    as _i914;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_move_to_on_hold.usecase.dart'
    as _i756;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_node.usecase.dart'
    as _i6;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_post_comment.usecase.dart'
    as _i676;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_redo_end_node.usecase.dart'
    as _i975;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_redo_previous.usecase.dart'
    as _i200;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_reject_onhold.usecase.dart'
    as _i87;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_relative.usecase.dart'
    as _i1019;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_reopen.usecase.dart'
    as _i715;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_response_additional_request.usecase.dart'
    as _i964;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_review.usecase.dart'
    as _i548;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_spam.usecase.dart'
    as _i199;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_unfollow.usecase.dart'
    as _i688;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_update_assignee.usecase.dart'
    as _i673;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_update_fields.usecase.dart'
    as _i797;
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_update_node_status.usecase.dart'
    as _i718;
import 'package:gp_feat_ticket/domain/usecase/workflow/admin_workflow_get.usecase.dart'
    as _i971;
import 'package:gp_feat_ticket/domain/usecase/workflow/admin_workflow_get_detail.usecase.dart'
    as _i288;
import 'package:gp_feat_ticket/domain/usecase/workflow/workflow_get.usecase.dart'
    as _i376;
import 'package:gp_feat_ticket/domain/usecase/workflow/workflow_group_get.usecase.dart'
    as _i66;
import 'package:gp_feat_ticket/domain/usecase/workflow/workflow_tag_list.usecase.dart'
    as _i341;
import 'package:gp_feat_ticket/domain/usecase/workflow/workflow_tag_list_search.usecase.dart'
    as _i140;
import 'package:gp_feat_ticket/mapper/ticket_mapper.dart' as _i532;
import 'package:gp_feat_ticket/route/ticket_navigator.dart' as _i545;
import 'package:gp_shared/data/data_source/remote/comment/comment.service.dart'
    as _i765;
import 'package:injectable/injectable.dart' as _i526;

class GpFeatTicketPackageModule extends _i526.MicroPackageModule {
// initializes the registration of main-scope dependencies inside of GetIt
  @override
  _i687.FutureOr<void> init(_i526.GetItHelper gh) {
    final navigatorModule = _$NavigatorModule();
    final urlModule = _$UrlModule();
    gh.lazySingleton<_i210.AppBloc>(() => _i210.AppBloc());
    gh.lazySingleton<_i545.TicketNavigator>(() => _i545.TicketNavigator());
    gh.singleton<_i409.GlobalKey<_i409.ScaffoldState>>(
      () => navigatorModule.kRootScaffoldKey,
      instanceName: 'kTicketRootScaffoldKey',
    );
    gh.singleton<String>(
      () => urlModule.kTicketUrl,
      instanceName: 'kTicketUrl',
    );
    gh.singleton<String>(
      () => urlModule.kTicketWorkFlowUrl,
      instanceName: 'kWorkFlowUrl',
    );
    gh.singleton<_i409.GlobalKey<_i409.ScaffoldMessengerState>>(
      () => navigatorModule.kRootScaffoldMessengerKey,
      instanceName: 'kTicketScaffoldMessengerState',
    );
    gh.singleton<_i409.GlobalKey<_i409.NavigatorState>>(
      () => navigatorModule.kNavigatorKey,
      instanceName: 'kTicketNavigatorKey',
    );
    gh.lazySingleton<_i116.TicketService>(
      () => _i116.TicketService(
        gh<_i361.Dio>(instanceName: 'kDio'),
        baseUrl: gh<String>(instanceName: 'kTicketUrl'),
      ),
      instanceName: 'kTicketService',
    );
    gh.lazySingleton<_i485.WorkFlowService>(
      () => _i485.WorkFlowService(
        gh<_i361.Dio>(instanceName: 'kDio'),
        baseUrl: gh<String>(instanceName: 'kWorkFlowUrl'),
      ),
      instanceName: 'kWorkFlowService',
    );
    gh.lazySingleton<_i1041.TicketRepository>(
      () => _i492.TicketRepositoryImpl(
        gh<_i165.TicketService>(instanceName: 'kTicketService'),
        gh<_i765.CommentService>(instanceName: 'kCommentService'),
      ),
      instanceName: 'kTicketRepository',
    );
    gh.lazySingleton<_i1041.WorkFlowRepository>(
      () => _i131.WorkFlowRepositoryImpl(
          gh<_i165.WorkFlowService>(instanceName: 'kWorkFlowService')),
      instanceName: 'kWorkFlowRepository',
    );
    gh.singleton<_i532.GPTicketMapper>(
      () => const _i532.GPTicketMapper(),
      instanceName: 'kGPTicketMapper',
    );
    gh.factory<_i518.TicketEditCommentUseCase>(() =>
        _i518.TicketEditCommentUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i1035.TicketListUseCase>(() => _i1035.TicketListUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i7.TicketDetailsUseCase>(() => _i7.TicketDetailsUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i712.TicketCreateUseCase>(() => _i712.TicketCreateUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i1019.TicketRelativeUseCase>(() => _i1019.TicketRelativeUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i975.TicketRedoEndNodeUseCase>(() =>
        _i975.TicketRedoEndNodeUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i12.TicketDeleteCommentUseCase>(() =>
        _i12.TicketDeleteCommentUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i885.TicketFlowChartUseCase>(() => _i885.TicketFlowChartUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i70.TicketGetCanRedoNodesUseCase>(() =>
        _i70.TicketGetCanRedoNodesUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i605.TicketCommentUseCase>(() => _i605.TicketCommentUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i548.TicketReviewUseCase>(() => _i548.TicketReviewUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i916.TicketGetActivitiesUseCase>(() =>
        _i916.TicketGetActivitiesUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i756.TicketMoveToOnHoldUseCase>(() =>
        _i756.TicketMoveToOnHoldUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i87.TicketRejectOnHoldUseCase>(() =>
        _i87.TicketRejectOnHoldUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i964.TicketResponseAdditionalRequestsUseCase>(() =>
        _i964.TicketResponseAdditionalRequestsUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i914.MembersUseCase>(() => _i914.MembersUseCase(
        gh<_i241.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i239.TicketAdditionalRequestsUseCase>(() =>
        _i239.TicketAdditionalRequestsUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i662.TicketGetCanRedoEndNodeUseCase>(() =>
        _i662.TicketGetCanRedoEndNodeUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i715.TicketReopenUseCase>(() => _i715.TicketReopenUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i199.TicketSpamUseCase>(() => _i199.TicketSpamUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i1024.TicketDeleteUseCase>(() => _i1024.TicketDeleteUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i673.TicketUpdateAsigneeUseCase>(() =>
        _i673.TicketUpdateAsigneeUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i200.TicketRedoPreviousUseCase>(() =>
        _i200.TicketRedoPreviousUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i848.TicketCloseUseCase>(() => _i848.TicketCloseUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i153.TicketLabelUseCase>(() => _i153.TicketLabelUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i355.CheckIsTicketAssigneeUseCase>(() =>
        _i355.CheckIsTicketAssigneeUseCase(
            gh<_i241.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i300.TicketActivityDetailUseCase>(() =>
        _i300.TicketActivityDetailUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i300.TicketActivitySpamDetailUseCase>(() =>
        _i300.TicketActivitySpamDetailUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i6.TicketNodeUseCase>(() => _i6.TicketNodeUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i961.TicketGetAdditionalRequestsUseCase>(() =>
        _i961.TicketGetAdditionalRequestsUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i812.TicketCancelUseCase>(() => _i812.TicketCancelUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i787.TicketAcceptOnHoldUseCase>(() =>
        _i787.TicketAcceptOnHoldUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i16.TicketAddFollowerUseCase>(() =>
        _i16.TicketAddFollowerUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i16.TicketAddFollowerAllStepUseCase>(() =>
        _i16.TicketAddFollowerAllStepUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i566.TicketCancelOnHoldUseCase>(() =>
        _i566.TicketCancelOnHoldUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i517.TicketGetOnHoldUseCase>(() => _i517.TicketGetOnHoldUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i676.TicketPostCommentUseCase>(() =>
        _i676.TicketPostCommentUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i797.TicketUpdateFieldsUseCase>(() =>
        _i797.TicketUpdateFieldsUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i688.TicketUnfollowUseCase>(() => _i688.TicketUnfollowUseCase(
        gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i718.TicketUpdateNodeStatusUseCase>(() =>
        _i718.TicketUpdateNodeStatusUseCase(
            gh<_i1041.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i757.TicketFollowMembersUseCase>(() =>
        _i757.TicketFollowMembersUseCase(
            gh<_i241.TicketRepository>(instanceName: 'kTicketRepository')));
    gh.factory<_i288.AdminWorkFlowDetailGetUseCase>(() =>
        _i288.AdminWorkFlowDetailGetUseCase(gh<_i1041.WorkFlowRepository>(
            instanceName: 'kWorkFlowRepository')));
    gh.factory<_i140.WorkflowSearchTagListUseCase>(() =>
        _i140.WorkflowSearchTagListUseCase(gh<_i1041.WorkFlowRepository>(
            instanceName: 'kWorkFlowRepository')));
    gh.factory<_i66.WorkFlowGroupGetUseCase>(() => _i66.WorkFlowGroupGetUseCase(
        gh<_i1041.WorkFlowRepository>(instanceName: 'kWorkFlowRepository')));
    gh.factory<_i376.WorkFlowGetUseCase>(() => _i376.WorkFlowGetUseCase(
        gh<_i1041.WorkFlowRepository>(instanceName: 'kWorkFlowRepository')));
    gh.factory<_i341.WorkflowTagListUseCase>(() => _i341.WorkflowTagListUseCase(
        gh<_i1041.WorkFlowRepository>(instanceName: 'kWorkFlowRepository')));
    gh.factory<_i971.AdminWorkFlowGetUseCase>(() =>
        _i971.AdminWorkFlowGetUseCase(gh<_i1041.WorkFlowRepository>(
            instanceName: 'kWorkFlowRepository')));
  }
}

class _$NavigatorModule extends _i286.NavigatorModule {}

class _$UrlModule extends _i814.UrlModule {}
