/*
 * Created Date: 1/02/2024 11:29:52
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 7th March 2024 14:20:09
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core_v2/base/base.dart';

import '../../../../domain/entity/base/base_list.entity.dart';
import '../bloc.dart';

const _defaultPage = 1;

mixin BaseListBehaviorMixin<T, Response, SearchParams>
    implements BaseListBehavior<T, Response, SearchParams> {
  @override
  Future<Response?>? usecaseOnSearchData({
    required SearchParams params,
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
    int? page,
  }) async {
    throw UnimplementedError('usecaseOnSearch');
  }
}

/// Các behavior của list
abstract class BaseListBehavior<T, Response, SearchParams> {
  /// usecase when user load data, refresh data, loadMore data
  Future<Response?>? usecaseOnLoadData({
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
    int? page,
  });

  /// usecase when user search
  Future<Response?>? usecaseOnSearchData({
    required SearchParams params,
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
    int? page,
  });

  /// emit [BaseListDataLoaded] khi có data
  /// extends for each case is needed
  Future<BaseListDataLoaded<T>> emitData({
    Response? response,
    List<T>? entityData,
    required Emitter<BaseListState> emit,
    required bool isInitialLoad,
  });
}

/// Quản lý các action của list
abstract class GPBaseListBlocV2<T, Response, SearchParams>
    extends CoreV2BaseBloc<CoreV2BaseEvent, BaseListState> {
  GPBaseListBlocV2() : super(const BaseListState()) {
    on<BaseListEvent>(_onBaseListEvent);

    on<BaseListUpdateDataEvent<T>>(_onBaseListUpdateDataEvent);

    on<BaseListChangeModeEvent>(_onBaseListChangeModeEvent);

    on<BaseListItemOnCheckedChangedEvent>(_onBaseListItemOnCheckedChangedEvent);
  }

  // /// emit inheritance of `BaseListDataLoaded`
  // S stateListDataLoaded<S extends BaseListDataLoaded>(
  //   BaseListDataLoaded<T> loadedDataStated,
  // );

  BaseListBehavior<T, Response, SearchParams>? _behavior;

  BaseListDataLoaded? _firstPageDataLoadedState;

  dynamic defaultInputData;

  String? _nextLink = '';
  int? page = _defaultPage;

  bool isLoading = false;

  int get defaultPage => 1;

  final List<T> _loadedEntities = [];
  final Map<int, T> _selectedEntities = {};

  void setOnUseCaseBehavior(
      BaseListBehavior<T, Response, SearchParams> behavior) {
    this._behavior = behavior;
  }

  void initInputData(dynamic inputData) {
    defaultInputData = inputData;
  }

  void loadData({
    bool isInitialLoad = true,
    dynamic inputData,
  }) {
    add(
      BaseListEvent(
        isInitialLoad: isInitialLoad,
        nextLink: isInitialLoad ? '' : _nextLink,
        page: isInitialLoad ? _defaultPage : page,
        inputData: inputData ?? defaultInputData,
      ),
    );
  }

  void searchData({
    required SearchParams searchParams,
    bool isInitialLoad = true,
    dynamic inputData,
  }) {
    add(
      BaseListEvent(
        isInitialLoad: isInitialLoad,
        nextLink: isInitialLoad ? '' : _nextLink,
        page: isInitialLoad ? _defaultPage : page,
        searchParams: searchParams,
        inputData: inputData ?? defaultInputData,
      ),
    );
  }

  FutureOr _onBaseListEvent(
    BaseListEvent event,
    Emitter<BaseListState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      doOnError: (error, stackTrace) async {
        logE('_onBaseListEvent error $error');
        emit(
          BaseListHasError(
            errror: error,
          ),
        );

        onError(error, stackTrace);
      },
      action: () async {
        assert(_behavior != null, "BaseListBehavior is not setup");
        if (isLoading && !event.isInitialLoad) return;

        final bool isRefreshData = event.isInitialLoad;

        if (isRefreshData) {
          emit(BaseListDataLoading());
        }

        final List<Response> response = [];

        final bool isSearch = event.searchParams != null &&
            (event.searchParams!.search != null ||
                event.searchParams!.searchByCode != null);
        ;

        if (isSearch) {
          // searchString is empty -> return state ở page 1

          // TODO: generic later
          String searchStr = '';
          try {
            searchStr = event.searchParams!.search;
          } catch (e) {}
          try {
            searchStr = event.searchParams!.searchByCode;
          } catch (e) {}
          if (_searchChecker(searchStr)) {
            if (_firstPageDataLoadedState != null) {
              emit(_firstPageDataLoadedState!);
            }
            return;
          }
          isLoading = true;
          Response? result = await _behavior!.usecaseOnSearchData(
            params: event.searchParams!,
            isRefreshData: isRefreshData,
            inputData: event.inputData,
            nextLink: isRefreshData ? '' : _nextLink,
            page: isRefreshData ? _defaultPage : page,
          );
          isLoading = false;
          assert(result != null);

          if (result != null) {
            response.clear();
            response.add(result);
          }
        } else {
          response.clear();

          isLoading = true;
          final result = await _behavior!.usecaseOnLoadData(
            isRefreshData: isRefreshData,
            inputData: event.inputData,
            nextLink: isRefreshData ? '' : _nextLink,
            page: isRefreshData ? _defaultPage : page,
          );
          isLoading = false;

          if (result != null) {
            response.add(result);
          } else {
            // final baseListDataLoaded = BaseListDataLoaded<T>(
            //   isInitialLoad: isRefreshData,
            //   data: null,
            //   canNextPage: false,
            //   nextLink: null,
            // );

            // emit(baseListDataLoaded);

            await _behavior!.emitData(
              response: null,
              entityData: null,
              emit: emit,
              isInitialLoad: isRefreshData,
            );
          }
        }

        if (response.isEmpty) {
          return;
        }

        final state = await _behavior!.emitData(
          response: response.first,
          emit: emit,
          isInitialLoad: isRefreshData,
        );

        _nextLink = state.nextLink;
        page = (event.page ?? _defaultPage) + 1;

        /* 
          Lưu lại state ở page đầu tiên,
          Khi search với String empty,
          emit lại state này, tránh phải load lại data 1 lần.
        */
        if (event.nextLink == null || event.page == _defaultPage) {
          _firstPageDataLoadedState = state;
        }

        emit(BaseListModeChanged(mode: ListViewMode.normal));

        emit(state);

        addLoadedData(
          isRefreshData,
          state.data ?? [],
        );
      },
    );
  }

  FutureOr _onBaseListUpdateDataEvent(
    BaseListUpdateDataEvent<T> event,
    Emitter<BaseListState> emit,
  ) async {
    if (state is BaseListDataLoaded<T>) {
      final currentState = state as BaseListDataLoaded;

      // final baseListDataLoaded = BaseListDataLoaded<T>(
      //   isInitialLoad: currentState.isInitialLoad,
      //   data: event.data,
      //   canNextPage: false,
      //   nextLink: null,
      // );

      // emit(stateListDataLoaded(baseListDataLoaded));

      await _behavior!.emitData(
        response: null,
        entityData: event.data,
        emit: emit,
        isInitialLoad: currentState.isInitialLoad,
      );
    } else {
      addLoadedData(
        true,
        event.data,
      );
    }
  }

  FutureOr _onBaseListChangeModeEvent(
    BaseListChangeModeEvent event,
    Emitter<BaseListState> emit,
  ) async {
    _selectedEntities.clear();

    if (event.mode == ListViewMode.normal) {
      _clearSelectLoadedData();
    }

    emit(BaseListModeChanged(mode: event.mode));
  }

  FutureOr _onBaseListItemOnCheckedChangedEvent(
    BaseListItemOnCheckedChangedEvent event,
    Emitter<BaseListState> emit,
  ) async {
    bool isChecked = event.isChecked;

    switch (event.changeCase) {
      case BaseListItemOnCheckedChangedCase.aCheckChanged:
        assert(event.entity != null);

        if (event.entity != null && event.position != null) {
          if (isChecked) {
            _selectedEntities[event.position!] = event.entity;
          } else {
            _selectedEntities.remove(event.position);
          }
        }
        final isSelectedAll =
            _selectedEntities.length == _loadedEntities.length;
        emit(
          BaseListItemOnCheckedChanged(
            entitiesByPosition: _selectedEntities,
            headerSelected: isSelectedAll
                ? true
                : _selectedEntities.isEmpty
                    ? false
                    : null,
          ),
        );
        break;
      case BaseListItemOnCheckedChangedCase.allCheckChanged:
        if (isChecked) {
          _selectedEntities.clear();

          for (int i = 0; i < _loadedEntities.length; i++) {
            final dynamic item = _loadedEntities[i];
            if (item is BaseListEntity) {
              item.isSelected.value = true;
            }
            _selectedEntities[i] = item;
          }
        } else {
          for (int i = 0; i < _loadedEntities.length; i++) {
            final dynamic item = _loadedEntities[i];
            if (item is BaseListEntity) {
              item.isSelected.value = false;
            }
          }

          _selectedEntities.clear();
        }

        emit(
          BaseListItemOnCheckedChanged(
            entitiesByPosition: _selectedEntities,
            headerSelected: isChecked,
          ),
        );
        break;
    }
  }

  bool _searchChecker(String search) {
    return search.isEmpty;
  }

  void addLoadedData(bool isInitialLoad, List<T> data) {
    if (isInitialLoad) {
      _loadedEntities.clear();
    }

    _loadedEntities.addAll(data);
  }

  void _clearSelectLoadedData() {
    if (_loadedEntities.isEmpty) return;

    if (_loadedEntities is List<BaseListEntity>) {
      for (var element in (_loadedEntities as List<BaseListEntity>)) {
        element.isSelected.value = false;
      }
    }
  }
}
