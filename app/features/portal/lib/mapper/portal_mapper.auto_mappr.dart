// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoMapprGenerator
// **************************************************************************

// ignore_for_file: type=lint, unnecessary_cast, unused_local_variable

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i1;

import '../data/model/response/mini_app_response.dart' as _i4;
import '../data/model/response/portal_list_response.dart' as _i2;
import '../domain/entity/mini_app/mini_app.entity.dart' as _i5;
import '../domain/entity/portal/portal_list.entity.dart' as _i3;
import 'entity/portal_entity_mapper.dart' as _i6;

/// Available mappings:
/// - `PortalListResponse` → `PortalListEntity`.
/// - `MiniAppResponse` → `MiniAppEntity`.
/// - `MiniAppCategoryResponse` → `MiniAppCategoryEntity`.
/// - `MiniAppMemberResponse` → `MiniAppMemberEntity`.
class $GPPortalMapper implements _i1.AutoMapprInterface {
  const $GPPortalMapper();

  Type _typeOf<T>() => T;

  List<_i1.AutoMapprInterface> get _delegates => const [];

  
  
  @override
  bool canConvert<SOURCE, TARGET>({bool recursive = true}) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.PortalListResponse>() ||
            sourceTypeOf == _typeOf<_i2.PortalListResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.PortalListEntity>() ||
            targetTypeOf == _typeOf<_i3.PortalListEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.MiniAppResponse>() ||
            sourceTypeOf == _typeOf<_i4.MiniAppResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.MiniAppEntity>() ||
            targetTypeOf == _typeOf<_i5.MiniAppEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.MiniAppCategoryResponse>() ||
            sourceTypeOf == _typeOf<_i4.MiniAppCategoryResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.MiniAppCategoryEntity>() ||
            targetTypeOf == _typeOf<_i5.MiniAppCategoryEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.MiniAppMemberResponse>() ||
            sourceTypeOf == _typeOf<_i4.MiniAppMemberResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.MiniAppMemberEntity>() ||
            targetTypeOf == _typeOf<_i5.MiniAppMemberEntity?>())) {
      return true;
    }
    if (recursive) {
      for (final mappr in _delegates) {
        if (mappr.canConvert<SOURCE, TARGET>()) {
          return true;
        }
      }
    }
    return false;
  }

  
  
  @override
  TARGET convert<SOURCE, TARGET>(SOURCE? model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _convert(model)!;
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convert(model)!;
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  TARGET? tryConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _safeConvert(
        model,
        onMappingError: onMappingError,
      );
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvert(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    return null;
  }

  
  
  @override
  Iterable<TARGET> convertIterable<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET>((item) => _convert(item)!);
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertIterable(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Iterable<TARGET?> tryConvertIterable<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET?>(
          (item) => _safeConvert(item, onMappingError: onMappingError));
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertIterable(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  List<TARGET> convertList<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertList(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  List<TARGET?> tryConvertList<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertList(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  Set<TARGET> convertSet<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertSet(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Set<TARGET?> tryConvertSet<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertSet(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  TARGET? _convert<SOURCE, TARGET>(
    SOURCE? model, {
    bool canReturnNull = false,
  }) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.PortalListResponse>() ||
            sourceTypeOf == _typeOf<_i2.PortalListResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.PortalListEntity>() ||
            targetTypeOf == _typeOf<_i3.PortalListEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$PortalListResponse_To__i3$PortalListEntity(
          (model as _i2.PortalListResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.MiniAppResponse>() ||
            sourceTypeOf == _typeOf<_i4.MiniAppResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.MiniAppEntity>() ||
            targetTypeOf == _typeOf<_i5.MiniAppEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$MiniAppResponse_To__i5$MiniAppEntity(
          (model as _i4.MiniAppResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.MiniAppCategoryResponse>() ||
            sourceTypeOf == _typeOf<_i4.MiniAppCategoryResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.MiniAppCategoryEntity>() ||
            targetTypeOf == _typeOf<_i5.MiniAppCategoryEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$MiniAppCategoryResponse_To__i5$MiniAppCategoryEntity(
          (model as _i4.MiniAppCategoryResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.MiniAppMemberResponse>() ||
            sourceTypeOf == _typeOf<_i4.MiniAppMemberResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.MiniAppMemberEntity>() ||
            targetTypeOf == _typeOf<_i5.MiniAppMemberEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$MiniAppMemberResponse_To__i5$MiniAppMemberEntity(
          (model as _i4.MiniAppMemberResponse?)) as TARGET);
    }
    throw Exception('No ${model.runtimeType} -> $targetTypeOf mapping.');
  }

  TARGET? _safeConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (!useSafeMapping<SOURCE, TARGET>()) {
      return _convert(
        model,
        canReturnNull: true,
      );
    }
    try {
      return _convert(
        model,
        canReturnNull: true,
      );
    } catch (e, s) {
      onMappingError?.call(e, s, model);
      return null;
    }
  }

  
  
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return false;
  }

  _i3.PortalListEntity _map__i2$PortalListResponse_To__i3$PortalListEntity(
      _i2.PortalListResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping PortalListResponse → PortalListEntity failed because PortalListResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<PortalListResponse, PortalListEntity> to handle null values during mapping.');
    }
    return _i3.PortalListEntity(
      id: _i6.PortalEntityMapper.mapId(model),
      name: model.name,
      url: model.url,
      image: model.image,
    );
  }

  _i5.MiniAppEntity _map__i4$MiniAppResponse_To__i5$MiniAppEntity(
      _i4.MiniAppResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping MiniAppResponse → MiniAppEntity failed because MiniAppResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<MiniAppResponse, MiniAppEntity> to handle null values during mapping.');
    }
    return _i5.MiniAppEntity(
      id: _i6.PortalEntityMapper.mapId(model),
      name: model.name,
      categoryId: model.categoryId,
      category: _map__i4$MiniAppCategoryResponse_To__i5$MiniAppCategoryEntity(
          model.category),
      link: model.link,
      iconUrl: model.iconUrl,
      description: model.description,
      access: model.access,
      members: model.members
          .map<_i5.MiniAppMemberEntity>((value) =>
              _map__i4$MiniAppMemberResponse_To__i5$MiniAppMemberEntity(value))
          .toList(),
    );
  }

  _i5.MiniAppCategoryEntity
      _map__i4$MiniAppCategoryResponse_To__i5$MiniAppCategoryEntity(
          _i4.MiniAppCategoryResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping MiniAppCategoryResponse → MiniAppCategoryEntity failed because MiniAppCategoryResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<MiniAppCategoryResponse, MiniAppCategoryEntity> to handle null values during mapping.');
    }
    return _i5.MiniAppCategoryEntity(
      id: _i6.PortalEntityMapper.mapId(model),
      name: model.name,
    );
  }

  _i5.MiniAppMemberEntity
      _map__i4$MiniAppMemberResponse_To__i5$MiniAppMemberEntity(
          _i4.MiniAppMemberResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping MiniAppMemberResponse → MiniAppMemberEntity failed because MiniAppMemberResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<MiniAppMemberResponse, MiniAppMemberEntity> to handle null values during mapping.');
    }
    return _i5.MiniAppMemberEntity(
      id: _i6.PortalEntityMapper.mapId(model),
      type: model.type,
      name: model.name,
    );
  }
}
