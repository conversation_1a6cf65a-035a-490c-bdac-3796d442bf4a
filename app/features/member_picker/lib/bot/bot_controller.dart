import 'package:gp_core/core.dart';
import 'package:gp_core/models/bot/bot_response.dart';
import 'package:gp_feat_member_picker/base/base_picker_controller.dart';

import '../invitees_list/widget/limit_dialog.dart';
import '../picker_mixin.dart';
import '../selected_list/select_invitees_controller.dart';

const kBotListWidget = "kBotListWidget";

class BotController extends BasePickerController<ChatBotModel>
    with ArgumentConfigMixin
    implements ArgumentInitialMixin {
  final BotAPI api = BotAPI();

  @override
  Future fetchData() async {
    final response = await api.searchBot(
      searchStr: searchStr,
      currentPage: currentPage,
    );

    handleLinks(response);

    return response.data;
  }

  @override
  Future filter() async {
    currentPage = 1;

    listItem.clear();

    return await getListItems();
  }

  @override
  void onSelected(ChatBotModel item) {
    // <PERSON><PERSON>m tra giới hạn chọn cho tab bot khi đang chọn
    if (item.rxIsSelected.value) {
      final tabLimit = SelectInviteeTabs.bot.getMaxSelectionLimit(svo);
      if (tabLimit != null && selectedItems.length >= tabLimit) {
        // Bỏ chọn lại item vì đã đạt giới hạn
        item.rxIsSelected.value = false;

        Popup.instance.showBottomSheet(LimitDialog(
            title:
                "${LocaleKeys.memberPicker_limit_title.tr} ${SelectInviteeTabs.bot.title.toLowerCase()}",
            content:
                "${LocaleKeys.memberPicker_limit_description.tr} $tabLimit ${SelectInviteeTabs.bot.title.toLowerCase()}"));
        return;
      }
    }

    super.onSelected(item);
    Get.find<SelectInviteesController>().checkHasAnySelectedItem();
  }

  @override
  void initArguments(SelectInviteesOptions arg) {
    svo = arg;
  }
}
