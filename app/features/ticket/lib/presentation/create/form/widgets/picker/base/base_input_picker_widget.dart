// ignore_for_file: invalid_use_of_visible_for_testing_member, invalid_use_of_protected_member

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_shared/domain/entity/attachment/gp_attachment_file.entity.dart';
import 'package:gp_shared/mapper/gp_mapper.dart';
import 'package:gp_shared/shared_feature/upload_download/upload_download.dart';

import 'attachment_upload_state.dart';

import '../../../../../../widgets/base/base_ticket_input_behavior.dart';
import '../../../../ticket_create.page.dart';
import '../../../../widgets/input/input_behavior.dart';
import '../../../../widgets/shared/title_widget.dart';
import '../../base_form_input.dart';
import '../../input_error.dart';
import 'base_input_picker.dart';

abstract class TicketCreatePickerBehavior<T> {
  Future onAddData();

  Widget buildDataWidget(List<T> data, int displayCount);
}

enum TicketBaseAttachmentListViewType {
  grid,
  list,
  wrap,
}

abstract class TicketCreateBasePickerWrapperWidget<E,
        P extends TicketCreatePickerParams<E>>
    extends TicketCreateInputBaseInputWidget<P> {
  const TicketCreateBasePickerWrapperWidget({
    required super.inputBehavior,
    required super.params,
    required super.isCreatingTicket,
    super.key,
  });
}

class TicketCreateBasePickerWrapperState<
    W extends TicketCreateBasePickerWrapperWidget<E, P>,
    P extends TicketCreatePickerParams<E>,
    E> extends TicketCreateInputBaseInputState<P, W> {
  late final ValueNotifier<TicketCreatePickerParams<E>> rxParams =
      ValueNotifier(widget.params);

  final ValueNotifier<BaseTicketInputValidateMode> rxCurrentValidateMode =
      ValueNotifier(BaseTicketInputValidateMode.validateFromAnotherAction);

  @override
  bool get canEdit => true;

  @override
  bool hasData(BaseTicketInputValidateMode mode) {
    rxCurrentValidateMode.value = mode;
    rxCurrentValidateMode.notifyListeners();

    if (widget.params.permissions?.canSubmitRequireField == true) {
      return true;
    }

    if (params.isRequired == true) {
      return params.data.isNotEmpty;
    }

    return true;
  }

  @override
  void didUpdateWidget(
    covariant W oldWidget,
  ) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.params.id != widget.params.id) {
      rxParams.value = widget.params;
    }
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox();
  }

  Widget buildWhenHasNoDataWidget() {
    return ValueListenableBuilder(
      valueListenable: rxCurrentValidateMode,
      builder: (context, value, child) {
        final hasDataWhenValidate = hasData(rxCurrentValidateMode.value);

        Widget? childWidget;

        if (!hasDataWhenValidate) {
          if (rxCurrentValidateMode.value ==
              BaseTicketInputValidateMode.validateFromForm) {
            childWidget = const TicketCreateInputErrorWidget();
          } else {
            // childWidget = TicketCreateInputErrorWidget(
            //   error: LocaleKeys
            //       .ticket_create_input_base_attachment_not_pick_data_hint.tr,
            //   textColor: GPColor.contentSecondary,
            // );
          }
        }

        return childWidget ?? const SizedBox();
      },
    );
  }
}

class TicketCreateBaseAttachmentPickerWrapperState<
        W extends TicketCreateBasePickerWrapperWidget<E, P>,
        P extends TicketCreatePickerParams<E>,
        E extends GPAttachmentFileEntity>
    extends TicketCreateBasePickerWrapperState<W, P, GPAttachmentFileEntity>
    with TicketAttachmentPickerMixin {
  final ValueNotifier<int> rxUploadingCount = ValueNotifier(0);

  @override
  void initState() {
    super.initState();

    rxUploadingCount.addListener(_checkUploadCompletion);
  }

  late final GPUploadCallback uploadCallback = GPUploadCallback(
    onAFileStartUpload: (client, estimate) {
      TicketAttachmentUploadState.isUploading = true;
      rxUploadingCount.value++;
    },
    onAFileUploaded: (
      totalUploadedFiles,
      totalFiles,
      uploadUrl,
      localEntity,
      responseWrapper,
    ) {
      assert(localEntity != null);
      if (localEntity == null) {
        rxUploadingCount.value--;
        return;
      }

      if (responseWrapper != null) {
        final GPMapper mapper = GetIt.I<GPMapper>(instanceName: 'kGPMapper');

        GPAttachmentFileEntity? attachmentFileEntity;
        if (responseWrapper.uploadFileResponseModels.isNotEmpty) {
          final fileResponse = responseWrapper.uploadFileResponseModels.first;
          attachmentFileEntity =
              mapper.convert<UploadFileResponseModel, GPAttachmentFileEntity>(
                  fileResponse);
          attachmentFileEntity.response = fileResponse;
        } else if (responseWrapper.uploadImageResponseModels.isNotEmpty) {
          final fileResponse = responseWrapper.uploadImageResponseModels.first;
          attachmentFileEntity =
              mapper.convert<UploadImageResponseModel, GPAttachmentFileEntity>(
                  fileResponse);
          attachmentFileEntity.response = fileResponse;
        }

        if (attachmentFileEntity == null) {
          rxUploadingCount.value--;
          return;
        }

        attachmentFileEntity.initLocalEntity(localEntity.localEntity);

        if (E == GPAttachmentFileEntity || E is GPAttachmentFileEntity) {
          final oldEntityIndex = params.data.indexWhere((e) {
            return e.id == localEntity.id;
          });

          if (oldEntityIndex != -1) {
            if (params.data.isNotEmpty == true) {
              params.data.removeAt(oldEntityIndex);
            }
            params.data.insert(oldEntityIndex, attachmentFileEntity as E);
            rxParams.value = params;
            rxParams.notifyListeners();
          }
        }
      }

      rxUploadingCount.value--;
    },
  );

  void _checkUploadCompletion() {
    if (rxUploadingCount.value <= 0) {
      TicketAttachmentUploadState.isUploading = false;

      // Reset counter
      rxUploadingCount.value = 0;
    }
  }

  @override
  void dispose() {
    rxUploadingCount.removeListener(_checkUploadCompletion);
    rxUploadingCount.dispose();
    super.dispose();
  }
}

class TicketCreateBasePickerWidget<T> extends StatelessWidget {
  TicketCreateBasePickerWidget({
    required this.inputBehavior,
    required this.attachmentBehavior,
    required this.params,
    required this.listViewType,
    this.isCreatingTicket = false,
    this.hasInitData = false,
    super.key,
  }) {
    rxExpandData.value = params.isExpanded;
  }

  final InputBehavior inputBehavior;

  final TicketCreatePickerBehavior<T> attachmentBehavior;
  final TicketCreatePickerParams<T> params;
  final TicketBaseAttachmentListViewType listViewType;

  final ValueNotifier<bool> rxExpandData = ValueNotifier(false);

  final bool isCreatingTicket;
  final bool hasInitData;

  Future<List<T>> onAddData() async {
    final results = await attachmentBehavior.onAddData();

    // update lại view sau khi add
    // rxExpandData.notifyListeners();

    return results;
  }

  void onToggleExpand() {
    rxExpandData.value = !rxExpandData.value;
  }

  int get displayCount {
    return rxExpandData.value
        ? (params.showLessDisplayCount)
        : params.data.length;
  }

  bool get showViewAllBtn => params.data.length > params.showLessDisplayCount;

  bool get canEdit =>
      params.permissions?.isFieldCanEdit(
          isCreatingTicket: isCreatingTicket, hasValue: hasInitData) ??
      false;

  @override
  Widget build(BuildContext context) {
    return AutoScrollTag(
      key: ValueKey(params.formIndex),
      controller: ticketAutoScrollController,
      index: params.formIndex,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          TitleWidget(
            title: params.title,
            subTitle: params.subTitle,
            tailingWidget: params.isReadOnly || !canEdit
                ? const SizedBox()
                : _TailingWidget(
                    addAssets: params.addAssets,
                    onAddData: onAddData,
                  ),
          ),
          ValueListenableBuilder(
            valueListenable: rxExpandData,
            builder: (context, value, child) {
              return attachmentBehavior.buildDataWidget(
                params.data,
                displayCount,
              );
            },
          ),
          if (showViewAllBtn)
            InkWell(
              onTap: onToggleExpand,
              borderRadius: BorderRadius.circular(8),
              child: ValueListenableBuilder(
                valueListenable: rxExpandData,
                builder: (context, value, child) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgWidget(
                        value
                            ? Assets
                                .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_FILL_CHEVRON_DOWN_SVG
                            : Assets
                                .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_FILL_CHEVRON_UP_SVG,
                        color: GPColor.functionAccentWorkSecondary,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        value
                            ? LocaleKeys.view_all.tr
                            : LocaleKeys.view_less.tr,
                        style: textStyle(GPTypography.headingMedium)
                            ?.mergeColor(GPColor.functionAccentWorkSecondary),
                      ),
                    ],
                  );
                },
              ).paddingSymmetric(vertical: 8, horizontal: 4),
            ),
          const SizedBox(height: 24),
        ],
      ),
    ).paddingSymmetric(horizontal: 16);
  }
}

class _TailingWidget extends StatelessWidget {
  const _TailingWidget({
    required this.addAssets,
    required this.onAddData,
  });

  final String addAssets;
  final void Function() onAddData;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onAddData,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Row(
          children: [
            SvgWidget(
              addAssets,
              color: GPColor.functionAccentWorkSecondary,
            ),
            const SizedBox(width: 8),
            Text(
              LocaleKeys.ticket_create_input_media_add.tr,
              style: textStyle(GPTypography.headingSmall)
                  ?.mergeColor(GPColor.functionAccentWorkSecondary),
            )
          ],
        ),
      ),
    );
  }
}

mixin TicketAttachmentPickerMixin {
  List<GPAttachmentFileEntity> attachmentEntitiesFromPicker(
    GPFilePickerResult pickerResult,
    FileType fileType,
    GPUploadCallback? callback,
  ) {
    final List<File> files = [];

    files.addAll((pickerResult.gpXFiles ?? []).map(
      (element) => File(element.xFile.path),
    ));

    files.addAll((pickerResult.filePickerResult?.files ?? []).map(
      (element) => File(element.xFile.path),
    ));

    return files
        .map(
          (file) => GPAttachmentFileEntity.fromLocalFiles(
            file: file,
            fileType: fileType,
            callback: callback,
          )..uploadFile(),
        )
        .toList();
  }
}
