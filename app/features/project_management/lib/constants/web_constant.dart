final class GPWebConstants {
  static const String kWebviewTestUrl =
      'http://192.168.10.218:8000/Downloads/test_webview/index.html';

  static const String kWebviewUrl = 'https://pm.gapowork.vn/{workspace_id}/';

  static const String kWebviewStagingUrl =
      'https://plane-staging.gapowork.vn/{workspace_id}';

  static const String kWebChannel = 'GPWebChannel';
  static const String kWindowReceiveTokenMethod = 'window.receiveToken';

  /// Web: Token handled successfully!
  static const String kTokenSuccess = 'web_token_success';

  /// Web: Communication channel GPWebChannel not found.
  static const String kWebChannelError = 'web_channel_not_found';

  /// Web: ReceiveToken was called but no token was provided.
  static const String kTokenError = 'web_token_error_no_token_provided';
}
