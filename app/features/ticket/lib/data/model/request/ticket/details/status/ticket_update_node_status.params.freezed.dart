// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_update_node_status.params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketUpdateNodeStatusParams {
  @JsonKey(name: 'status')
  TicketNodeStatus get status;

  /// Serializes this TicketUpdateNodeStatusParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketUpdateNodeStatusParams &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, status);

  @override
  String toString() {
    return 'TicketUpdateNodeStatusParams(status: $status)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketUpdateNodeStatusParams implements TicketUpdateNodeStatusParams {
  _TicketUpdateNodeStatusParams(
      {@JsonKey(name: 'status') required this.status});

  @override
  @JsonKey(name: 'status')
  final TicketNodeStatus status;

  @override
  Map<String, dynamic> toJson() {
    return _$TicketUpdateNodeStatusParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketUpdateNodeStatusParams &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, status);

  @override
  String toString() {
    return 'TicketUpdateNodeStatusParams(status: $status)';
  }
}

// dart format on
