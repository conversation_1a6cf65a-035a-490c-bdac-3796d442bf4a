// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_redo_end_node.params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketRedoEndNodeParams {
  @JsonKey(name: 'reason')
  String get reason;
  @JsonKey(name: 'node_ids')
  List<int> get nodeIds;

  /// Serializes this TicketRedoEndNodeParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketRedoEndNodeParams &&
            (identical(other.reason, reason) || other.reason == reason) &&
            const DeepCollectionEquality().equals(other.nodeIds, nodeIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, reason, const DeepCollectionEquality().hash(nodeIds));

  @override
  String toString() {
    return 'TicketRedoEndNodeParams(reason: $reason, nodeIds: $nodeIds)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketRedoEndNodeParams implements TicketRedoEndNodeParams {
  _TicketRedoEndNodeParams(
      {@JsonKey(name: 'reason') required this.reason,
      @JsonKey(name: 'node_ids') required final List<int> nodeIds})
      : _nodeIds = nodeIds;

  @override
  @JsonKey(name: 'reason')
  final String reason;
  final List<int> _nodeIds;
  @override
  @JsonKey(name: 'node_ids')
  List<int> get nodeIds {
    if (_nodeIds is EqualUnmodifiableListView) return _nodeIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_nodeIds);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TicketRedoEndNodeParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketRedoEndNodeParams &&
            (identical(other.reason, reason) || other.reason == reason) &&
            const DeepCollectionEquality().equals(other._nodeIds, _nodeIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, reason, const DeepCollectionEquality().hash(_nodeIds));

  @override
  String toString() {
    return 'TicketRedoEndNodeParams(reason: $reason, nodeIds: $nodeIds)';
  }
}

// dart format on
