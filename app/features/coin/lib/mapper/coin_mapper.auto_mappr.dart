// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoMapprGenerator
// **************************************************************************

// ignore_for_file: type=lint, unnecessary_cast, unused_local_variable

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i1;
import 'package:gp_core/models/assignee.dart' as _i8;
import 'package:gp_core/models/bot/bot_response.dart' as _i19;
import 'package:gp_core/models/conversation.dart' as _i13;
import 'package:gp_core/models/orgchat/organization_department.dart' as _i15;
import 'package:gp_core/models/orgchat/organization_role.dart' as _i17;
import 'package:gp_core/models/orgchat/select_invitees_model.dart' as _i21;
import 'package:gp_shared/domain/entity/assignee/assignee.entity.dart' as _i10;
import 'package:gp_shared/domain/entity/assignee/assignee_info.entity.dart'
    as _i12;
import 'package:gp_shared/domain/entity/assignee/assignee_work.entity.dart'
    as _i11;
import 'package:gp_shared/domain/entity/chatbot/chatbot.entity.dart' as _i20;
import 'package:gp_shared/domain/entity/conversation/conversation.entity.dart'
    as _i14;
import 'package:gp_shared/domain/entity/department/department.entity.dart'
    as _i16;
import 'package:gp_shared/domain/entity/role/role.entity.dart' as _i18;
import 'package:gp_shared/domain/entity/select_invite_options.entity.dart'
    as _i22;
import 'package:gp_shared/domain/entity/user/gp_user.entity.dart' as _i9;
import 'package:gp_shared/mapper/entity/assignee_entity_mapper.dart' as _i28;

import '../data/model/response/coin_qr_code_state_response.dart' as _i6;
import '../data/model/response/coin_transaction_response.dart' as _i4;
import '../data/model/response/gift_exchange_detail_response.dart' as _i25;
import '../data/model/response/gift_response.dart' as _i23;
import '../data/model/response/wallet_info_response.dart' as _i2;
import '../domain/entity/coin_qr_code.entity.dart' as _i7;
import '../domain/entity/coin_transaction.entity.dart' as _i5;
import '../domain/entity/gift.entity.dart' as _i24;
import '../domain/entity/gift_exchange_detail.entity.dart' as _i26;
import '../domain/entity/wallet_info.entity.dart' as _i3;
import 'entity/coin_entity_mapper.dart' as _i27;
import 'entity/gift_entity_mapper.dart' as _i29;

/// Available mappings:
/// - `WalletInfoResponse` → `WalletInfoEntity`.
/// - `CoinTransactionResponse` → `CoinTransactionEntity`.
/// - `CoinQrCodeStateResponse` → `CoinQrCodeEntity`.
/// - `Assignee` → `GPUserEntity`.
/// - `AssigneeEntity` → `GPUserEntity`.
/// - `GPUserEntity` → `AssigneeEntity`.
/// - `Assignee` → `AssigneeEntity`.
/// - `Work` → `WorkEntity`.
/// - `Info` → `InfoEntity`.
/// - `Conversation` → `ConversationEntity`.
/// - `OrganizationDepartment` → `OrganizationDepartmentEntity`.
/// - `OrganizationRole` → `OrganizationRoleEntity`.
/// - `ChatBotModel` → `ChatBotEntity`.
/// - `SelectInviteesOptions` → `SelectMemberEntity`.
/// - `AssigneeEntity` → `Assignee`.
/// - `WorkEntity` → `Work`.
/// - `InfoEntity` → `Info`.
/// - `ConversationEntity` → `Conversation`.
/// - `OrganizationRoleEntity` → `OrganizationRole`.
/// - `OrganizationDepartmentEntity` → `OrganizationDepartment`.
/// - `ChatBotEntity` → `ChatBotModel`.
/// - `GiftResponse` → `GiftEntity`.
/// - `GiftExchangeDetailResponse` → `GiftExchangeDetailEntity`.
class $GPCoinMapper implements _i1.AutoMapprInterface {
  const $GPCoinMapper();

  Type _typeOf<T>() => T;

  List<_i1.AutoMapprInterface> get _delegates => const [];

  
  
  @override
  bool canConvert<SOURCE, TARGET>({bool recursive = true}) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.WalletInfoResponse>() ||
            sourceTypeOf == _typeOf<_i2.WalletInfoResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.WalletInfoEntity>() ||
            targetTypeOf == _typeOf<_i3.WalletInfoEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.CoinTransactionResponse>() ||
            sourceTypeOf == _typeOf<_i4.CoinTransactionResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.CoinTransactionEntity>() ||
            targetTypeOf == _typeOf<_i5.CoinTransactionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i6.CoinQrCodeStateResponse>() ||
            sourceTypeOf == _typeOf<_i6.CoinQrCodeStateResponse?>()) &&
        (targetTypeOf == _typeOf<_i7.CoinQrCodeEntity>() ||
            targetTypeOf == _typeOf<_i7.CoinQrCodeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i8.Assignee>() ||
            sourceTypeOf == _typeOf<_i8.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i9.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i9.GPUserEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i10.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i10.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i9.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i9.GPUserEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.GPUserEntity>() ||
            sourceTypeOf == _typeOf<_i9.GPUserEntity?>()) &&
        (targetTypeOf == _typeOf<_i10.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i10.AssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i8.Assignee>() ||
            sourceTypeOf == _typeOf<_i8.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i10.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i10.AssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i8.Work>() ||
            sourceTypeOf == _typeOf<_i8.Work?>()) &&
        (targetTypeOf == _typeOf<_i11.WorkEntity>() ||
            targetTypeOf == _typeOf<_i11.WorkEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i8.Info>() ||
            sourceTypeOf == _typeOf<_i8.Info?>()) &&
        (targetTypeOf == _typeOf<_i12.InfoEntity>() ||
            targetTypeOf == _typeOf<_i12.InfoEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i13.Conversation>() ||
            sourceTypeOf == _typeOf<_i13.Conversation?>()) &&
        (targetTypeOf == _typeOf<_i14.ConversationEntity>() ||
            targetTypeOf == _typeOf<_i14.ConversationEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.OrganizationDepartment>() ||
            sourceTypeOf == _typeOf<_i15.OrganizationDepartment?>()) &&
        (targetTypeOf == _typeOf<_i16.OrganizationDepartmentEntity>() ||
            targetTypeOf == _typeOf<_i16.OrganizationDepartmentEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i17.OrganizationRole>() ||
            sourceTypeOf == _typeOf<_i17.OrganizationRole?>()) &&
        (targetTypeOf == _typeOf<_i18.OrganizationRoleEntity>() ||
            targetTypeOf == _typeOf<_i18.OrganizationRoleEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i19.ChatBotModel>() ||
            sourceTypeOf == _typeOf<_i19.ChatBotModel?>()) &&
        (targetTypeOf == _typeOf<_i20.ChatBotEntity>() ||
            targetTypeOf == _typeOf<_i20.ChatBotEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i21.SelectInviteesOptions>() ||
            sourceTypeOf == _typeOf<_i21.SelectInviteesOptions?>()) &&
        (targetTypeOf == _typeOf<_i22.SelectMemberEntity>() ||
            targetTypeOf == _typeOf<_i22.SelectMemberEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i10.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i10.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.Assignee>() ||
            targetTypeOf == _typeOf<_i8.Assignee?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i11.WorkEntity>() ||
            sourceTypeOf == _typeOf<_i11.WorkEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.Work>() ||
            targetTypeOf == _typeOf<_i8.Work?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i12.InfoEntity>() ||
            sourceTypeOf == _typeOf<_i12.InfoEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.Info>() ||
            targetTypeOf == _typeOf<_i8.Info?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i14.ConversationEntity>() ||
            sourceTypeOf == _typeOf<_i14.ConversationEntity?>()) &&
        (targetTypeOf == _typeOf<_i13.Conversation>() ||
            targetTypeOf == _typeOf<_i13.Conversation?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i18.OrganizationRoleEntity>() ||
            sourceTypeOf == _typeOf<_i18.OrganizationRoleEntity?>()) &&
        (targetTypeOf == _typeOf<_i17.OrganizationRole>() ||
            targetTypeOf == _typeOf<_i17.OrganizationRole?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i16.OrganizationDepartmentEntity>() ||
            sourceTypeOf == _typeOf<_i16.OrganizationDepartmentEntity?>()) &&
        (targetTypeOf == _typeOf<_i15.OrganizationDepartment>() ||
            targetTypeOf == _typeOf<_i15.OrganizationDepartment?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i20.ChatBotEntity>() ||
            sourceTypeOf == _typeOf<_i20.ChatBotEntity?>()) &&
        (targetTypeOf == _typeOf<_i19.ChatBotModel>() ||
            targetTypeOf == _typeOf<_i19.ChatBotModel?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i23.GiftResponse>() ||
            sourceTypeOf == _typeOf<_i23.GiftResponse?>()) &&
        (targetTypeOf == _typeOf<_i24.GiftEntity>() ||
            targetTypeOf == _typeOf<_i24.GiftEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i25.GiftExchangeDetailResponse>() ||
            sourceTypeOf == _typeOf<_i25.GiftExchangeDetailResponse?>()) &&
        (targetTypeOf == _typeOf<_i26.GiftExchangeDetailEntity>() ||
            targetTypeOf == _typeOf<_i26.GiftExchangeDetailEntity?>())) {
      return true;
    }
    if (recursive) {
      for (final mappr in _delegates) {
        if (mappr.canConvert<SOURCE, TARGET>()) {
          return true;
        }
      }
    }
    return false;
  }

  
  
  @override
  TARGET convert<SOURCE, TARGET>(SOURCE? model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _convert(model)!;
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convert(model)!;
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  TARGET? tryConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _safeConvert(
        model,
        onMappingError: onMappingError,
      );
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvert(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    return null;
  }

  
  
  @override
  Iterable<TARGET> convertIterable<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET>((item) => _convert(item)!);
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertIterable(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Iterable<TARGET?> tryConvertIterable<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET?>(
          (item) => _safeConvert(item, onMappingError: onMappingError));
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertIterable(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  List<TARGET> convertList<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertList(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  List<TARGET?> tryConvertList<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertList(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  Set<TARGET> convertSet<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertSet(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Set<TARGET?> tryConvertSet<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertSet(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  TARGET? _convert<SOURCE, TARGET>(
    SOURCE? model, {
    bool canReturnNull = false,
  }) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.WalletInfoResponse>() ||
            sourceTypeOf == _typeOf<_i2.WalletInfoResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.WalletInfoEntity>() ||
            targetTypeOf == _typeOf<_i3.WalletInfoEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$WalletInfoResponse_To__i3$WalletInfoEntity(
          (model as _i2.WalletInfoResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.CoinTransactionResponse>() ||
            sourceTypeOf == _typeOf<_i4.CoinTransactionResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.CoinTransactionEntity>() ||
            targetTypeOf == _typeOf<_i5.CoinTransactionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$CoinTransactionResponse_To__i5$CoinTransactionEntity(
          (model as _i4.CoinTransactionResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i6.CoinQrCodeStateResponse>() ||
            sourceTypeOf == _typeOf<_i6.CoinQrCodeStateResponse?>()) &&
        (targetTypeOf == _typeOf<_i7.CoinQrCodeEntity>() ||
            targetTypeOf == _typeOf<_i7.CoinQrCodeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i6$CoinQrCodeStateResponse_To__i7$CoinQrCodeEntity(
          (model as _i6.CoinQrCodeStateResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i8.Assignee>() ||
            sourceTypeOf == _typeOf<_i8.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i9.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i9.GPUserEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i8$Assignee_To__i9$GPUserEntity((model as _i8.Assignee?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i10.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i10.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i9.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i9.GPUserEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i10$AssigneeEntity_To__i9$GPUserEntity(
          (model as _i10.AssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.GPUserEntity>() ||
            sourceTypeOf == _typeOf<_i9.GPUserEntity?>()) &&
        (targetTypeOf == _typeOf<_i10.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i10.AssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$GPUserEntity_To__i10$AssigneeEntity(
          (model as _i9.GPUserEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i8.Assignee>() ||
            sourceTypeOf == _typeOf<_i8.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i10.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i10.AssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i8$Assignee_To__i10$AssigneeEntity((model as _i8.Assignee?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i8.Work>() ||
            sourceTypeOf == _typeOf<_i8.Work?>()) &&
        (targetTypeOf == _typeOf<_i11.WorkEntity>() ||
            targetTypeOf == _typeOf<_i11.WorkEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i8$Work_To__i11$WorkEntity((model as _i8.Work?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i8.Info>() ||
            sourceTypeOf == _typeOf<_i8.Info?>()) &&
        (targetTypeOf == _typeOf<_i12.InfoEntity>() ||
            targetTypeOf == _typeOf<_i12.InfoEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i8$Info_To__i12$InfoEntity((model as _i8.Info?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i13.Conversation>() ||
            sourceTypeOf == _typeOf<_i13.Conversation?>()) &&
        (targetTypeOf == _typeOf<_i14.ConversationEntity>() ||
            targetTypeOf == _typeOf<_i14.ConversationEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i13$Conversation_To__i14$ConversationEntity(
          (model as _i13.Conversation?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.OrganizationDepartment>() ||
            sourceTypeOf == _typeOf<_i15.OrganizationDepartment?>()) &&
        (targetTypeOf == _typeOf<_i16.OrganizationDepartmentEntity>() ||
            targetTypeOf == _typeOf<_i16.OrganizationDepartmentEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$OrganizationDepartment_To__i16$OrganizationDepartmentEntity(
          (model as _i15.OrganizationDepartment?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i17.OrganizationRole>() ||
            sourceTypeOf == _typeOf<_i17.OrganizationRole?>()) &&
        (targetTypeOf == _typeOf<_i18.OrganizationRoleEntity>() ||
            targetTypeOf == _typeOf<_i18.OrganizationRoleEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i17$OrganizationRole_To__i18$OrganizationRoleEntity(
          (model as _i17.OrganizationRole?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i19.ChatBotModel>() ||
            sourceTypeOf == _typeOf<_i19.ChatBotModel?>()) &&
        (targetTypeOf == _typeOf<_i20.ChatBotEntity>() ||
            targetTypeOf == _typeOf<_i20.ChatBotEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i19$ChatBotModel_To__i20$ChatBotEntity(
          (model as _i19.ChatBotModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i21.SelectInviteesOptions>() ||
            sourceTypeOf == _typeOf<_i21.SelectInviteesOptions?>()) &&
        (targetTypeOf == _typeOf<_i22.SelectMemberEntity>() ||
            targetTypeOf == _typeOf<_i22.SelectMemberEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i21$SelectInviteesOptions_To__i22$SelectMemberEntity(
          (model as _i21.SelectInviteesOptions?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i10.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i10.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.Assignee>() ||
            targetTypeOf == _typeOf<_i8.Assignee?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i10$AssigneeEntity_To__i8$Assignee(
          (model as _i10.AssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i11.WorkEntity>() ||
            sourceTypeOf == _typeOf<_i11.WorkEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.Work>() ||
            targetTypeOf == _typeOf<_i8.Work?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i11$WorkEntity_To__i8$Work((model as _i11.WorkEntity?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i12.InfoEntity>() ||
            sourceTypeOf == _typeOf<_i12.InfoEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.Info>() ||
            targetTypeOf == _typeOf<_i8.Info?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i12$InfoEntity_To__i8$Info((model as _i12.InfoEntity?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i14.ConversationEntity>() ||
            sourceTypeOf == _typeOf<_i14.ConversationEntity?>()) &&
        (targetTypeOf == _typeOf<_i13.Conversation>() ||
            targetTypeOf == _typeOf<_i13.Conversation?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i14$ConversationEntity_To__i13$Conversation(
          (model as _i14.ConversationEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i18.OrganizationRoleEntity>() ||
            sourceTypeOf == _typeOf<_i18.OrganizationRoleEntity?>()) &&
        (targetTypeOf == _typeOf<_i17.OrganizationRole>() ||
            targetTypeOf == _typeOf<_i17.OrganizationRole?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i18$OrganizationRoleEntity_To__i17$OrganizationRole(
          (model as _i18.OrganizationRoleEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i16.OrganizationDepartmentEntity>() ||
            sourceTypeOf == _typeOf<_i16.OrganizationDepartmentEntity?>()) &&
        (targetTypeOf == _typeOf<_i15.OrganizationDepartment>() ||
            targetTypeOf == _typeOf<_i15.OrganizationDepartment?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i16$OrganizationDepartmentEntity_To__i15$OrganizationDepartment(
          (model as _i16.OrganizationDepartmentEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i20.ChatBotEntity>() ||
            sourceTypeOf == _typeOf<_i20.ChatBotEntity?>()) &&
        (targetTypeOf == _typeOf<_i19.ChatBotModel>() ||
            targetTypeOf == _typeOf<_i19.ChatBotModel?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i20$ChatBotEntity_To__i19$ChatBotModel(
          (model as _i20.ChatBotEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i23.GiftResponse>() ||
            sourceTypeOf == _typeOf<_i23.GiftResponse?>()) &&
        (targetTypeOf == _typeOf<_i24.GiftEntity>() ||
            targetTypeOf == _typeOf<_i24.GiftEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i23$GiftResponse_To__i24$GiftEntity(
          (model as _i23.GiftResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i25.GiftExchangeDetailResponse>() ||
            sourceTypeOf == _typeOf<_i25.GiftExchangeDetailResponse?>()) &&
        (targetTypeOf == _typeOf<_i26.GiftExchangeDetailEntity>() ||
            targetTypeOf == _typeOf<_i26.GiftExchangeDetailEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i25$GiftExchangeDetailResponse_To__i26$GiftExchangeDetailEntity(
          (model as _i25.GiftExchangeDetailResponse?)) as TARGET);
    }
    throw Exception('No ${model.runtimeType} -> $targetTypeOf mapping.');
  }

  TARGET? _safeConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (!useSafeMapping<SOURCE, TARGET>()) {
      return _convert(
        model,
        canReturnNull: true,
      );
    }
    try {
      return _convert(
        model,
        canReturnNull: true,
      );
    } catch (e, s) {
      onMappingError?.call(e, s, model);
      return null;
    }
  }

  
  
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return false;
  }

  _i3.WalletInfoEntity _map__i2$WalletInfoResponse_To__i3$WalletInfoEntity(
      _i2.WalletInfoResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WalletInfoResponse → WalletInfoEntity failed because WalletInfoResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WalletInfoResponse, WalletInfoEntity> to handle null values during mapping.');
    }
    return _i3.WalletInfoEntity(
      walletBalance: model.walletBalance,
      userId: model.userId,
      workspaceId: model.workspaceId,
      walletCurrency: model.walletCurrency,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i5.CoinTransactionEntity
      _map__i4$CoinTransactionResponse_To__i5$CoinTransactionEntity(
          _i4.CoinTransactionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping CoinTransactionResponse → CoinTransactionEntity failed because CoinTransactionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<CoinTransactionResponse, CoinTransactionEntity> to handle null values during mapping.');
    }
    return _i5.CoinTransactionEntity(
      transactionId: model.transactionId,
      transactionType: model.transactionType,
      transactionStatus: model.transactionStatus,
      contextType: model.contextType,
      walletBalanceChanged: model.walletBalanceChanged,
      contextName: model.contextName,
      fromUserId: model.fromUserId,
      toUserId: model.toUserId,
      walletCurrency: model.walletCurrency,
      createdAt: model.createdAt,
      message: model.message,
      fromUserInfo:
          _map__i8$Assignee_To__i10$AssigneeEntity_Nullable(model.fromUserInfo),
      toUserInfo:
          _map__i8$Assignee_To__i10$AssigneeEntity_Nullable(model.toUserInfo),
      contextId: model.contextId,
      exchangeInfo: _i27.CoinEntityMapper.mapExchangeInfo(model),
    );
  }

  _i7.CoinQrCodeEntity _map__i6$CoinQrCodeStateResponse_To__i7$CoinQrCodeEntity(
      _i6.CoinQrCodeStateResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping CoinQrCodeStateResponse → CoinQrCodeEntity failed because CoinQrCodeStateResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<CoinQrCodeStateResponse, CoinQrCodeEntity> to handle null values during mapping.');
    }
    return _i7.CoinQrCodeEntity()
      ..id = model.id
      ..point = model.point
      ..contextType = model.contextType
      ..ownerBy = model.ownerBy
      ..createdAt = model.createdAt
      ..updatedAt = model.updatedAt;
  }

  _i9.GPUserEntity _map__i8$Assignee_To__i9$GPUserEntity(_i8.Assignee? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Assignee → GPUserEntity failed because Assignee was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Assignee, GPUserEntity> to handle null values during mapping.');
    }
    return _i9.GPUserEntity(
      id: _i28.AssigneeEntityMapper.mapIntIdToString(model),
      name: model.displayName,
      avatar: model.avatar,
      displayName: model.displayName,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i9.GPUserEntity _map__i10$AssigneeEntity_To__i9$GPUserEntity(
      _i10.AssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AssigneeEntity → GPUserEntity failed because AssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AssigneeEntity, GPUserEntity> to handle null values during mapping.');
    }
    return _i9.GPUserEntity(
      id: _i28.AssigneeEntityMapper.mapAssigneeEntityId(model),
      avatar: model.avatar,
      displayName: model.displayName,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i10.AssigneeEntity _map__i9$GPUserEntity_To__i10$AssigneeEntity(
      _i9.GPUserEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GPUserEntity → AssigneeEntity failed because GPUserEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GPUserEntity, AssigneeEntity> to handle null values during mapping.');
    }
    return _i10.AssigneeEntity(
      id: _i28.AssigneeEntityMapper.mapGPUserEntityId(model),
      displayName: _i28.AssigneeEntityMapper.mapGPUserEntityDisplayName(model),
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i10.AssigneeEntity _map__i8$Assignee_To__i10$AssigneeEntity(
      _i8.Assignee? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Assignee → AssigneeEntity failed because Assignee was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Assignee, AssigneeEntity> to handle null values during mapping.');
    }
    return _i10.AssigneeEntity(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i8$Info_To__i12$InfoEntity_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i11.WorkEntity _map__i8$Work_To__i11$WorkEntity(_i8.Work? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Work → WorkEntity failed because Work was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Work, WorkEntity> to handle null values during mapping.');
    }
    return _i11.WorkEntity(
      company: model.company,
      department: model.department,
      title: model.title,
      departmentId: model.departmentId,
      departments: model.departments,
      departmentIds: model.departmentIds,
      roleId: model.roleId,
      privacy: model.privacy,
    );
  }

  _i12.InfoEntity _map__i8$Info_To__i12$InfoEntity(_i8.Info? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Info → InfoEntity failed because Info was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Info, InfoEntity> to handle null values during mapping.');
    }
    return _i12.InfoEntity(
        work: model.work
            ?.map<_i11.WorkEntity>(
                (value) => _map__i8$Work_To__i11$WorkEntity(value))
            .toList());
  }

  _i14.ConversationEntity _map__i13$Conversation_To__i14$ConversationEntity(
      _i13.Conversation? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Conversation → ConversationEntity failed because Conversation was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Conversation, ConversationEntity> to handle null values during mapping.');
    }
    return _i14.ConversationEntity(
      id: model.id,
      name: model.name,
      role: model.role,
      avatar: model.avatar,
      folder: model.folder,
      type: model.type,
      groupLevel: model.groupLevel,
      messageCount: model.messageCount,
      memberCount: model.memberCount,
    );
  }

  _i16.OrganizationDepartmentEntity
      _map__i15$OrganizationDepartment_To__i16$OrganizationDepartmentEntity(
          _i15.OrganizationDepartment? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationDepartment → OrganizationDepartmentEntity failed because OrganizationDepartment was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationDepartment, OrganizationDepartmentEntity> to handle null values during mapping.');
    }
    return _i16.OrganizationDepartmentEntity(
      id: model.id,
      name: model.name,
      children: model.children
          .map<_i16.OrganizationDepartmentEntity>((value) =>
              _map__i15$OrganizationDepartment_To__i16$OrganizationDepartmentEntity(
                  value))
          .toList(),
      groupId: model.groupId,
      treeId: model.treeId,
      threadId: model.threadId,
      isPrimary: model.isPrimary,
    );
  }

  _i18.OrganizationRoleEntity
      _map__i17$OrganizationRole_To__i18$OrganizationRoleEntity(
          _i17.OrganizationRole? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationRole → OrganizationRoleEntity failed because OrganizationRole was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationRole, OrganizationRoleEntity> to handle null values during mapping.');
    }
    return _i18.OrganizationRoleEntity(
      name: model.name,
      workspaceId: model.workspaceId,
      id: model.id,
    );
  }

  _i20.ChatBotEntity _map__i19$ChatBotModel_To__i20$ChatBotEntity(
      _i19.ChatBotModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ChatBotModel → ChatBotEntity failed because ChatBotModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ChatBotModel, ChatBotEntity> to handle null values during mapping.');
    }
    return _i20.ChatBotEntity(
      id: model.id,
      name: model.name,
      avatar: model.avatar,
      description: model.description,
      workspaceId: model.workspaceId,
      creatorId: model.creatorId,
      createdAt: model.createdAt,
      botCreatorType: model.botCreatorType,
    );
  }

  _i22.SelectMemberEntity
      _map__i21$SelectInviteesOptions_To__i22$SelectMemberEntity(
          _i21.SelectInviteesOptions? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping SelectInviteesOptions → SelectMemberEntity failed because SelectInviteesOptions was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<SelectInviteesOptions, SelectMemberEntity> to handle null values during mapping.');
    }
    return _i22.SelectMemberEntity(
      assigneeEntities: model.selectedMembers
          ?.map<_i10.AssigneeEntity>(
              (value) => _map__i8$Assignee_To__i10$AssigneeEntity(value))
          .toList(),
      conversationEntities: model.selectedThreads
          ?.map<_i14.ConversationEntity>((value) =>
              _map__i13$Conversation_To__i14$ConversationEntity(value))
          .toList(),
      departmentEntities: model.selectedDepartments
          ?.map<_i16.OrganizationDepartmentEntity>((value) =>
              _map__i15$OrganizationDepartment_To__i16$OrganizationDepartmentEntity(
                  value))
          .toList(),
      roleEntities: model.selectedRoles
          ?.map<_i18.OrganizationRoleEntity>((value) =>
              _map__i17$OrganizationRole_To__i18$OrganizationRoleEntity(value))
          .toList(),
      chatBotEntities: model.selectedBots
          ?.map<_i20.ChatBotEntity>(
              (value) => _map__i19$ChatBotModel_To__i20$ChatBotEntity(value))
          .toList(),
    );
  }

  _i8.Assignee _map__i10$AssigneeEntity_To__i8$Assignee(
      _i10.AssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AssigneeEntity → Assignee failed because AssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AssigneeEntity, Assignee> to handle null values during mapping.');
    }
    return _i8.Assignee(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i12$InfoEntity_To__i8$Info_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i8.Work _map__i11$WorkEntity_To__i8$Work(_i11.WorkEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkEntity → Work failed because WorkEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkEntity, Work> to handle null values during mapping.');
    }
    return _i8.Work(
      company: model.company,
      department: model.department,
      title: model.title,
      departmentId: model.departmentId,
      departments: model.departments,
      departmentIds: model.departmentIds,
      roleId: model.roleId,
      privacy: model.privacy,
    );
  }

  _i8.Info _map__i12$InfoEntity_To__i8$Info(_i12.InfoEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping InfoEntity → Info failed because InfoEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<InfoEntity, Info> to handle null values during mapping.');
    }
    return _i8.Info(
        work: model.work
            ?.map<_i8.Work>((value) => _map__i11$WorkEntity_To__i8$Work(value))
            .toList());
  }

  _i13.Conversation _map__i14$ConversationEntity_To__i13$Conversation(
      _i14.ConversationEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ConversationEntity → Conversation failed because ConversationEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ConversationEntity, Conversation> to handle null values during mapping.');
    }
    return _i13.Conversation(
      name: model.name,
      role: model.role,
      avatar: model.avatar,
      folder: model.folder,
      type: model.type,
      id: model.id,
      groupLevel: model.groupLevel,
      memberCount: model.memberCount,
    )..messageCount = model.messageCount;
  }

  _i17.OrganizationRole
      _map__i18$OrganizationRoleEntity_To__i17$OrganizationRole(
          _i18.OrganizationRoleEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationRoleEntity → OrganizationRole failed because OrganizationRoleEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationRoleEntity, OrganizationRole> to handle null values during mapping.');
    }
    return _i17.OrganizationRole(
      name: model.name,
      workspaceId: model.workspaceId,
      id: model.id,
    );
  }

  _i15.OrganizationDepartment
      _map__i16$OrganizationDepartmentEntity_To__i15$OrganizationDepartment(
          _i16.OrganizationDepartmentEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationDepartmentEntity → OrganizationDepartment failed because OrganizationDepartmentEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationDepartmentEntity, OrganizationDepartment> to handle null values during mapping.');
    }
    return _i15.OrganizationDepartment(
      id: model.id,
      name: model.name,
      children: model.children
          .map<_i15.OrganizationDepartment>((value) =>
              _map__i16$OrganizationDepartmentEntity_To__i15$OrganizationDepartment(
                  value))
          .toList(),
      isPrimary: model.isPrimary,
      threadId: model.threadId,
      treeId: model.treeId,
    )..groupId = model.groupId;
  }

  _i19.ChatBotModel _map__i20$ChatBotEntity_To__i19$ChatBotModel(
      _i20.ChatBotEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ChatBotEntity → ChatBotModel failed because ChatBotEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ChatBotEntity, ChatBotModel> to handle null values during mapping.');
    }
    return _i19.ChatBotModel(
      name: _i28.AssigneeEntityMapper.mapChatBotEntityName(model),
      id: _i28.AssigneeEntityMapper.mapChatBotEntityId(model),
      avatar: model.avatar,
      description: model.description,
      workspaceId: model.workspaceId,
      creatorId: model.creatorId,
      createdAt: model.createdAt,
      botCreatorType: model.botCreatorType,
    );
  }

  _i24.GiftEntity _map__i23$GiftResponse_To__i24$GiftEntity(
      _i23.GiftResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GiftResponse → GiftEntity failed because GiftResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GiftResponse, GiftEntity> to handle null values during mapping.');
    }
    return _i24.GiftEntity(
      id: _i29.GiftEntityMapper.mapIdFromGiftResponse(model),
      workspaceId: model.workspaceId,
      name: model.name,
      description: model.description,
      point: model.point,
      quantity: model.quantity,
      remainingQuantity: model.remainingQuantity,
      images: _i29.GiftEntityMapper.mapImages(model),
      status: model.status,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i26.GiftExchangeDetailEntity
      _map__i25$GiftExchangeDetailResponse_To__i26$GiftExchangeDetailEntity(
          _i25.GiftExchangeDetailResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GiftExchangeDetailResponse → GiftExchangeDetailEntity failed because GiftExchangeDetailResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GiftExchangeDetailResponse, GiftExchangeDetailEntity> to handle null values during mapping.');
    }
    return _i26.GiftExchangeDetailEntity(
      id: _i29.GiftEntityMapper.mapIdFromGiftExchangeDetail(model),
      workspaceId: model.workspaceId,
      giftId: model.giftId,
      giftInfo:
          _map__i23$GiftResponse_To__i24$GiftEntity_Nullable(model.giftInfo),
      name: model.name,
      description: model.description,
      point: model.point,
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      deliveryAddress: model.deliveryAddress,
      reason: model.reason,
    );
  }

  _i10.AssigneeEntity? _map__i8$Assignee_To__i10$AssigneeEntity_Nullable(
      _i8.Assignee? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i10.AssigneeEntity(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i8$Info_To__i12$InfoEntity_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i12.InfoEntity? _map__i8$Info_To__i12$InfoEntity_Nullable(_i8.Info? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i12.InfoEntity(
        work: model.work
            ?.map<_i11.WorkEntity>(
                (value) => _map__i8$Work_To__i11$WorkEntity(value))
            .toList());
  }

  _i8.Info? _map__i12$InfoEntity_To__i8$Info_Nullable(_i12.InfoEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i8.Info(
        work: model.work
            ?.map<_i8.Work>((value) => _map__i11$WorkEntity_To__i8$Work(value))
            .toList());
  }

  _i24.GiftEntity? _map__i23$GiftResponse_To__i24$GiftEntity_Nullable(
      _i23.GiftResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i24.GiftEntity(
      id: _i29.GiftEntityMapper.mapIdFromGiftResponse(model),
      workspaceId: model.workspaceId,
      name: model.name,
      description: model.description,
      point: model.point,
      quantity: model.quantity,
      remainingQuantity: model.remainingQuantity,
      images: _i29.GiftEntityMapper.mapImages(model),
      status: model.status,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }
}
