import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/domain/entity/entity.dart';

typedef OnTicketActionClick = Function(
  BuildContext context,
  TicketActionBottomSheetEntity actionEntity,
  TicketActionResult actionResult,
);

// ignore: must_be_immutable
class HomeActionView extends StatelessWidget {
  // ignore: prefer_const_constructors_in_immutables
  HomeActionView({
    super.key,
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.ticketFlowChartEntity,
    this.ticketActionEntity,
    this.onTicketActionClick,
    this.assignees,
    this.permissions,
    this.followers,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketFlowChartEntity ticketFlowChartEntity;
  final OnTicketActionClick? onTicketActionClick;

  final TicketActionEntity? ticketActionEntity;
  final List<Assignee>? assignees;
  final List<Assignee>? followers;
  final List<WorkflowFormFieldPermissionEntity>? permissions;

  @override
  Widget build(BuildContext context) {
    final actions = List<TicketAction>.from(ticketActionEntity?.actions ?? []);
    if (ticketActionEntity == null || actions.isEmpty == true) {
      return const SizedBox();
    }

    if (actions.contains(TicketAction.comment)) {
      actions.remove(TicketAction.comment);
    }

    final hasMoreBtn = actions.length >= 4;

    return Container(
      color: GPColor.bgPrimary,
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
      child: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (actions.isNotEmpty)
              Expanded(
                flex: 5,
                child: _ActionButton(
                  entity: actions.first,
                  isHighlightFirstButton:
                      ticketActionEntity?.highlightFirstButton ?? false,
                  ticketEntity: ticketEntity,
                  ticketNodeEntity: ticketNodeEntity,
                  ticketFlowChartEntity: ticketFlowChartEntity,
                  onClickAction: onTicketActionClick,
                  assignees: assignees,
                  permissions: permissions,
                  followers: followers,
                ),
              ),
            if (actions.length >= 2) const SizedBox(width: 8),
            if (actions.length >= 2)
              Expanded(
                flex: 5,
                child: _ActionButton(
                  entity: actions[1],
                  isHighlightSecondButton:
                      ticketActionEntity?.highlightSecondaryButton ?? false,
                  ticketEntity: ticketEntity,
                  ticketNodeEntity: ticketNodeEntity,
                  ticketFlowChartEntity: ticketFlowChartEntity,
                  onClickAction: onTicketActionClick,
                  assignees: assignees,
                  permissions: permissions,
                  followers: followers,
                ),
              ),
            const SizedBox(width: 8),
            if (hasMoreBtn) ...{
              Expanded(
                flex: 3,
                child: _ActionButton(
                  entity: TicketAction.more,
                  actions: hasMoreBtn ? actions.sublist(2) : [],
                  ticketEntity: ticketEntity,
                  ticketNodeEntity: ticketNodeEntity,
                  ticketFlowChartEntity: ticketFlowChartEntity,
                  onClickAction: onTicketActionClick,
                  assignees: assignees,
                  permissions: permissions,
                  followers: followers,
                ),
              ),
              // }
            } else if (actions.length >= 3) ...{
              Expanded(
                flex: 3,
                child: _ActionButton(
                  entity: actions[2],
                  ticketEntity: ticketEntity,
                  ticketNodeEntity: ticketNodeEntity,
                  ticketFlowChartEntity: ticketFlowChartEntity,
                  onClickAction: onTicketActionClick,
                  assignees: assignees,
                  permissions: permissions,
                  followers: followers,
                ),
              ),
            }
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  const _ActionButton({
    required this.entity,
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.ticketFlowChartEntity,
    this.isHighlightFirstButton = false,
    this.isHighlightSecondButton = false,
    this.actions,
    this.onClickAction,
    this.assignees,
    this.permissions,
    this.followers,
  });

  final TicketAction entity;
  final bool isHighlightFirstButton;
  final bool isHighlightSecondButton;

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketFlowChartEntity ticketFlowChartEntity;
  final List<Assignee>? assignees;
  final List<Assignee>? followers;
  final List<WorkflowFormFieldPermissionEntity>? permissions;

  final List<TicketAction>? actions;
  final OnTicketActionClick? onClickAction;

  Future onTapped(BuildContext context) async {
    final result = await entity.onClick(
      context: context,
      actions: actions,
      ticketEntity: ticketEntity,
      ticketNodeEntity: ticketNodeEntity,
      ticketFlowChartEntity: ticketFlowChartEntity,
      assignees: assignees,
      permissions: permissions,
      followers: followers,
    );

    onClickAction?.call(
      // ignore: use_build_context_synchronously
      context,
      TicketActionBottomSheetEntity(
        ticketEntity: ticketEntity,
        ticketFlowChartEntity: ticketFlowChartEntity,
        ticketNodeEntity: ticketNodeEntity,
      ),
      result,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: isHighlightFirstButton
            ? GPColor.functionPositiveSecondary
            : isHighlightSecondButton
                ? GPColor.functionNegativeSecondary
                : GPColor.bgPrimary,
      ),
      child: InkWell(
        onTap: () => onTapped(context),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (isHighlightFirstButton) ...{
                Column(
                  children: [
                    if (entity.asset?.isNotEmpty == true)
                      SvgWidget(
                        entity.asset ?? '',
                        color: GPColor.greenDark,
                      ),
                    Text(
                      entity.displayName(),
                      style: textStyle(GPTypography.bodySmall)
                          ?.mergeColor(GPColor.greenDark),
                      textAlign: TextAlign.center,
                    )
                  ],
                ),
              } else if (isHighlightSecondButton) ...{
                Column(
                  children: [
                    if (entity.asset?.isNotEmpty == true)
                      SvgWidget(
                        entity.asset ?? '',
                        color: GPColor.functionNegativePrimary,
                      ),
                    Text(
                      entity.displayName(),
                      style: textStyle(GPTypography.bodySmall)
                          ?.mergeColor(GPColor.functionNegativePrimary),
                      textAlign: TextAlign.center,
                    )
                  ],
                ),
              } else ...{
                if (entity.asset?.isNotEmpty == true)
                  SvgWidget(
                    entity.asset ?? '',
                    color: GPColor.contentPrimary,
                  ),
                Text(
                  entity.displayName(),
                  style: textStyle(GPTypography.bodySmall),
                  textAlign: TextAlign.center,
                )
              }
            ],
          ),
        ),
      ),
    );
  }
}
