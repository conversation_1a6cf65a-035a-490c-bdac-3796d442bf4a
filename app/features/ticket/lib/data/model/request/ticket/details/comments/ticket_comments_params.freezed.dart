// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_comments_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketCommentsParams {
  @JsonKey(name: 'version')
  int get version;
  @JsonKey(name: 'after')
  String? get after;
  @JsonKey(name: 'parent_id')
  String? get parentId;
  @JsonKey(name: 'limit')
  int get limit;

  /// Create a copy of TicketCommentsParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TicketCommentsParamsCopyWith<TicketCommentsParams> get copyWith =>
      _$TicketCommentsParamsCopyWithImpl<TicketCommentsParams>(
          this as TicketCommentsParams, _$identity);

  /// Serializes this TicketCommentsParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketCommentsParams &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.after, after) || other.after == after) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.limit, limit) || other.limit == limit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, version, after, parentId, limit);

  @override
  String toString() {
    return 'TicketCommentsParams(version: $version, after: $after, parentId: $parentId, limit: $limit)';
  }
}

/// @nodoc
abstract mixin class $TicketCommentsParamsCopyWith<$Res> {
  factory $TicketCommentsParamsCopyWith(TicketCommentsParams value,
          $Res Function(TicketCommentsParams) _then) =
      _$TicketCommentsParamsCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'version') int version,
      @JsonKey(name: 'after') String? after,
      @JsonKey(name: 'parent_id') String? parentId,
      @JsonKey(name: 'limit') int limit});
}

/// @nodoc
class _$TicketCommentsParamsCopyWithImpl<$Res>
    implements $TicketCommentsParamsCopyWith<$Res> {
  _$TicketCommentsParamsCopyWithImpl(this._self, this._then);

  final TicketCommentsParams _self;
  final $Res Function(TicketCommentsParams) _then;

  /// Create a copy of TicketCommentsParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? version = null,
    Object? after = freezed,
    Object? parentId = freezed,
    Object? limit = null,
  }) {
    return _then(_self.copyWith(
      version: null == version
          ? _self.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      after: freezed == after
          ? _self.after
          : after // ignore: cast_nullable_to_non_nullable
              as String?,
      parentId: freezed == parentId
          ? _self.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketCommentsParams implements TicketCommentsParams {
  _TicketCommentsParams(
      {@JsonKey(name: 'version') this.version = 5,
      @JsonKey(name: 'after') this.after,
      @JsonKey(name: 'parent_id') this.parentId,
      @JsonKey(name: 'limit') this.limit = 20});

  @override
  @JsonKey(name: 'version')
  final int version;
  @override
  @JsonKey(name: 'after')
  final String? after;
  @override
  @JsonKey(name: 'parent_id')
  final String? parentId;
  @override
  @JsonKey(name: 'limit')
  final int limit;

  /// Create a copy of TicketCommentsParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TicketCommentsParamsCopyWith<_TicketCommentsParams> get copyWith =>
      __$TicketCommentsParamsCopyWithImpl<_TicketCommentsParams>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TicketCommentsParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketCommentsParams &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.after, after) || other.after == after) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.limit, limit) || other.limit == limit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, version, after, parentId, limit);

  @override
  String toString() {
    return 'TicketCommentsParams(version: $version, after: $after, parentId: $parentId, limit: $limit)';
  }
}

/// @nodoc
abstract mixin class _$TicketCommentsParamsCopyWith<$Res>
    implements $TicketCommentsParamsCopyWith<$Res> {
  factory _$TicketCommentsParamsCopyWith(_TicketCommentsParams value,
          $Res Function(_TicketCommentsParams) _then) =
      __$TicketCommentsParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'version') int version,
      @JsonKey(name: 'after') String? after,
      @JsonKey(name: 'parent_id') String? parentId,
      @JsonKey(name: 'limit') int limit});
}

/// @nodoc
class __$TicketCommentsParamsCopyWithImpl<$Res>
    implements _$TicketCommentsParamsCopyWith<$Res> {
  __$TicketCommentsParamsCopyWithImpl(this._self, this._then);

  final _TicketCommentsParams _self;
  final $Res Function(_TicketCommentsParams) _then;

  /// Create a copy of TicketCommentsParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? version = null,
    Object? after = freezed,
    Object? parentId = freezed,
    Object? limit = null,
  }) {
    return _then(_TicketCommentsParams(
      version: null == version
          ? _self.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      after: freezed == after
          ? _self.after
          : after // ignore: cast_nullable_to_non_nullable
              as String?,
      parentId: freezed == parentId
          ? _self.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
