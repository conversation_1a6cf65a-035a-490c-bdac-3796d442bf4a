/*
 * Created Date: 4/01/2024 14:52:59
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 20th August 2024 15:16:22
 * Modified By: gapo
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:gp_core/core.dart';
import 'package:gp_shared/domain/entity/entity.dart';
import 'package:gp_shared/mapper/entity/core_entity_mapper.auto_mappr.dart';


@AutoMappr(
  [
    MapType<GPUser, GPUserEntity>(
      fields: [
        Field(
          'id',
          custom: CoreEntityMapper.mapId,
        ),
      ],
    ),
  ],
)
class CoreEntityMapper extends $CoreEntityMapper {
  const CoreEntityMapper();

  static String? mapId(GPUser input) {
    return ParserHelper.parseString(input.id);
  }

  static String? mapAssigneeEntityId(AssigneeEntity input) {
    return ParserHelper.parseString(input.id);
  }

  static String mapIntIdToString(Assignee input) {
    return input.id.toString();
  }

  // static List<BaseFileEntity> mapContent(SelectInviteesOptions input) {
  //   final ret = <BaseFileEntity>[];
  //   const mapper = CoreEntityMapper();
  //   final data = input.content;

  //   if (content == null || content.isEmpty == true) return ret;

  //   if (content is List<FolderResponse>) {
  //     return const DriveEntityMapper()
  //         .convertList<FolderResponse, FolderEntity>(content);
  //   } else if (content is List<FileResponse>) {
  //     return const DriveEntityMapper()
  //         .convertList<FileResponse, FileEntity>(content);
  //   } else if (content is List<UrlResponse>) {
  //     return const DriveEntityMapper()
  //         .convertList<UrlResponse, UrlEntity>(content);
  //   } else {
  //     for (final element in content) {
  //       if (element is FolderResponse) {
  //         ret.add(
  //           mapper.convert<FolderResponse, FolderEntity>(element),
  //         );
  //       } else if (element is FileResponse) {
  //         ret.add(
  //           mapper.convert<FileResponse, FileEntity>(element),
  //         );
  //       } else if (element is UrlResponse) {
  //         ret.add(
  //           mapper.convert<UrlResponse, UrlEntity>(element),
  //         );
  //       } else {
  //         ret.add(
  //           mapper.convert<BaseFileResponse, BaseFileEntity>(element),
  //         );
  //       }
  //     }
  //   }

  //   return ret;
  // }
}
