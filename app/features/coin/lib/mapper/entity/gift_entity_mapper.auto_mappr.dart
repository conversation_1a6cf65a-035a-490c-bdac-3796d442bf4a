// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoMapprGenerator
// **************************************************************************

// ignore_for_file: type=lint, unnecessary_cast, unused_local_variable

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i1;

import '../../data/model/response/gift_exchange_detail_response.dart' as _i4;
import '../../data/model/response/gift_response.dart' as _i2;
import '../../domain/entity/gift.entity.dart' as _i3;
import '../../domain/entity/gift_exchange_detail.entity.dart' as _i5;
import 'gift_entity_mapper.dart' as _i6;

/// Available mappings:
/// - `GiftResponse` → `GiftEntity`.
/// - `GiftExchangeDetailResponse` → `GiftExchangeDetailEntity`.
class $GiftEntityMapper implements _i1.AutoMapprInterface {
  const $GiftEntityMapper();

  Type _typeOf<T>() => T;

  List<_i1.AutoMapprInterface> get _delegates => const [];

  
  
  @override
  bool canConvert<SOURCE, TARGET>({bool recursive = true}) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.GiftResponse>() ||
            sourceTypeOf == _typeOf<_i2.GiftResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.GiftEntity>() ||
            targetTypeOf == _typeOf<_i3.GiftEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.GiftExchangeDetailResponse>() ||
            sourceTypeOf == _typeOf<_i4.GiftExchangeDetailResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.GiftExchangeDetailEntity>() ||
            targetTypeOf == _typeOf<_i5.GiftExchangeDetailEntity?>())) {
      return true;
    }
    if (recursive) {
      for (final mappr in _delegates) {
        if (mappr.canConvert<SOURCE, TARGET>()) {
          return true;
        }
      }
    }
    return false;
  }

  
  
  @override
  TARGET convert<SOURCE, TARGET>(SOURCE? model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _convert(model)!;
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convert(model)!;
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  TARGET? tryConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _safeConvert(
        model,
        onMappingError: onMappingError,
      );
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvert(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    return null;
  }

  
  
  @override
  Iterable<TARGET> convertIterable<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET>((item) => _convert(item)!);
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertIterable(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Iterable<TARGET?> tryConvertIterable<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET?>(
          (item) => _safeConvert(item, onMappingError: onMappingError));
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertIterable(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  List<TARGET> convertList<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertList(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  List<TARGET?> tryConvertList<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertList(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  Set<TARGET> convertSet<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertSet(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Set<TARGET?> tryConvertSet<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertSet(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  TARGET? _convert<SOURCE, TARGET>(
    SOURCE? model, {
    bool canReturnNull = false,
  }) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.GiftResponse>() ||
            sourceTypeOf == _typeOf<_i2.GiftResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.GiftEntity>() ||
            targetTypeOf == _typeOf<_i3.GiftEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$GiftResponse_To__i3$GiftEntity(
          (model as _i2.GiftResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.GiftExchangeDetailResponse>() ||
            sourceTypeOf == _typeOf<_i4.GiftExchangeDetailResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.GiftExchangeDetailEntity>() ||
            targetTypeOf == _typeOf<_i5.GiftExchangeDetailEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$GiftExchangeDetailResponse_To__i5$GiftExchangeDetailEntity(
          (model as _i4.GiftExchangeDetailResponse?)) as TARGET);
    }
    throw Exception('No ${model.runtimeType} -> $targetTypeOf mapping.');
  }

  TARGET? _safeConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (!useSafeMapping<SOURCE, TARGET>()) {
      return _convert(
        model,
        canReturnNull: true,
      );
    }
    try {
      return _convert(
        model,
        canReturnNull: true,
      );
    } catch (e, s) {
      onMappingError?.call(e, s, model);
      return null;
    }
  }

  
  
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return false;
  }

  _i3.GiftEntity _map__i2$GiftResponse_To__i3$GiftEntity(
      _i2.GiftResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GiftResponse → GiftEntity failed because GiftResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GiftResponse, GiftEntity> to handle null values during mapping.');
    }
    return _i3.GiftEntity(
      id: _i6.GiftEntityMapper.mapIdFromGiftResponse(model),
      workspaceId: model.workspaceId,
      name: model.name,
      description: model.description,
      point: model.point,
      quantity: model.quantity,
      remainingQuantity: model.remainingQuantity,
      images: _i6.GiftEntityMapper.mapImages(model),
      status: model.status,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i5.GiftExchangeDetailEntity
      _map__i4$GiftExchangeDetailResponse_To__i5$GiftExchangeDetailEntity(
          _i4.GiftExchangeDetailResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GiftExchangeDetailResponse → GiftExchangeDetailEntity failed because GiftExchangeDetailResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GiftExchangeDetailResponse, GiftExchangeDetailEntity> to handle null values during mapping.');
    }
    return _i5.GiftExchangeDetailEntity(
      id: _i6.GiftEntityMapper.mapIdFromGiftExchangeDetail(model),
      workspaceId: model.workspaceId,
      giftId: model.giftId,
      giftInfo:
          _map__i2$GiftResponse_To__i3$GiftEntity_Nullable(model.giftInfo),
      name: model.name,
      description: model.description,
      point: model.point,
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      deliveryAddress: model.deliveryAddress,
      reason: model.reason,
    );
  }

  _i3.GiftEntity? _map__i2$GiftResponse_To__i3$GiftEntity_Nullable(
      _i2.GiftResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i3.GiftEntity(
      id: _i6.GiftEntityMapper.mapIdFromGiftResponse(model),
      workspaceId: model.workspaceId,
      name: model.name,
      description: model.description,
      point: model.point,
      quantity: model.quantity,
      remainingQuantity: model.remainingQuantity,
      images: _i6.GiftEntityMapper.mapImages(model),
      status: model.status,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }
}
