import 'dart:convert';
import 'dart:math';

import 'package:android_id/android_id.dart';
import 'package:flutter/services.dart';
import 'package:flutter_udid/flutter_udid.dart';
import 'package:gp_core/base/networking/base/api.dart';
import 'package:gp_core/configs/constants.dart';
import 'package:gp_core/configs/token_manager.dart';
import 'package:gp_core/models/token_info/token_info.dart';
import 'package:gp_core/utils/log.dart';
import 'package:gp_shared_dep/gp_shared_dep.dart';

class AuthAPI {
  final ApiService _service =
      ApiService(Constants.apiDomain, gpDomainChecker: GPDomainChecker.api);
  final ApiService _workspacService = ApiService(Constants.workspaceDomain,
      gpDomainChecker: GPDomainChecker.workspace);

  Future<String> getUUId() async {
    // final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    try {
      if (GetPlatform.isAndroid) {
        //UUID for Android
        const androidIdPlugin = AndroidId();
        final String? androidId = await androidIdPlugin.getId();
        return androidId ?? '';
      } else if (GetPlatform.isIOS) {
        // final data = await deviceInfoPlugin.iosInfo;
        // //UUID for iOS
        // String a = data.identifierForVendor ?? '';
        return await FlutterUdid.udid;
      }
    } on PlatformException {
      logDebug('Failed to get platform version');
    }
    return '';
  }

  void updateUUId() async {
    if (Constants.getDeviceId().isEmpty) {
      String deviceId = await getUUId();
      Constants.updateDeviceId(deviceId);
    }
  }

  Future<void> loginAndSetLocalData({
    String? emailArg,
    String? passArg,
  }) async {
    // String email = "<EMAIL>",
    // String pass = "1234567a",

    // String email = '<EMAIL>',
    // String pass = '1234abcd',

    // String email = '<EMAIL>', //nguyenmanhtoan
    // String pass = '1234abcd@',

    // String email = '<EMAIL>',
    // String pass = '12345678a',

    // String email = '<EMAIL>', //nguyenmanhtoan
    // String pass = '1234abcd@',

    // String email = '<EMAIL>',
    // String pass = '1234567ab',
    // String email = '<EMAIL>', //nguyenmanhtoan
    // String pass = '12345678a',
    // <EMAIL>/1234567a
    // String email = '<EMAIL>',
    // String email = '<EMAIL>',
    // String email = '<EMAIL>',
    // String email = "<EMAIL>",
    //String pass = '12345678a',
    // nguyenmanhtoan 1234abcd@
    // String email = '<EMAIL>',
    // String pass = '12345678a',
    // String email = '<EMAIL>',
    // String pass = '12345678a',
    // String email = '<EMAIL>',
    // String pass = '12345678a',
    // String email = '<EMAIL>',
    // String pass = '12345678a',
    // String email = '<EMAIL>',
    // String pass = '12345678a',
    final String email = emailArg ?? '<EMAIL>';
    final String pass = passArg ?? '1234abcd@';

    updateUUId();
    String deviceId = Constants.getDeviceId();
    // final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    // try {
    //   if (GetPlatform.isAndroid) {
    //     //UUID for Android
    //     const androidIdPlugin = AndroidId();
    //     final String? androidId = await androidIdPlugin.getId();
    //     deviceId = androidId ?? '';
    //   } else if (GetPlatform.isIOS) {
    //     final data = await deviceInfoPlugin.iosInfo;
    //     //UUID for iOS
    //     deviceId = data.identifierForVendor ?? '';
    //   }
    //   Constants.updateDeviceId(deviceId);
    // } on PlatformException {
    //   logDebug('Failed to get platform version');
    // }

    if (deviceId == '') {
      deviceId = 'ab596683-3806-4e9c-9c2e-e0500002cacc${Random().nextInt(100)}';
    }

    final checkEmailResponse = await _service.postData(
        endPoint:
            '${Constants.authAPI}${Constants.checkEmailPath}', //checkPhoneNumberPath
        body: {'email': email, 'phone_number': email});

    final salt = checkEmailResponse.data['data']['salt'];

    if (salt is String) {
      var step1 = pass + salt;
      var step2 = sha256.convert(utf8.encode(step1)).toString();
      var step3 = step2 + salt;
      var step4 = sha256.convert(utf8.encode(step3)).toString().toLowerCase();

      final password = step4;

      final loginResponse = await _service.postData(
          endPoint: "${Constants.authAPI}${Constants.loginPath}",
          body: {
            "client_id": "6n6rwo86qmx7u8aahgrq",
            "device_model": "Simulator iPhone 11",
            "device_id": deviceId,
            "password": password,
            "trusted_device": true,
            "email": email,
            // "phone_number": email,
          });

      var userId = loginResponse.data['data']['user_id'].toString();
      var workspaceId = loginResponse.data['data']['workspace_id'].toString();
      var accessToken = loginResponse.data['data']['access_token'].toString();
      var refreshToken = loginResponse.data['data']['refresh_token'].toString();

      var params = <String, dynamic>{
        "userId": userId,
        "displayName": 'FlutterToken.displayName',
        "avatar": 'FlutterToken.userAvatar',
        "accessToken": accessToken,
        "refreshToken": refreshToken,
        "workspaceId": workspaceId,
        "language": Constants.language(),
        "environment": Constants.environment().name //"uat" : "production"
      };

      var tokenInfo = TokenInfo.fromJson(params);
      Constants.updateTokenInfo(tokenInfo);
      await TokenManager.saveTokenInfo(tokenInfo);

      final usersMeResponse =
          await _workspacService.getData(endPoint: Constants.userMePath);

      var displayName = usersMeResponse.data['data']['user']['display_name'];
      var avatar = usersMeResponse.data['data']['user']['avatar'];

      const workSpaceIndex = 0;
      List<dynamic> workspaceIds =
          usersMeResponse.data['data']['user_workspace'];
      // select workspace if if needed
      final ws = workspaceIds.firstWhereOrNull((element) =>
          Map.from(element).values.toString().toLowerCase().contains('gapo'));
      if (ws != null) {
        //
        workspaceId = ws['id'].toString();
      } else if (workspaceIds.length > 1) {
        workspaceId = workspaceIds[workSpaceIndex]['id'].toString();
      } else {
        workspaceId = workspaceIds[workSpaceIndex]['id'].toString();
      }

      // workspaceId = '581860791816317'; // GapoWork staging

      params = {
        "userId": userId,
        "displayName": displayName,
        "avatar": avatar,
        "accessToken": accessToken,
        "refreshToken": refreshToken,
        "workspaceId": workspaceId,
        "language": Constants.language(),
        "environment": Constants.environment().name,
      };

      tokenInfo = TokenInfo.fromJson(params);
      Constants.updateTokenInfo(tokenInfo);
      await TokenManager.saveTokenInfo(tokenInfo);
    }
  }
}
