/*
 * Created Date: 4/01/2024 14:52:59
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 17th March 2025 10:16:53
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs
// ignore_for_file: use_if_null_to_convert_nulls_to_bools

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:gp_feat_coin/data/model/response/gift_exchange_detail_response.dart';
import 'package:gp_feat_coin/data/model/response/gift_response.dart';
import 'package:gp_feat_coin/domain/entity/gift.entity.dart';
import 'package:gp_feat_coin/domain/entity/gift_exchange_detail.entity.dart';
import 'package:gp_feat_coin/domain/entity/gift_image.entity.dart';

import 'gift_entity_mapper.auto_mappr.dart';


@AutoMappr([
  MapType<GiftResponse, GiftEntity>(fields: [
    Field(
      'id',
      custom: GiftEntityMapper.mapIdFromGiftResponse,
    ),
    Field("images", custom: GiftEntityMapper.mapImages),
  ]),
  MapType<GiftExchangeDetailResponse, GiftExchangeDetailEntity>(
    fields: [
      Field("id", custom: GiftEntityMapper.mapIdFromGiftExchangeDetail),
    ],
  ),
])
class GiftEntityMapper extends $GiftEntityMapper {
  const GiftEntityMapper();

  static dynamic mapIdFromGiftResponse(GiftResponse input) {
    return input.id;
  }

  static dynamic mapIdFromGiftExchangeDetail(GiftExchangeDetailResponse input) {
    return input.id;
  }

  static List<GiftImageEntity>? mapImages(GiftResponse input) {
    return input.images
        ?.map((e) => GiftImageEntity(
            id: e.id, imageUrl: e.imageUrl ?? "", fileType: e.fileType))
        .toList();
  }
}

mixin GiftEntityMapperMixin {
  GiftEntity mapToGiftEntity(GiftResponse input) {
    return GiftEntityMapper().convert(input);
  }

  GiftExchangeDetailEntity mapToGiftExchangeDetailEntity(
      GiftExchangeDetailResponse input) {
    return GiftEntityMapper().convert(input);
  }
}
