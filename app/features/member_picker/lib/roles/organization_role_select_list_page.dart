import 'package:flutter/material.dart' hide SearchBar;
import 'package:gp_core/core.dart';

import '../widgets/item_with_tooltip.dart';
import 'organization_role_select_list_controller.dart';

class OrganizationRoleSelectListPage extends StatefulWidget {
  final OrganizationRoleSelectListController controller;

  const OrganizationRoleSelectListPage({
    super.key,
    required this.controller,
  });

  @override
  _OrganizationRoleSelectListPageState createState() =>
      _OrganizationRoleSelectListPageState(controller);
}

class _OrganizationRoleSelectListPageState
    extends State<OrganizationRoleSelectListPage> {
  final OrganizationRoleSelectListController controller;

  _OrganizationRoleSelectListPageState(this.controller);

  @override
  void initState() {
    super.initState();
    controller.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: StreamBuilder<List<OrganizationRole>>(
          stream: controller.selectedRoles.stream,
          builder: (context, snapshot) {
            return NestedScrollView(
                controller: ScrollController()
                  ..addListener(() {
                    controller.hideTooltip();
                    controller.hideSelectedTooltip();
                  }),
                floatHeaderSlivers: true,
                headerSliverBuilder: (context, innerBoxIsScrolled) {
                  return [
                    SliverAppBar(
                      elevation: 0,
                      leading: const SizedBox(),
                      floating: true,
                      flexibleSpace: GPSearchBar(
                        onTap: () {
                          controller.hideTooltip();
                          controller.hideSelectedTooltip();
                        },
                        height: 44,
                        borderRadius: BorderRadius.circular(8),
                        textEditingController:
                            controller.searchTextEditingController,
                        hintText: LocaleKeys.task_search.tr,
                        showClearBtn: true,
                        showBorder: true,
                      ).paddingSymmetric(horizontal: 12).paddingOnly(top: 10),
                    ),
                    if (snapshot.hasData &&
                        (snapshot.data?.isNotEmpty ?? false) &&
                        (controller.svo.mode !=
                            SelectInviteesOptionsMode.viewOnly))
                      SliverToBoxAdapter(
                        child: Container(
                          color: GPColor.functionAlwaysLightPrimary,
                          // height: 44,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Builder(builder: (context) {
                                List<Widget> listWidget = [];
                                for (int i = 0;
                                    i < snapshot.data!.length;
                                    i++) {
                                  var item = snapshot.data![i];
                                  listWidget.add(_SelectedItemWrapper(
                                    role: item,
                                    onTap: item.isEnabled
                                        ? () => controller.onRemove(i)
                                        : null,
                                    tooltip:
                                        controller.tooltipSelectedControllers[
                                            item.id.toString()],
                                  ).paddingOnly(bottom: 8));
                                }
                                return Wrap(
                                  children: listWidget,
                                ).paddingSymmetric(horizontal: 16);
                              })
                            ],
                          ),
                        ).paddingOnly(top: 12),
                      ),
                  ];
                },
                body: StreamBuilder<List<OrganizationRole>>(
                  stream: controller.displayRoles.stream,
                  builder: (context, snapshot) {
                    if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                      return ListView.builder(
                        keyboardDismissBehavior:
                            ScrollViewKeyboardDismissBehavior.onDrag,
                        itemCount: snapshot.data!.length,
                        itemBuilder: (context, index) {
                          final item = snapshot.data![index];
                          return _RoleRow(
                            item,
                            onSelectionChanged: controller.onSelectionChanged,
                            showCheckBox: controller.svo.showCheckBox,
                            tooltip: controller.tooltipControllers[item.id],
                          );
                        },
                      ).paddingOnly(left: 16);
                    } else if (controller.isLoading) {
                      return SizedBox(
                        height: 50,
                        child: Center(
                          child: CircularProgressIndicator(
                              color: GPColor.workPrimary),
                        ),
                      );
                    } else {
                      if (controller
                          .searchTextEditingController.text.isNotEmpty) {
                        return EmptyView(emptyText: LocaleKeys.error_nodata.tr);
                      } else {
                        return EmptyView(emptyText: controller.svo.emptyText());
                      }
                    }
                  },
                ));
          }),
    );
  }
}

class _RoleRow extends StatelessWidget {
  const _RoleRow(
    this.role, {
    required this.onSelectionChanged,
    required this.showCheckBox,
    this.tooltip,
  });

  final OrganizationRole role;
  final Function(OrganizationRole) onSelectionChanged;
  final bool showCheckBox;

  final AssigneeTooltipModel? tooltip;

  @override
  Widget build(BuildContext context) {
    return WidgetWithTooltip(
      avatarWidget: InkWell(
        onTap: role.isEnabled
            ? () {
                role.isSelected = !role.isSelected;
                onSelectionChanged(role);
              }
            : null,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SvgWidget(
                  'assets/images/svg/ic24-fill-briefcase.svg',
                  color: GPColor.contentSecondary,
                ),
                const SizedBox(
                  width: 12,
                ),
                Expanded(
                    child: Text(
                  role.name ?? '',
                  style: textStyle(GPTypography.bodyLarge),
                )),
                GPCheckBox(
                  isSelected: role.isSelected,
                  isEnabled: role.isEnabled,
                  onCheckedChanged: (value) {
                    // update data and callback
                    role.isSelected = value;
                    onSelectionChanged(role);
                  },
                  isCircleShape: !showCheckBox,
                ),
              ],
            ).paddingSymmetric(vertical: 6),
            const Divider(height: 1).paddingOnly(left: 38, right: 8)
          ],
        ),
      ),
      tooltip: tooltip,
      arrowTipDistance: 22,
      dynamicLeft: false,
    );
  }
}

class _SelectedItemWrapper extends StatelessWidget {
  const _SelectedItemWrapper({
    required this.role,
    required this.onTap,
    this.tooltip,
  });

  final OrganizationRole role;
  final void Function()? onTap;

  final AssigneeTooltipModel? tooltip;

  @override
  Widget build(BuildContext context) {
    return WidgetWithTooltip(
      avatarWidget: _SelectedItem(
        role: role,
        onTap: onTap,
      ),
      tooltip: tooltip,
      arrowTipDistance: 22,
      dynamicLeftValue: rxRoleTooltipLeft,
    );
  }
}

class _SelectedItem extends StatelessWidget {
  final OrganizationRole role;
  final void Function()? onTap;

  const _SelectedItem({
    required this.role,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: Container(
          padding: const EdgeInsets.all(8),
          // height: 32,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: GPColor.orangeLight,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  role.name ?? '',
                  style: textStyle(GPTypography.bodyMedium)
                      ?.copyWith(color: GPColor.orangeDark, height: 1),
                ),
              ),
              const SizedBox(width: 8),
              if (role.isEnabled)
                SizedBox(
                    width: 16,
                    height: 16,
                    child: SvgWidget(
                      'assets/images/svg/ic16-fill-xmark.svg',
                      color: GPColor.orangeDark,
                      fit: BoxFit.scaleDown,
                    )),
            ],
          ),
        ),
      ),
    );
  }
}
