#!/usr/bin/env python3
"""
Script to remove all triple slash comments (///) from Dart files
Works on current project and pub cache folder
"""

import os
import re
import sys
import argparse
from pathlib import Path
from typing import List, Tuple
import tempfile
import shutil

class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def print_info(message: str):
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

def print_success(message: str):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def print_warning(message: str):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def print_error(message: str):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def remove_triple_slash_comments(file_path: str, dry_run: bool = False) -> bool:
    """
    Remove triple slash comments from a Dart file.
    Returns True if file was modified, False otherwise.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                lines = f.readlines()
        except Exception as e:
            print_error(f"Could not read file {file_path}: {e}")
            return False
    except Exception as e:
        print_error(f"Could not read file {file_path}: {e}")
        return False

    modified_lines = []
    file_modified = False
    in_multiline_comment = False
    in_string = False
    
    for line_num, line in enumerate(lines, 1):
        original_line = line
        modified_line = ""
        i = 0
        
        while i < len(line):
            char = line[i]
            
            # Handle string literals (skip processing inside strings)
            if char in ['"', "'"]:
                if not in_multiline_comment:
                    quote_char = char
                    modified_line += char
                    i += 1
                    # Find the end of the string
                    while i < len(line):
                        char = line[i]
                        modified_line += char
                        if char == quote_char and (i == 0 or line[i-1] != '\\'):
                            break
                        i += 1
                    i += 1
                    continue
            
            # Handle multi-line comments /* */
            if not in_string and i < len(line) - 1:
                if line[i:i+2] == '/*':
                    in_multiline_comment = True
                    modified_line += line[i:i+2]
                    i += 2
                    continue
                elif line[i:i+2] == '*/' and in_multiline_comment:
                    in_multiline_comment = False
                    modified_line += line[i:i+2]
                    i += 2
                    continue
            
            # Handle triple slash comments
            if not in_multiline_comment and not in_string and i < len(line) - 2:
                if line[i:i+3] == '///':
                    # Check if this is at the start of line (with optional whitespace)
                    prefix = line[:i].strip()
                    if not prefix:
                        # This is a line that starts with /// - remove entire line
                        file_modified = True
                        break
                    else:
                        # This is /// at the end of a line - remove from /// to end
                        modified_line = line[:i].rstrip() + '\n'
                        file_modified = True
                        break
            
            modified_line += char
            i += 1
        else:
            # If we didn't break out of the loop, use the modified line
            if modified_line != original_line:
                file_modified = True
            modified_lines.append(modified_line)
            continue
        
        # If we broke out of the loop due to /// at start of line, skip this line
        if line[:i].strip() == '' and i < len(line) - 2 and line[i:i+3] == '///':
            continue
        else:
            modified_lines.append(modified_line)
    
    if file_modified and not dry_run:
        try:
            # Write to temporary file first
            with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', delete=False) as temp_file:
                temp_file.writelines(modified_lines)
                temp_file_path = temp_file.name
            
            # Replace original file
            shutil.move(temp_file_path, file_path)
            return True
        except Exception as e:
            print_error(f"Could not write to file {file_path}: {e}")
            return False
    
    return file_modified

def find_dart_files(directory: str) -> List[str]:
    """Find all .dart files in a directory recursively."""
    dart_files = []
    try:
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.dart'):
                    dart_files.append(os.path.join(root, file))
    except Exception as e:
        print_error(f"Error scanning directory {directory}: {e}")
    
    return dart_files

def process_directory(directory: str, description: str, dry_run: bool = False) -> Tuple[int, int]:
    """
    Process all Dart files in a directory.
    Returns (total_files, modified_files)
    """
    print_info(f"Processing {description}: {directory}")
    
    if not os.path.isdir(directory):
        print_warning(f"Directory does not exist: {directory}")
        return 0, 0
    
    dart_files = find_dart_files(directory)
    modified_count = 0
    
    for file_path in dart_files:
        if remove_triple_slash_comments(file_path, dry_run):
            modified_count += 1
            if dry_run:
                print_info(f"Would modify: {file_path}")
            else:
                print_info(f"Modified: {file_path}")
    
    if dry_run:
        print_success(f"{description}: Found {len(dart_files)} files, {modified_count} would be modified")
    else:
        print_success(f"{description}: Processed {len(dart_files)} files, modified {modified_count} files")
    
    return len(dart_files), modified_count

def main():
    parser = argparse.ArgumentParser(
        description="Remove all triple slash comments (///) from Dart files"
    )
    parser.add_argument(
        '-p', '--project',
        action='store_true',
        help='Process only current project (skip pub cache)'
    )
    parser.add_argument(
        '-c', '--cache',
        action='store_true',
        help='Process only pub cache (skip current project)'
    )
    parser.add_argument(
        '-d', '--dry-run',
        action='store_true',
        help='Show what would be modified without making changes'
    )
    
    args = parser.parse_args()
    
    # Default behavior: process both project and cache
    process_project = True
    process_cache = True
    
    if args.project:
        process_project = True
        process_cache = False
    elif args.cache:
        process_project = False
        process_cache = True
    
    if args.dry_run:
        print_info("DRY RUN MODE - No files will be modified")
    
    print_info("Starting triple slash comment removal process...")
    
    total_files = 0
    total_modified = 0
    
    # Process current project
    if process_project:
        print_info("=== Processing Current Project ===")
        current_dir = os.getcwd()
        files, modified = process_directory(current_dir, "Current Project", args.dry_run)
        total_files += files
        total_modified += modified
    
    # Process pub cache
    if process_cache:
        print_info("=== Processing Pub Cache ===")
        pub_cache_dir = os.path.expanduser("~/.pub-cache/hosted/pub.dev")
        
        if os.path.isdir(pub_cache_dir):
            for package_dir in os.listdir(pub_cache_dir):
                package_path = os.path.join(pub_cache_dir, package_dir)
                if os.path.isdir(package_path):
                    files, modified = process_directory(
                        package_path, 
                        f"Pub Cache Package: {package_dir}", 
                        args.dry_run
                    )
                    total_files += files
                    total_modified += modified
        else:
            print_warning(f"Pub cache directory not found: {pub_cache_dir}")
    
    if args.dry_run:
        print_success(f"Triple slash comment removal preview completed!")
        print_success(f"Total: {total_files} files found, {total_modified} would be modified")
    else:
        print_success(f"Triple slash comment removal completed!")
        print_success(f"Total: {total_files} files processed, {total_modified} files modified")

if __name__ == "__main__":
    main()
