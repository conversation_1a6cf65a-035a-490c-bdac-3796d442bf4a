// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'workflow_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkFlowParams {
  /// 1 -> true, 0 -> false
  int get published;

  ///
  int get limit;

  ///
  @JsonKey(name: 'get_of_child_group')
  bool get getOfChildGroup;

  ///
  @JsonKey(name: 'workflow_group_id')
  String get workflowGroupId;

  ///
  String get q;

  ///
  @JsonKey(name: 'include_grandchildren')
  bool get includeGrandchildren;

  ///
  @JsonKey(name: 'offset')
  int? get offset;

  /// Create a copy of WorkFlowParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkFlowParamsCopyWith<WorkFlowParams> get copyWith =>
      _$WorkFlowParamsCopyWithImpl<WorkFlowParams>(
          this as WorkFlowParams, _$identity);

  /// Serializes this WorkFlowParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkFlowParams &&
            (identical(other.published, published) ||
                other.published == published) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.getOfChildGroup, getOfChildGroup) ||
                other.getOfChildGroup == getOfChildGroup) &&
            (identical(other.workflowGroupId, workflowGroupId) ||
                other.workflowGroupId == workflowGroupId) &&
            (identical(other.q, q) || other.q == q) &&
            (identical(other.includeGrandchildren, includeGrandchildren) ||
                other.includeGrandchildren == includeGrandchildren) &&
            (identical(other.offset, offset) || other.offset == offset));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, published, limit,
      getOfChildGroup, workflowGroupId, q, includeGrandchildren, offset);

  @override
  String toString() {
    return 'WorkFlowParams(published: $published, limit: $limit, getOfChildGroup: $getOfChildGroup, workflowGroupId: $workflowGroupId, q: $q, includeGrandchildren: $includeGrandchildren, offset: $offset)';
  }
}

/// @nodoc
abstract mixin class $WorkFlowParamsCopyWith<$Res> {
  factory $WorkFlowParamsCopyWith(
          WorkFlowParams value, $Res Function(WorkFlowParams) _then) =
      _$WorkFlowParamsCopyWithImpl;
  @useResult
  $Res call(
      {int published,
      int limit,
      @JsonKey(name: 'get_of_child_group') bool getOfChildGroup,
      @JsonKey(name: 'workflow_group_id') String workflowGroupId,
      String q,
      @JsonKey(name: 'include_grandchildren') bool includeGrandchildren,
      @JsonKey(name: 'offset') int? offset});
}

/// @nodoc
class _$WorkFlowParamsCopyWithImpl<$Res>
    implements $WorkFlowParamsCopyWith<$Res> {
  _$WorkFlowParamsCopyWithImpl(this._self, this._then);

  final WorkFlowParams _self;
  final $Res Function(WorkFlowParams) _then;

  /// Create a copy of WorkFlowParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? published = null,
    Object? limit = null,
    Object? getOfChildGroup = null,
    Object? workflowGroupId = null,
    Object? q = null,
    Object? includeGrandchildren = null,
    Object? offset = freezed,
  }) {
    return _then(_self.copyWith(
      published: null == published
          ? _self.published
          : published // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      getOfChildGroup: null == getOfChildGroup
          ? _self.getOfChildGroup
          : getOfChildGroup // ignore: cast_nullable_to_non_nullable
              as bool,
      workflowGroupId: null == workflowGroupId
          ? _self.workflowGroupId
          : workflowGroupId // ignore: cast_nullable_to_non_nullable
              as String,
      q: null == q
          ? _self.q
          : q // ignore: cast_nullable_to_non_nullable
              as String,
      includeGrandchildren: null == includeGrandchildren
          ? _self.includeGrandchildren
          : includeGrandchildren // ignore: cast_nullable_to_non_nullable
              as bool,
      offset: freezed == offset
          ? _self.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _WorkFlowParams implements WorkFlowParams {
  _WorkFlowParams(
      {this.published = 1,
      this.limit = 10,
      @JsonKey(name: 'get_of_child_group') this.getOfChildGroup = true,
      @JsonKey(name: 'workflow_group_id') this.workflowGroupId = '',
      this.q = '',
      @JsonKey(name: 'include_grandchildren') this.includeGrandchildren = true,
      @JsonKey(name: 'offset') this.offset = 0});

  /// 1 -> true, 0 -> false
  @override
  @JsonKey()
  final int published;

  ///
  @override
  @JsonKey()
  final int limit;

  ///
  @override
  @JsonKey(name: 'get_of_child_group')
  final bool getOfChildGroup;

  ///
  @override
  @JsonKey(name: 'workflow_group_id')
  final String workflowGroupId;

  ///
  @override
  @JsonKey()
  final String q;

  ///
  @override
  @JsonKey(name: 'include_grandchildren')
  final bool includeGrandchildren;

  ///
  @override
  @JsonKey(name: 'offset')
  final int? offset;

  /// Create a copy of WorkFlowParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkFlowParamsCopyWith<_WorkFlowParams> get copyWith =>
      __$WorkFlowParamsCopyWithImpl<_WorkFlowParams>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkFlowParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkFlowParams &&
            (identical(other.published, published) ||
                other.published == published) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.getOfChildGroup, getOfChildGroup) ||
                other.getOfChildGroup == getOfChildGroup) &&
            (identical(other.workflowGroupId, workflowGroupId) ||
                other.workflowGroupId == workflowGroupId) &&
            (identical(other.q, q) || other.q == q) &&
            (identical(other.includeGrandchildren, includeGrandchildren) ||
                other.includeGrandchildren == includeGrandchildren) &&
            (identical(other.offset, offset) || other.offset == offset));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, published, limit,
      getOfChildGroup, workflowGroupId, q, includeGrandchildren, offset);

  @override
  String toString() {
    return 'WorkFlowParams(published: $published, limit: $limit, getOfChildGroup: $getOfChildGroup, workflowGroupId: $workflowGroupId, q: $q, includeGrandchildren: $includeGrandchildren, offset: $offset)';
  }
}

/// @nodoc
abstract mixin class _$WorkFlowParamsCopyWith<$Res>
    implements $WorkFlowParamsCopyWith<$Res> {
  factory _$WorkFlowParamsCopyWith(
          _WorkFlowParams value, $Res Function(_WorkFlowParams) _then) =
      __$WorkFlowParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int published,
      int limit,
      @JsonKey(name: 'get_of_child_group') bool getOfChildGroup,
      @JsonKey(name: 'workflow_group_id') String workflowGroupId,
      String q,
      @JsonKey(name: 'include_grandchildren') bool includeGrandchildren,
      @JsonKey(name: 'offset') int? offset});
}

/// @nodoc
class __$WorkFlowParamsCopyWithImpl<$Res>
    implements _$WorkFlowParamsCopyWith<$Res> {
  __$WorkFlowParamsCopyWithImpl(this._self, this._then);

  final _WorkFlowParams _self;
  final $Res Function(_WorkFlowParams) _then;

  /// Create a copy of WorkFlowParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? published = null,
    Object? limit = null,
    Object? getOfChildGroup = null,
    Object? workflowGroupId = null,
    Object? q = null,
    Object? includeGrandchildren = null,
    Object? offset = freezed,
  }) {
    return _then(_WorkFlowParams(
      published: null == published
          ? _self.published
          : published // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      getOfChildGroup: null == getOfChildGroup
          ? _self.getOfChildGroup
          : getOfChildGroup // ignore: cast_nullable_to_non_nullable
              as bool,
      workflowGroupId: null == workflowGroupId
          ? _self.workflowGroupId
          : workflowGroupId // ignore: cast_nullable_to_non_nullable
              as String,
      q: null == q
          ? _self.q
          : q // ignore: cast_nullable_to_non_nullable
              as String,
      includeGrandchildren: null == includeGrandchildren
          ? _self.includeGrandchildren
          : includeGrandchildren // ignore: cast_nullable_to_non_nullable
              as bool,
      offset: freezed == offset
          ? _self.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

// dart format on
