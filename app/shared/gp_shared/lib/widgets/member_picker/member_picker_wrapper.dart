/*
 * Created Date: Saturday, 6th April 2024, 14:41:59
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Saturday, 6th April 2024 14:50:19
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/models/bot/bot_response.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:gp_shared/domain/entity/entity.dart';
import 'package:gp_shared/domain/usecase/assignee_picker/assignee_picker.usecase.dart';
import 'package:gp_shared/mapper/gp_mapper.dart';
import 'package:injectable/injectable.dart';

import 'member_picker.dart';

@Singleton(order: DiConstants.kLastestOrder)
final class MemberPickerWrapper {
  late final AssigneePickerUseCase _pickerUseCase =
      GetIt.I<AssigneePickerUseCase>();

  final GPMapper mapper = GetIt.I<GPMapper>(instanceName: 'kGPMapper');

  SelectMemberEntity convertToSelectMemberEntity(
      SelectInviteesOptions selectInviteesOptions) {
    return mapper.convert<SelectInviteesOptions, SelectMemberEntity>(
        selectInviteesOptions);
  }

  Future<MemberPickerResult<T>?> pickMember<T extends SelectMemberEntity>(
    MemberPickerParams<T> params,
  ) async {
    final SelectInviteesOptions newOption = params.selecMemberEntity != null
        ? params.options.fromSelectMemberEntity(params.selecMemberEntity!)
        : params.options;

    final result = await _pickerUseCase.execute(
      AssigneePickerInput(newOption),
    );

    return MemberPickerResult(
      result: result.result,
      resultEntity: mapper.convert<SelectInviteesOptions, T>(result.result),
      pickedGPUserEntities: _convertResultToGPUserEntity(result.result),
    );
  }

  List<GPUserEntity> _convertResultToGPUserEntity(
      SelectInviteesOptions result) {
    if (result.selectedMembers?.isEmpty == true) {
      return [];
    }

    return (result.selectedMembers ?? [])
        .map(
          (e) => mapper.convert<Assignee, GPUserEntity>(e),
        )
        .toList();
  }
}

extension SelectInviteesOptionsFromEntityExt on SelectInviteesOptions {
  SelectInviteesOptions fromSelectMemberEntity(SelectMemberEntity entity) {
    final GPMapper mapper = GetIt.I<GPMapper>(instanceName: 'kGPMapper');

    final isEditting = mode == SelectInviteesOptionsMode.edit;

    final sConfigs = {
      ...?this.sConfigs,
      "show_btn_view_selected_members": false,
      "need_sync_ignore_remove_members": false,
      "default_can_press_done": false,
    };

    return SelectInviteesOptions(
      title: title,
      actionButtonTitle: actionButtonTitle,
      mode: mode,
      tabs: tabs,
      selectedMembers: isEditting
          ? null
          : entity.assigneeEntities != null
              ? mapper.convertList<AssigneeEntity, Assignee>(
                  entity.assigneeEntities!)
              : null,
      selectedDepartments: isEditting
          ? null
          : entity.departmentEntities != null
              ? mapper.convertList<OrganizationDepartmentEntity,
                  OrganizationDepartment>(entity.departmentEntities!)
              : null,
      selectedRoles: isEditting
          ? null
          : entity.roleEntities != null
              ? mapper.convertList<OrganizationRoleEntity, OrganizationRole>(
                  entity.roleEntities!)
              : null,
      selectedThreads: entity.conversationEntities != null
          ? mapper.convertList<ConversationEntity, Conversation>(
              entity.conversationEntities!)
          : null,
      selectedBots: entity.chatBotEntities != null
          ? mapper
              .convertList<ChatBotEntity, ChatBotModel>(entity.chatBotEntities!)
          : null,
      ignoreUsers: ignoreUsers,
      selectedMemberIds: isEditting
          ? null
          : entity.assigneeEntities?.map((e) => e.id).toList(),
      selectedDepartmentIds: isEditting
          ? null
          : entity.departmentEntities?.map((e) => e.id).toList(),
      selectedThreadIds:
          entity.conversationEntities?.map((e) => '${e.id}').toList(),
      selectedRoleIds: isEditting
          ? null
          : entity.roleEntities?.map((e) => e.id ?? '').toList(),
      selectedBotIds: entity.chatBotEntities?.map((e) => e.id).toList(),
      ignoreUserIds: null,
      notRemovableUserIds: isEditting
          ? entity.assigneeEntities?.map((e) => e.id).toList()
          : null,
      arguments: arguments,
      selectInviteesPickMode: selectInviteesPickMode,
      sConfigs: sConfigs,
      memberConfigs: memberConfigs,
      filterOutMemberIds: filterOutMemberIds,
      notRemovable: {
        "department_ids": isEditting
            ? entity.departmentEntities?.map((e) => e.id).toList()
            : null,
        "role_ids":
            isEditting ? entity.roleEntities?.map((e) => e.id).toList() : null,
      },
      addedMyAddminIds: addedMyAddminIds,
      onBackPressed: onBackPressed,
      onlyCurrentWorkspace: onlyCurrentWorkspace,
      fromNative: fromNative,
      needGetAllSelectedMembers: needGetAllSelectedMembers,
      closeOverlayWhenBack: closeOverlayWhenBack,
    );
  }
}
