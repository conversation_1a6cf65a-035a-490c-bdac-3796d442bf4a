// ignore_for_file: invalid_use_of_visible_for_testing_member, invalid_use_of_protected_member

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_shared/domain/entity/entity.dart';
import 'package:gp_shared/widgets/attachment/attachment.dart';

import '../../../../widgets/base/base_ticket_input_behavior.dart';
import '../../../details/edit/ticket_details_data_checker_mixin.dart';
import 'picker/base/attachment_upload_state.dart';
import 'base_form_input.dart';
import 'picker/base/base_input_picker.dart';
import 'picker/base/base_input_picker_widget.dart';

typedef E = GPAttachmentFileEntity;
typedef _P = TicketCreatePickerParams<E>;

// ignore: must_be_immutable
final class TicketCreateInputAttachmentWidget
    extends TicketCreateInputBaseInputWrapperWidget<
        TicketCreatePickerParams<E>> {
  TicketCreateInputAttachmentWidget({
    super.key,
    required super.params,
    required super.inputBehavior,
    super.readyOnlyWidget,
    this.isCreatingTicket = false,
  }) {
    inputWidget = _TicketCreateInputAttachmentWidget(
      inputBehavior: inputBehavior,
      params: params,
      isCreatingTicket: isCreatingTicket,
    );
  }

  final bool isCreatingTicket;
}

const _itemWidth = 40.0;

const _previewSize = GpAttachmentFilePreviewSize(
  width: _itemWidth,
  height: _itemWidth,
);

class _TicketCreateInputAttachmentWidget
    extends TicketCreateBasePickerWrapperWidget<E, _P> {
  const _TicketCreateInputAttachmentWidget({
    required super.inputBehavior,
    required super.params,
    required super.isCreatingTicket,
  });

  @override
  State<_TicketCreateInputAttachmentWidget> createState() =>
      _TicketCreateInputAttachmentWidgetState();
}

class _TicketCreateInputAttachmentWidgetState
    extends TicketCreateBaseAttachmentPickerWrapperState<
        _TicketCreateInputAttachmentWidget,
        _P,
        E> implements TicketCreatePickerBehavior<E> {
  @override
  bool get canEdit =>
      widget.params.permissions?.isFieldCanEdit(
          isCreatingTicket: widget.isCreatingTicket, hasValue: hasInitData) ==
      true;

  @override
  void initState() {
    super.initState();
    hasInitData = params.data.isNotEmpty;
  }

  void onRemove(E item) {
    if (!canEdit) return;
    params.data.remove(item);

    rxParams.value.data.remove(item);

    rxParams.notifyListeners();

    ticketEditDeletedIds.add(item.id);
  }

  @override
  void didUpdateWidget(covariant _TicketCreateInputAttachmentWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    rxParams.value.isReadOnly = widget.params.isReadOnly;
    rxParams.value.title = widget.params.title;
    rxParams.value.subTitle = widget.params.subTitle;

    if (oldWidget.params != widget.params ||
        (widget.isCreatingTicket == false &&
            ListEquality().equals(widget.params.data, oldWidget.params.data) ==
                false)) {
      params = widget.params;
      rxParams.value = params;
      rxParams.value.data
        ..clear()
        ..addAll(widget.params.data);
      rxParams.notifyListeners();
    }
  }

  @override
  Future<List<E>> onAddData() async {
    final Completer<List<E>> completer = Completer();

    var pickerResult = await GPPicker.instance.pick(FileType.any);

    if (pickerResult != null) {
      final attachmentEntities = attachmentEntitiesFromPicker(
        pickerResult,
        FileType.any,
        uploadCallback,
      );

      params.data.addAll(attachmentEntities);

      rxCurrentValidateMode.value =
          BaseTicketInputValidateMode.validateFromAnotherAction;

      rxParams.value = params;
      rxParams.notifyListeners();
    }

    completer.complete(rxParams.value.data);

    // await Popup.instance.showBottomSheet(
    //   FilePickerBottomSheet(
    //     fileTypes: const [
    //       FileType.any,
    //     ],
    //     onSelected: (fileType) async {
    //       var pickerResult = await GPPicker.instance.pick(fileType);

    //       if (pickerResult != null) {
    //         final attachmentEntities = attachmentEntitiesFromPicker(
    //           pickerResult,
    //           fileType,
    //           uploadCallback,
    //         );

    //         rxParams.value.data.addAll(attachmentEntities);

    //         rxCurrentValidateMode.value =
    //             BaseTicketInputValidateMode.validateFromAnotherAction;

    //         rxParams.notifyListeners();
    //       }

    //       completer.complete(rxParams.value.data);
    //     },
    //   ),
    // );

    return completer.future;
  }

  @override
  Widget buildDataWidget(List<E> data, int displayCount) {
    int newDisplayCount = displayCount;
    final dataLength = data.length;
    if (dataLength < displayCount) {
      newDisplayCount = dataLength;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildWhenHasNoDataWidget(),
        TicketAttachmentsWidget(
          isReadOnly: params.isReadOnly || !canEdit,
          data: data,
          displayCount: newDisplayCount,
          onRemoveFunc: onRemove,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // temp
    // rxParams.value = widget.params;
    return ValueListenableBuilder(
      valueListenable: rxParams,
      builder: (context, value, child) {
        return widget.params.permissions?.canView == true
            ? TicketCreateBasePickerWidget(
                inputBehavior: widget.inputBehavior,
                attachmentBehavior: this,
                params: value,
                listViewType: TicketBaseAttachmentListViewType.list,
                isCreatingTicket: widget.isCreatingTicket,
                hasInitData: hasInitData,
              )
            : const SizedBox();
      },
    );
  }
}

class TicketAttachmentsWidget extends StatelessWidget {
  const TicketAttachmentsWidget({
    super.key,
    required this.data,
    required this.displayCount,
    required this.onRemoveFunc,
    required this.isReadOnly,
  });

  final List<E> data;
  final int displayCount;
  final Function(E item) onRemoveFunc;

  final bool isReadOnly;

  void goToMediaPreviewList(int index) async {
    final List<GMedia> medias = data.map((e) => e.toGMedia()).toList();
    Utils.openMediaPreview(
      medias,
      initialIndex: index,
    );
  }

  void onRemove(E item) {
    data.remove(item);

    onRemoveFunc.call(item);
  }

  @override
  Widget build(BuildContext context) {
    return isReadOnly && data.isEmpty
        ? const SizedBox(height: 8)
        : ListView.builder(
            physics: const ClampingScrollPhysics(),
            shrinkWrap: true,
            itemCount: displayCount,
            padding: const EdgeInsets.all(0),
            itemBuilder: (context, index) {
              final item = data[index];

              return InkWell(
                onTap: () => goToMediaPreviewList(index),
                child: Row(
                  children: [
                    Expanded(
                      child: GPAttachmentFilePreviewHorizontal(
                          item: item,
                          previewSize: _previewSize,
                          onRemove: () => onRemove(item)),
                    ),
                    if (!isReadOnly)
                      InkWell(
                        customBorder: const CircleBorder(),
                        onTap: () => onRemove(item),
                        child: SvgWidget(
                          Assets
                              .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_FILL_XMARK_SVG,
                          color: GPColor.contentSecondary,
                        ).paddingAll(12),
                      )
                  ],
                ),
              );
            },
          );
  }
}
