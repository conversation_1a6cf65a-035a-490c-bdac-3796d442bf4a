#!/bin/bash

# Script to remove all triple slash comments (///) from Dart files
# Works on current project and pub cache folder

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to remove triple slash comments from a file
remove_triple_slash_comments() {
    local file="$1"
    local temp_file=$(mktemp)
    
    # Use sed to remove lines that start with /// (with optional whitespace)
    # Also remove /// comments at the end of lines
    sed -E '
        # Remove lines that start with optional whitespace followed by ///
        /^[[:space:]]*\/\/\//d
        # Remove /// comments at the end of lines (keep the code part)
        s/[[:space:]]*\/\/\/.*$//
    ' "$file" > "$temp_file"
    
    # Check if the file was actually modified
    if ! cmp -s "$file" "$temp_file"; then
        mv "$temp_file" "$file"
        return 0  # File was modified
    else
        rm "$temp_file"
        return 1  # File was not modified
    fi
}

# Function to process files in a directory
process_directory() {
    local dir="$1"
    local description="$2"
    local modified_count=0
    local total_count=0
    
    print_info "Processing $description: $dir"
    
    if [ ! -d "$dir" ]; then
        print_warning "Directory does not exist: $dir"
        return
    fi
    
    # Find all .dart files and process them
    while IFS= read -r -d '' file; do
        ((total_count++))
        if remove_triple_slash_comments "$file"; then
            ((modified_count++))
            print_info "Modified: $file"
        fi
    done < <(find "$dir" -name "*.dart" -type f -print0 2>/dev/null)
    
    print_success "$description: Processed $total_count files, modified $modified_count files"
}

# Main execution
main() {
    print_info "Starting triple slash comment removal process..."
    
    # Get current directory
    CURRENT_DIR=$(pwd)
    print_info "Current directory: $CURRENT_DIR"
    
    # Process current project
    print_info "=== Processing Current Project ==="
    process_directory "$CURRENT_DIR" "Current Project"
    
    # Process pub cache
    print_info "=== Processing Pub Cache ==="
    PUB_CACHE_DIR="$HOME/.pub-cache/hosted/pub.dev"
    
    if [ -d "$PUB_CACHE_DIR" ]; then
        # Get list of all package directories in pub cache
        for package_dir in "$PUB_CACHE_DIR"/*; do
            if [ -d "$package_dir" ]; then
                package_name=$(basename "$package_dir")
                process_directory "$package_dir" "Pub Cache Package: $package_name"
            fi
        done
    else
        print_warning "Pub cache directory not found: $PUB_CACHE_DIR"
    fi
    
    print_success "Triple slash comment removal completed!"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Remove all triple slash comments (///) from Dart files"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -p, --project  Process only current project (skip pub cache)"
    echo "  -c, --cache    Process only pub cache (skip current project)"
    echo "  -d, --dry-run  Show what would be modified without making changes"
    echo ""
    echo "Examples:"
    echo "  $0                    # Process both project and pub cache"
    echo "  $0 --project          # Process only current project"
    echo "  $0 --cache            # Process only pub cache"
    echo "  $0 --dry-run          # Preview changes without modifying files"
}

# Parse command line arguments
PROCESS_PROJECT=true
PROCESS_CACHE=true
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -p|--project)
            PROCESS_PROJECT=true
            PROCESS_CACHE=false
            shift
            ;;
        -c|--cache)
            PROCESS_PROJECT=false
            PROCESS_CACHE=true
            shift
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Dry run function
dry_run_check() {
    local file="$1"
    
    # Check if file contains triple slash comments
    if grep -q '^[[:space:]]*///' "$file" || grep -q '[[:space:]]*///.*$' "$file"; then
        echo "$file"
        return 0
    fi
    return 1
}

# Dry run mode
if [ "$DRY_RUN" = true ]; then
    print_info "DRY RUN MODE - No files will be modified"
    print_info "Files that would be modified:"
    
    if [ "$PROCESS_PROJECT" = true ]; then
        print_info "=== Current Project Files ==="
        find "$(pwd)" -name "*.dart" -type f -exec bash -c 'dry_run_check() { if grep -q "^[[:space:]]*///" "$1" || grep -q "[[:space:]]*///.*$" "$1"; then echo "$1"; fi; }; dry_run_check "$0"' {} \;
    fi
    
    if [ "$PROCESS_CACHE" = true ]; then
        print_info "=== Pub Cache Files ==="
        PUB_CACHE_DIR="$HOME/.pub-cache/hosted/pub.dev"
        if [ -d "$PUB_CACHE_DIR" ]; then
            find "$PUB_CACHE_DIR" -name "*.dart" -type f -exec bash -c 'dry_run_check() { if grep -q "^[[:space:]]*///" "$1" || grep -q "[[:space:]]*///.*$" "$1"; then echo "$1"; fi; }; dry_run_check "$0"' {} \;
        fi
    fi
    
    exit 0
fi

# Execute main function based on options
if [ "$PROCESS_PROJECT" = true ] && [ "$PROCESS_CACHE" = true ]; then
    main
elif [ "$PROCESS_PROJECT" = true ]; then
    print_info "Processing only current project..."
    CURRENT_DIR=$(pwd)
    process_directory "$CURRENT_DIR" "Current Project"
    print_success "Project processing completed!"
elif [ "$PROCESS_CACHE" = true ]; then
    print_info "Processing only pub cache..."
    PUB_CACHE_DIR="$HOME/.pub-cache/hosted/pub.dev"
    if [ -d "$PUB_CACHE_DIR" ]; then
        for package_dir in "$PUB_CACHE_DIR"/*; do
            if [ -d "$package_dir" ]; then
                package_name=$(basename "$package_dir")
                process_directory "$package_dir" "Pub Cache Package: $package_name"
            fi
        done
    else
        print_warning "Pub cache directory not found: $PUB_CACHE_DIR"
    fi
    print_success "Pub cache processing completed!"
fi
