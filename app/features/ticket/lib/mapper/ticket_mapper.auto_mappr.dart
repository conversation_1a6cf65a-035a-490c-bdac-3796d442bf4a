// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoMapprGenerator
// **************************************************************************

// ignore_for_file: type=lint, unnecessary_cast, unused_local_variable

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i1;
import 'package:gp_core/models/assignee.dart' as _i51;
import 'package:gp_core/models/bot/bot_response.dart' as _i62;
import 'package:gp_core/models/comment/comment.dart' as _i28;
import 'package:gp_core/models/conversation.dart' as _i56;
import 'package:gp_core/models/orgchat/organization_department.dart' as _i58;
import 'package:gp_core/models/orgchat/organization_role.dart' as _i60;
import 'package:gp_core/models/orgchat/select_invitees_model.dart' as _i64;
import 'package:gp_core/models/upload/upload_file_response_model.dart' as _i68;
import 'package:gp_core/models/upload/upload_file_url_response_model.dart'
    as _i66;
import 'package:gp_core/models/upload/upload_image_response_model.dart' as _i70;
import 'package:gp_shared/data/model/upload/response/upload/upload_file_response_v2.dart'
    as _i69;
import 'package:gp_shared/domain/entity/assignee/assignee.entity.dart' as _i53;
import 'package:gp_shared/domain/entity/assignee/assignee_info.entity.dart'
    as _i55;
import 'package:gp_shared/domain/entity/assignee/assignee_work.entity.dart'
    as _i54;
import 'package:gp_shared/domain/entity/attachment/gp_attachment_file.entity.dart'
    as _i67;
import 'package:gp_shared/domain/entity/chatbot/chatbot.entity.dart' as _i63;
import 'package:gp_shared/domain/entity/conversation/conversation.entity.dart'
    as _i57;
import 'package:gp_shared/domain/entity/department/department.entity.dart'
    as _i59;
import 'package:gp_shared/domain/entity/role/role.entity.dart' as _i61;
import 'package:gp_shared/domain/entity/select_invite_options.entity.dart'
    as _i65;
import 'package:gp_shared/domain/entity/user/gp_user.entity.dart' as _i52;
import 'package:gp_shared/mapper/entity/assignee_entity_mapper.dart' as _i72;
import 'package:gp_shared/mapper/entity/attachment_entity_mapper.dart' as _i73;

import '../data/model/request/ticket/details/comments/ticket_edit_comments_request_params.dart'
    as _i30;
import '../data/model/request/ticket/details/comments/ticket_post_comments_request_params.dart'
    as _i29;
import '../data/model/response/ticket/ticket_activity_content_response.dart'
    as _i6;
import '../data/model/response/ticket/ticket_activity_response.dart' as _i8;
import '../data/model/response/ticket/ticket_additional_request_response.dart'
    as _i12;
import '../data/model/response/ticket/ticket_assignee_response.dart' as _i22;
import '../data/model/response/ticket/ticket_comment_response.dart' as _i26;
import '../data/model/response/ticket/ticket_creator_response.dart' as _i4;
import '../data/model/response/ticket/ticket_flowchart_response.dart' as _i16;
import '../data/model/response/ticket/ticket_is_assignee_response.dart' as _i47;
import '../data/model/response/ticket/ticket_list_response.dart' as _i2;
import '../data/model/response/ticket/ticket_node_response.dart' as _i20;
import '../data/model/response/ticket/ticket_on_hold_request_response.dart'
    as _i32;
import '../data/model/response/ticket/ticket_reopen_request_response.dart'
    as _i34;
import '../data/model/response/ticket/ticket_shift_response.dart' as _i10;
import '../data/model/response/ticket/ticket_sla_response.dart' as _i14;
import '../data/model/response/ticket/ticket_spam_request_response.dart'
    as _i45;
import '../data/model/response/ticket/ticket_user_role_response.dart' as _i18;
import '../data/model/response/workflow/workflow_advance_setting_response.dart'
    as _i24;
import '../data/model/response/workflow/workflow_form_response.dart' as _i37;
import '../data/model/response/workflow/workflow_response.dart' as _i35;
import '../data/model/response/workflow/workflow_start_node_response.dart'
    as _i39;
import '../data/model/response/workflow/workflow_tag_response.dart' as _i43;
import '../data/model/response/workflow/workflow_user_response.dart' as _i41;
import '../data/model/response/workflow_group/workflow_group_response.dart'
    as _i49;
import '../domain/entity/ticket/ticket_activity.entity.dart' as _i9;
import '../domain/entity/ticket/ticket_activity_content.entity.dart' as _i7;
import '../domain/entity/ticket/ticket_additional_request.entity.dart' as _i13;
import '../domain/entity/ticket/ticket_assignee.entity.dart' as _i23;
import '../domain/entity/ticket/ticket_comment.entity.dart' as _i27;
import '../domain/entity/ticket/ticket_creator.entity.dart' as _i5;
import '../domain/entity/ticket/ticket_flowchart.entity.dart' as _i17;
import '../domain/entity/ticket/ticket_is_assignee.entity.dart' as _i48;
import '../domain/entity/ticket/ticket_list.entity.dart' as _i3;
import '../domain/entity/ticket/ticket_node.entity.dart' as _i21;
import '../domain/entity/ticket/ticket_onhold_request_respose.entity.dart'
    as _i31;
import '../domain/entity/ticket/ticket_reopen_request_entity.dart' as _i33;
import '../domain/entity/ticket/ticket_shift.entity.dart' as _i11;
import '../domain/entity/ticket/ticket_sla.entity.dart' as _i15;
import '../domain/entity/ticket/ticket_spam_request_entity.dart' as _i46;
import '../domain/entity/ticket/ticket_user_role.entity.dart' as _i19;
import '../domain/entity/workflow/workflow.entity.dart' as _i36;
import '../domain/entity/workflow/workflow_advance_setting.dart' as _i25;
import '../domain/entity/workflow/workflow_form.entity.dart' as _i38;
import '../domain/entity/workflow/workflow_group.entity.dart' as _i50;
import '../domain/entity/workflow/workflow_start_node.entity.dart' as _i40;
import '../domain/entity/workflow/workflow_tag.entity.dart' as _i44;
import '../domain/entity/workflow/workflow_user.entity.dart' as _i42;
import 'entity/ticket_entity_mapper.dart' as _i71;

/// Available mappings:
/// - `TicketListResponse` → `TicketEntity`.
/// - `TicketCreatorResponse` → `TicketCreatorEntity`.
/// - `TicketActivityContentResponse` → `TicketActivityContentEntity`.
/// - `TicketActivityContentItemResponse` → `TicketActivityContentItemEntity`.
/// - `TicketHighlightResponse` → `TicketHighlightEntity`.
/// - `RefTicketResponse` → `RefTicketEntity`.
/// - `TicketActivityResponse` → `TicketActivityEntity`.
/// - `TicketShiftDetailResponse` → `TicketShiftDetailEntity`.
/// - `TicketAdditionalRequestResponse` → `TicketAdditionalRequestEntity`.
/// - `TicketSLAResponse` → `TicketSLAEntity`.
/// - `TicketSLAAggResponse` → `TicketSLAAggEntity`.
/// - `TicketShiftAggResponse` → `TicketShiftAggEntity`.
/// - `TicketSLAPriorityLevelsResponse` → `TicketSLAPriorityLevelsEntity`.
/// - `TicketSLAPriorityLevelFirstResponse` → `TicketSLAPriorityLevelFirstEntity`.
/// - `TicketSLANotificationResponse` → `TicketSLANotificationEntity`.
/// - `TicketSLANotificationDurationResponse` → `TicketSLANotificationDurationEntity`.
/// - `TickeUserRoleResponse` → `TickeUserRoleEntity`.
/// - `TicketFlowChartResponse` → `TicketFlowChartEntity`.
/// - `TicketFlowchartNodeResponse` → `TicketFlowChartNodeEntity`.
/// - `TicketNodeOptionResponse` → `TicketNodeOptionEntity`.
/// - `TicketFlowchartEdgeResponse` → `TicketFlowchartEdgeEntity`.
/// - `TicketNodeResponse` → `TicketNodeEntity`.
/// - `TicketAssigneeResponse` → `TicketAssigneeEntity`.
/// - `WorkflowAdvanceSettingAssigneeResponse` → `WorkflowAdvanceSettingAssigneeEntity`.
/// - `TicketCommentResponse` → `TicketCommentEntity`.
/// - `CommentAsResponse` → `CommentAs`.
/// - `MentionResponse` → `Mentions`.
/// - `MediasResponse` → `Medias`.
/// - `TicketCommentEntity` → `TicketPostCommentsRequestParams`.
/// - `Comment` → `TicketEditCommentsRequestParams`.
/// - `Comment` → `TicketCommentEntity`.
/// - `TicketNodeTagResponse` → `TicketNodeTagEntity`.
/// - `TicketEntity` → `TicketListResponse`.
/// - `TicketCreatorEntity` → `TicketCreatorResponse`.
/// - `TicketActivityContentEntity` → `TicketActivityContentResponse`.
/// - `TicketActivityContentItemEntity` → `TicketActivityContentItemResponse`.
/// - `TicketHighlightEntity` → `TicketHighlightResponse`.
/// - `RefTicketEntity` → `RefTicketResponse`.
/// - `TicketActivityEntity` → `TicketActivityResponse`.
/// - `TicketShiftDetailEntity` → `TicketShiftDetailResponse`.
/// - `TicketAdditionalRequestEntity` → `TicketAdditionalRequestResponse`.
/// - `TicketOnHoldRequestEntity` → `TicketOnHoldRequestResponse`.
/// - `TicketReopenRequestEntity` → `TicketReopenRequestResponse`.
/// - `TicketSLAAggEntity` → `TicketSLAAggResponse`.
/// - `TicketShiftAggEntity` → `TicketShiftAggResponse`.
/// - `TicketSLAEntity` → `TicketSLAResponse`.
/// - `TicketSLAPriorityLevelsEntity` → `TicketSLAPriorityLevelsResponse`.
/// - `TicketSLAPriorityLevelFirstEntity` → `TicketSLAPriorityLevelFirstResponse`.
/// - `TicketSLANotificationEntity` → `TicketSLANotificationResponse`.
/// - `TicketSLANotificationDurationEntity` → `TicketSLANotificationDurationResponse`.
/// - `TickeUserRoleEntity` → `TickeUserRoleResponse`.
/// - `TicketFlowChartEntity` → `TicketFlowChartResponse`.
/// - `TicketFlowChartNodeEntity` → `TicketFlowchartNodeResponse`.
/// - `TicketNodeOptionEntity` → `TicketNodeOptionResponse`.
/// - `TicketFlowchartEdgeEntity` → `TicketFlowchartEdgeResponse`.
/// - `TicketNodeEntity` → `TicketNodeResponse`.
/// - `TicketAssigneeEntity` → `TicketAssigneeResponse`.
/// - `WorkflowAdvanceSettingAssigneeEntity` → `WorkflowAdvanceSettingAssigneeResponse`.
/// - `TicketNodeTagEntity` → `TicketNodeTagResponse`.
/// - `WorkFlowWrapperResponse` → `WorkFlowWrapperEntity`.
/// - `WorkFlowResponse` → `WorkFlowEntity`.
/// - `WorkflowFormFieldPermissionsResponse` → `WorkflowFormFieldPermissionEntity`.
/// - `WorkFlowFormResponse` → `WorkFlowFormEntity`.
/// - `WorkFlowFormFieldResponse` → `WorkFlowFormFieldEntity`.
/// - `WorkFlowFieldValuesResponse` → `WorkFlowFieldValuesEntity`.
/// - `WorkFlowFormOptionResponse` → `WorkFlowFormOptionEntity`.
/// - `WorkFlowFormSelectionResponse` → `WorkFlowFormSelectionEntity`.
/// - `WorkFlowFormColumnResponse` → `WorkFlowFormColumnEntity`.
/// - `WorkFlowStartNodeResponse` → `WorkFlowStartNodeEntity`.
/// - `WorkFlowUserInfoResponse` → `WorkFlowUserInfoEntity`.
/// - `WorkFlowDeparmentResponse` → `WorkFlowDeparmentEntity`.
/// - `WorkflowAdvanceSettingResponse` → `WorkflowAdvanceSettingEntity`.
/// - `WorkflowAdvanceSettingAllowActionResponse` → `WorkflowAdvanceSettingAllowActionEntity`.
/// - `WorkflowAdvanceSettingAdminOptionResponse` → `WorkflowAdvanceSettingAdminOptionEntity`.
/// - `WorkflowAdvanceSettingAllowOptionResponse` → `WorkflowAdvanceSettingAllowOptionEntity`.
/// - `WorkflowAdvanceSettingRateOptionResponse` → `WorkflowAdvanceSettingRateOptionEntity`.
/// - `WorkflowTagResponse` → `WorkflowTagEntity`.
/// - `WorkFlowWrapperEntity` → `WorkFlowWrapperResponse`.
/// - `WorkFlowEntity` → `WorkFlowResponse`.
/// - `WorkflowFormFieldPermissionEntity` → `WorkflowFormFieldPermissionsResponse`.
/// - `WorkFlowFormEntity` → `WorkFlowFormResponse`.
/// - `WorkFlowFormFieldEntity` → `WorkFlowFormFieldResponse`.
/// - `WorkFlowFieldValuesEntity` → `WorkFlowFieldValuesResponse`.
/// - `WorkFlowFormOptionEntity` → `WorkFlowFormOptionResponse`.
/// - `WorkFlowFormSelectionEntity` → `WorkFlowFormSelectionResponse`.
/// - `WorkFlowFormColumnEntity` → `WorkFlowFormColumnResponse`.
/// - `WorkFlowStartNodeEntity` → `WorkFlowStartNodeResponse`.
/// - `WorkFlowUserInfoEntity` → `WorkFlowUserInfoResponse`.
/// - `WorkFlowDeparmentEntity` → `WorkFlowDeparmentResponse`.
/// - `WorkflowAdvanceSettingEntity` → `WorkflowAdvanceSettingResponse`.
/// - `WorkflowAdvanceSettingAllowActionEntity` → `WorkflowAdvanceSettingAllowActionResponse`.
/// - `WorkflowAdvanceSettingAdminOptionEntity` → `WorkflowAdvanceSettingAdminOptionResponse`.
/// - `WorkflowAdvanceSettingAllowOptionEntity` → `WorkflowAdvanceSettingAllowOptionResponse`.
/// - `WorkflowAdvanceSettingRateOptionEntity` → `WorkflowAdvanceSettingRateOptionResponse`.
/// - `TicketOnHoldRequestResponse` → `TicketOnHoldRequestEntity`.
/// - `TicketReopenRequestResponse` → `TicketReopenRequestEntity`.
/// - `TicketSpamRequestResponse` → `TicketSpamRequestEntity`.
/// - `TicketSpamRequestEntity` → `TicketSpamRequestResponse`.
/// - `WorkflowTagEntity` → `WorkflowTagResponse`.
/// - `TicketIsTicketAssigneeResponse` → `TicketIsTicketAssigneeEntity`.
/// - `TicketIsTicketAssigneeEntity` → `TicketIsTicketAssigneeResponse`.
/// - `WorkFlowGroupWrapperResponse` → `WorkFlowGroupWrapperEntity`.
/// - `WorkFlowGroupResponse` → `WorkFlowGroupEntity`.
/// - `Assignee` → `GPUserEntity`.
/// - `AssigneeEntity` → `GPUserEntity`.
/// - `GPUserEntity` → `AssigneeEntity`.
/// - `Assignee` → `AssigneeEntity`.
/// - `Work` → `WorkEntity`.
/// - `Info` → `InfoEntity`.
/// - `Conversation` → `ConversationEntity`.
/// - `OrganizationDepartment` → `OrganizationDepartmentEntity`.
/// - `OrganizationRole` → `OrganizationRoleEntity`.
/// - `ChatBotModel` → `ChatBotEntity`.
/// - `SelectInviteesOptions` → `SelectMemberEntity`.
/// - `AssigneeEntity` → `Assignee`.
/// - `WorkEntity` → `Work`.
/// - `InfoEntity` → `Info`.
/// - `ConversationEntity` → `Conversation`.
/// - `OrganizationRoleEntity` → `OrganizationRole`.
/// - `OrganizationDepartmentEntity` → `OrganizationDepartment`.
/// - `ChatBotEntity` → `ChatBotModel`.
/// - `UploadFileURLResponseModel` → `GpAttachmentFileUrlEntity`.
/// - `UploadFileResponseModel` → `GPAttachmentFileEntity`.
/// - `UploadFileResponseModelV2` → `GPAttachmentFileEntity`.
/// - `UploadImageResponseModel` → `GPAttachmentFileEntity`.
/// - `GpAttachmentFileUrlEntity` → `UploadFileURLResponseModel`.
/// - `GPAttachmentFileEntity` → `UploadFileResponseModel`.
/// - `GPAttachmentFileEntity` → `UploadFileResponseModelV2`.
class $GPTicketMapper implements _i1.AutoMapprInterface {
  const $GPTicketMapper();

  Type _typeOf<T>() => T;

  List<_i1.AutoMapprInterface> get _delegates => const [];

  
  
  @override
  bool canConvert<SOURCE, TARGET>({bool recursive = true}) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.TicketListResponse>() ||
            sourceTypeOf == _typeOf<_i2.TicketListResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.TicketEntity>() ||
            targetTypeOf == _typeOf<_i3.TicketEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.TicketCreatorResponse>() ||
            sourceTypeOf == _typeOf<_i4.TicketCreatorResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.TicketCreatorEntity>() ||
            targetTypeOf == _typeOf<_i5.TicketCreatorEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i6.TicketActivityContentResponse>() ||
            sourceTypeOf == _typeOf<_i6.TicketActivityContentResponse?>()) &&
        (targetTypeOf == _typeOf<_i7.TicketActivityContentEntity>() ||
            targetTypeOf == _typeOf<_i7.TicketActivityContentEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i6.TicketActivityContentItemResponse>() ||
            sourceTypeOf ==
                _typeOf<_i6.TicketActivityContentItemResponse?>()) &&
        (targetTypeOf == _typeOf<_i7.TicketActivityContentItemEntity>() ||
            targetTypeOf == _typeOf<_i7.TicketActivityContentItemEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i6.TicketHighlightResponse>() ||
            sourceTypeOf == _typeOf<_i6.TicketHighlightResponse?>()) &&
        (targetTypeOf == _typeOf<_i7.TicketHighlightEntity>() ||
            targetTypeOf == _typeOf<_i7.TicketHighlightEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i2.RefTicketResponse>() ||
            sourceTypeOf == _typeOf<_i2.RefTicketResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.RefTicketEntity>() ||
            targetTypeOf == _typeOf<_i3.RefTicketEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i8.TicketActivityResponse>() ||
            sourceTypeOf == _typeOf<_i8.TicketActivityResponse?>()) &&
        (targetTypeOf == _typeOf<_i9.TicketActivityEntity>() ||
            targetTypeOf == _typeOf<_i9.TicketActivityEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i10.TicketShiftDetailResponse>() ||
            sourceTypeOf == _typeOf<_i10.TicketShiftDetailResponse?>()) &&
        (targetTypeOf == _typeOf<_i11.TicketShiftDetailEntity>() ||
            targetTypeOf == _typeOf<_i11.TicketShiftDetailEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i12.TicketAdditionalRequestResponse>() ||
            sourceTypeOf == _typeOf<_i12.TicketAdditionalRequestResponse?>()) &&
        (targetTypeOf == _typeOf<_i13.TicketAdditionalRequestEntity>() ||
            targetTypeOf == _typeOf<_i13.TicketAdditionalRequestEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i14.TicketSLAResponse>() ||
            sourceTypeOf == _typeOf<_i14.TicketSLAResponse?>()) &&
        (targetTypeOf == _typeOf<_i15.TicketSLAEntity>() ||
            targetTypeOf == _typeOf<_i15.TicketSLAEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i16.TicketSLAAggResponse>() ||
            sourceTypeOf == _typeOf<_i16.TicketSLAAggResponse?>()) &&
        (targetTypeOf == _typeOf<_i17.TicketSLAAggEntity>() ||
            targetTypeOf == _typeOf<_i17.TicketSLAAggEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i16.TicketShiftAggResponse>() ||
            sourceTypeOf == _typeOf<_i16.TicketShiftAggResponse?>()) &&
        (targetTypeOf == _typeOf<_i17.TicketShiftAggEntity>() ||
            targetTypeOf == _typeOf<_i17.TicketShiftAggEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i14.TicketSLAPriorityLevelsResponse>() ||
            sourceTypeOf == _typeOf<_i14.TicketSLAPriorityLevelsResponse?>()) &&
        (targetTypeOf == _typeOf<_i15.TicketSLAPriorityLevelsEntity>() ||
            targetTypeOf == _typeOf<_i15.TicketSLAPriorityLevelsEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i14.TicketSLAPriorityLevelFirstResponse>() ||
            sourceTypeOf ==
                _typeOf<_i14.TicketSLAPriorityLevelFirstResponse?>()) &&
        (targetTypeOf == _typeOf<_i15.TicketSLAPriorityLevelFirstEntity>() ||
            targetTypeOf ==
                _typeOf<_i15.TicketSLAPriorityLevelFirstEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i14.TicketSLANotificationResponse>() ||
            sourceTypeOf == _typeOf<_i14.TicketSLANotificationResponse?>()) &&
        (targetTypeOf == _typeOf<_i15.TicketSLANotificationEntity>() ||
            targetTypeOf == _typeOf<_i15.TicketSLANotificationEntity?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i14.TicketSLANotificationDurationResponse>() ||
            sourceTypeOf ==
                _typeOf<_i14.TicketSLANotificationDurationResponse?>()) &&
        (targetTypeOf == _typeOf<_i15.TicketSLANotificationDurationEntity>() ||
            targetTypeOf ==
                _typeOf<_i15.TicketSLANotificationDurationEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i18.TickeUserRoleResponse>() ||
            sourceTypeOf == _typeOf<_i18.TickeUserRoleResponse?>()) &&
        (targetTypeOf == _typeOf<_i19.TickeUserRoleEntity>() ||
            targetTypeOf == _typeOf<_i19.TickeUserRoleEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i16.TicketFlowChartResponse>() ||
            sourceTypeOf == _typeOf<_i16.TicketFlowChartResponse?>()) &&
        (targetTypeOf == _typeOf<_i17.TicketFlowChartEntity>() ||
            targetTypeOf == _typeOf<_i17.TicketFlowChartEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i16.TicketFlowchartNodeResponse>() ||
            sourceTypeOf == _typeOf<_i16.TicketFlowchartNodeResponse?>()) &&
        (targetTypeOf == _typeOf<_i17.TicketFlowChartNodeEntity>() ||
            targetTypeOf == _typeOf<_i17.TicketFlowChartNodeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i20.TicketNodeOptionResponse>() ||
            sourceTypeOf == _typeOf<_i20.TicketNodeOptionResponse?>()) &&
        (targetTypeOf == _typeOf<_i21.TicketNodeOptionEntity>() ||
            targetTypeOf == _typeOf<_i21.TicketNodeOptionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i16.TicketFlowchartEdgeResponse>() ||
            sourceTypeOf == _typeOf<_i16.TicketFlowchartEdgeResponse?>()) &&
        (targetTypeOf == _typeOf<_i17.TicketFlowchartEdgeEntity>() ||
            targetTypeOf == _typeOf<_i17.TicketFlowchartEdgeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i20.TicketNodeResponse>() ||
            sourceTypeOf == _typeOf<_i20.TicketNodeResponse?>()) &&
        (targetTypeOf == _typeOf<_i21.TicketNodeEntity>() ||
            targetTypeOf == _typeOf<_i21.TicketNodeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i22.TicketAssigneeResponse>() ||
            sourceTypeOf == _typeOf<_i22.TicketAssigneeResponse?>()) &&
        (targetTypeOf == _typeOf<_i23.TicketAssigneeEntity>() ||
            targetTypeOf == _typeOf<_i23.TicketAssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAssigneeResponse>() ||
            sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAssigneeResponse?>()) &&
        (targetTypeOf == _typeOf<_i25.WorkflowAdvanceSettingAssigneeEntity>() ||
            targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i26.TicketCommentResponse>() ||
            sourceTypeOf == _typeOf<_i26.TicketCommentResponse?>()) &&
        (targetTypeOf == _typeOf<_i27.TicketCommentEntity>() ||
            targetTypeOf == _typeOf<_i27.TicketCommentEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i26.CommentAsResponse>() ||
            sourceTypeOf == _typeOf<_i26.CommentAsResponse?>()) &&
        (targetTypeOf == _typeOf<_i28.CommentAs>() ||
            targetTypeOf == _typeOf<_i28.CommentAs?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i26.MentionResponse>() ||
            sourceTypeOf == _typeOf<_i26.MentionResponse?>()) &&
        (targetTypeOf == _typeOf<_i28.Mentions>() ||
            targetTypeOf == _typeOf<_i28.Mentions?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i26.MediasResponse>() ||
            sourceTypeOf == _typeOf<_i26.MediasResponse?>()) &&
        (targetTypeOf == _typeOf<_i28.Medias>() ||
            targetTypeOf == _typeOf<_i28.Medias?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i27.TicketCommentEntity>() ||
            sourceTypeOf == _typeOf<_i27.TicketCommentEntity?>()) &&
        (targetTypeOf == _typeOf<_i29.TicketPostCommentsRequestParams>() ||
            targetTypeOf == _typeOf<_i29.TicketPostCommentsRequestParams?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i28.Comment>() ||
            sourceTypeOf == _typeOf<_i28.Comment?>()) &&
        (targetTypeOf == _typeOf<_i30.TicketEditCommentsRequestParams>() ||
            targetTypeOf == _typeOf<_i30.TicketEditCommentsRequestParams?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i28.Comment>() ||
            sourceTypeOf == _typeOf<_i28.Comment?>()) &&
        (targetTypeOf == _typeOf<_i27.TicketCommentEntity>() ||
            targetTypeOf == _typeOf<_i27.TicketCommentEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i20.TicketNodeTagResponse>() ||
            sourceTypeOf == _typeOf<_i20.TicketNodeTagResponse?>()) &&
        (targetTypeOf == _typeOf<_i21.TicketNodeTagEntity>() ||
            targetTypeOf == _typeOf<_i21.TicketNodeTagEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i3.TicketEntity>() ||
            sourceTypeOf == _typeOf<_i3.TicketEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.TicketListResponse>() ||
            targetTypeOf == _typeOf<_i2.TicketListResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i5.TicketCreatorEntity>() ||
            sourceTypeOf == _typeOf<_i5.TicketCreatorEntity?>()) &&
        (targetTypeOf == _typeOf<_i4.TicketCreatorResponse>() ||
            targetTypeOf == _typeOf<_i4.TicketCreatorResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i7.TicketActivityContentEntity>() ||
            sourceTypeOf == _typeOf<_i7.TicketActivityContentEntity?>()) &&
        (targetTypeOf == _typeOf<_i6.TicketActivityContentResponse>() ||
            targetTypeOf == _typeOf<_i6.TicketActivityContentResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i7.TicketActivityContentItemEntity>() ||
            sourceTypeOf == _typeOf<_i7.TicketActivityContentItemEntity?>()) &&
        (targetTypeOf == _typeOf<_i6.TicketActivityContentItemResponse>() ||
            targetTypeOf ==
                _typeOf<_i6.TicketActivityContentItemResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i7.TicketHighlightEntity>() ||
            sourceTypeOf == _typeOf<_i7.TicketHighlightEntity?>()) &&
        (targetTypeOf == _typeOf<_i6.TicketHighlightResponse>() ||
            targetTypeOf == _typeOf<_i6.TicketHighlightResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i3.RefTicketEntity>() ||
            sourceTypeOf == _typeOf<_i3.RefTicketEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.RefTicketResponse>() ||
            targetTypeOf == _typeOf<_i2.RefTicketResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.TicketActivityEntity>() ||
            sourceTypeOf == _typeOf<_i9.TicketActivityEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.TicketActivityResponse>() ||
            targetTypeOf == _typeOf<_i8.TicketActivityResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i11.TicketShiftDetailEntity>() ||
            sourceTypeOf == _typeOf<_i11.TicketShiftDetailEntity?>()) &&
        (targetTypeOf == _typeOf<_i10.TicketShiftDetailResponse>() ||
            targetTypeOf == _typeOf<_i10.TicketShiftDetailResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i13.TicketAdditionalRequestEntity>() ||
            sourceTypeOf == _typeOf<_i13.TicketAdditionalRequestEntity?>()) &&
        (targetTypeOf == _typeOf<_i12.TicketAdditionalRequestResponse>() ||
            targetTypeOf == _typeOf<_i12.TicketAdditionalRequestResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i31.TicketOnHoldRequestEntity>() ||
            sourceTypeOf == _typeOf<_i31.TicketOnHoldRequestEntity?>()) &&
        (targetTypeOf == _typeOf<_i32.TicketOnHoldRequestResponse>() ||
            targetTypeOf == _typeOf<_i32.TicketOnHoldRequestResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i33.TicketReopenRequestEntity>() ||
            sourceTypeOf == _typeOf<_i33.TicketReopenRequestEntity?>()) &&
        (targetTypeOf == _typeOf<_i34.TicketReopenRequestResponse>() ||
            targetTypeOf == _typeOf<_i34.TicketReopenRequestResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i17.TicketSLAAggEntity>() ||
            sourceTypeOf == _typeOf<_i17.TicketSLAAggEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.TicketSLAAggResponse>() ||
            targetTypeOf == _typeOf<_i16.TicketSLAAggResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i17.TicketShiftAggEntity>() ||
            sourceTypeOf == _typeOf<_i17.TicketShiftAggEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.TicketShiftAggResponse>() ||
            targetTypeOf == _typeOf<_i16.TicketShiftAggResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.TicketSLAEntity>() ||
            sourceTypeOf == _typeOf<_i15.TicketSLAEntity?>()) &&
        (targetTypeOf == _typeOf<_i14.TicketSLAResponse>() ||
            targetTypeOf == _typeOf<_i14.TicketSLAResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.TicketSLAPriorityLevelsEntity>() ||
            sourceTypeOf == _typeOf<_i15.TicketSLAPriorityLevelsEntity?>()) &&
        (targetTypeOf == _typeOf<_i14.TicketSLAPriorityLevelsResponse>() ||
            targetTypeOf == _typeOf<_i14.TicketSLAPriorityLevelsResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.TicketSLAPriorityLevelFirstEntity>() ||
            sourceTypeOf ==
                _typeOf<_i15.TicketSLAPriorityLevelFirstEntity?>()) &&
        (targetTypeOf == _typeOf<_i14.TicketSLAPriorityLevelFirstResponse>() ||
            targetTypeOf ==
                _typeOf<_i14.TicketSLAPriorityLevelFirstResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.TicketSLANotificationEntity>() ||
            sourceTypeOf == _typeOf<_i15.TicketSLANotificationEntity?>()) &&
        (targetTypeOf == _typeOf<_i14.TicketSLANotificationResponse>() ||
            targetTypeOf == _typeOf<_i14.TicketSLANotificationResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.TicketSLANotificationDurationEntity>() ||
            sourceTypeOf ==
                _typeOf<_i15.TicketSLANotificationDurationEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i14.TicketSLANotificationDurationResponse>() ||
            targetTypeOf ==
                _typeOf<_i14.TicketSLANotificationDurationResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i19.TickeUserRoleEntity>() ||
            sourceTypeOf == _typeOf<_i19.TickeUserRoleEntity?>()) &&
        (targetTypeOf == _typeOf<_i18.TickeUserRoleResponse>() ||
            targetTypeOf == _typeOf<_i18.TickeUserRoleResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i17.TicketFlowChartEntity>() ||
            sourceTypeOf == _typeOf<_i17.TicketFlowChartEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.TicketFlowChartResponse>() ||
            targetTypeOf == _typeOf<_i16.TicketFlowChartResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i17.TicketFlowChartNodeEntity>() ||
            sourceTypeOf == _typeOf<_i17.TicketFlowChartNodeEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.TicketFlowchartNodeResponse>() ||
            targetTypeOf == _typeOf<_i16.TicketFlowchartNodeResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i21.TicketNodeOptionEntity>() ||
            sourceTypeOf == _typeOf<_i21.TicketNodeOptionEntity?>()) &&
        (targetTypeOf == _typeOf<_i20.TicketNodeOptionResponse>() ||
            targetTypeOf == _typeOf<_i20.TicketNodeOptionResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i17.TicketFlowchartEdgeEntity>() ||
            sourceTypeOf == _typeOf<_i17.TicketFlowchartEdgeEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.TicketFlowchartEdgeResponse>() ||
            targetTypeOf == _typeOf<_i16.TicketFlowchartEdgeResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i21.TicketNodeEntity>() ||
            sourceTypeOf == _typeOf<_i21.TicketNodeEntity?>()) &&
        (targetTypeOf == _typeOf<_i20.TicketNodeResponse>() ||
            targetTypeOf == _typeOf<_i20.TicketNodeResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i23.TicketAssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i23.TicketAssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i22.TicketAssigneeResponse>() ||
            targetTypeOf == _typeOf<_i22.TicketAssigneeResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i25.WorkflowAdvanceSettingAssigneeEntity>() ||
            sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAssigneeEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAssigneeResponse>() ||
            targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAssigneeResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i21.TicketNodeTagEntity>() ||
            sourceTypeOf == _typeOf<_i21.TicketNodeTagEntity?>()) &&
        (targetTypeOf == _typeOf<_i20.TicketNodeTagResponse>() ||
            targetTypeOf == _typeOf<_i20.TicketNodeTagResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i35.WorkFlowWrapperResponse>() ||
            sourceTypeOf == _typeOf<_i35.WorkFlowWrapperResponse?>()) &&
        (targetTypeOf == _typeOf<_i36.WorkFlowWrapperEntity>() ||
            targetTypeOf == _typeOf<_i36.WorkFlowWrapperEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i35.WorkFlowResponse>() ||
            sourceTypeOf == _typeOf<_i35.WorkFlowResponse?>()) &&
        (targetTypeOf == _typeOf<_i36.WorkFlowEntity>() ||
            targetTypeOf == _typeOf<_i36.WorkFlowEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i20.WorkflowFormFieldPermissionsResponse>() ||
            sourceTypeOf ==
                _typeOf<_i20.WorkflowFormFieldPermissionsResponse?>()) &&
        (targetTypeOf == _typeOf<_i21.WorkflowFormFieldPermissionEntity>() ||
            targetTypeOf ==
                _typeOf<_i21.WorkflowFormFieldPermissionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFormResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFormResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFormEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFormEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFormFieldResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFormFieldResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFormFieldEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFormFieldEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFieldValuesResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFieldValuesResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFieldValuesEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFieldValuesEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFormOptionResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFormOptionResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFormOptionEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFormOptionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFormSelectionResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFormSelectionResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFormSelectionEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFormSelectionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFormColumnResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFormColumnResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFormColumnEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFormColumnEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i39.WorkFlowStartNodeResponse>() ||
            sourceTypeOf == _typeOf<_i39.WorkFlowStartNodeResponse?>()) &&
        (targetTypeOf == _typeOf<_i40.WorkFlowStartNodeEntity>() ||
            targetTypeOf == _typeOf<_i40.WorkFlowStartNodeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i41.WorkFlowUserInfoResponse>() ||
            sourceTypeOf == _typeOf<_i41.WorkFlowUserInfoResponse?>()) &&
        (targetTypeOf == _typeOf<_i42.WorkFlowUserInfoEntity>() ||
            targetTypeOf == _typeOf<_i42.WorkFlowUserInfoEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i41.WorkFlowDeparmentResponse>() ||
            sourceTypeOf == _typeOf<_i41.WorkFlowDeparmentResponse?>()) &&
        (targetTypeOf == _typeOf<_i42.WorkFlowDeparmentEntity>() ||
            targetTypeOf == _typeOf<_i42.WorkFlowDeparmentEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i24.WorkflowAdvanceSettingResponse>() ||
            sourceTypeOf == _typeOf<_i24.WorkflowAdvanceSettingResponse?>()) &&
        (targetTypeOf == _typeOf<_i25.WorkflowAdvanceSettingEntity>() ||
            targetTypeOf == _typeOf<_i25.WorkflowAdvanceSettingEntity?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowActionResponse>() ||
            sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowActionResponse?>()) &&
        (targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowActionEntity>() ||
            targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowActionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAdminOptionResponse>() ||
            sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAdminOptionResponse?>()) &&
        (targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAdminOptionEntity>() ||
            targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAdminOptionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowOptionResponse>() ||
            sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowOptionResponse?>()) &&
        (targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowOptionEntity>() ||
            targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowOptionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingRateOptionResponse>() ||
            sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingRateOptionResponse?>()) &&
        (targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingRateOptionEntity>() ||
            targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingRateOptionEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i43.WorkflowTagResponse>() ||
            sourceTypeOf == _typeOf<_i43.WorkflowTagResponse?>()) &&
        (targetTypeOf == _typeOf<_i44.WorkflowTagEntity>() ||
            targetTypeOf == _typeOf<_i44.WorkflowTagEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i36.WorkFlowWrapperEntity>() ||
            sourceTypeOf == _typeOf<_i36.WorkFlowWrapperEntity?>()) &&
        (targetTypeOf == _typeOf<_i35.WorkFlowWrapperResponse>() ||
            targetTypeOf == _typeOf<_i35.WorkFlowWrapperResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i36.WorkFlowEntity>() ||
            sourceTypeOf == _typeOf<_i36.WorkFlowEntity?>()) &&
        (targetTypeOf == _typeOf<_i35.WorkFlowResponse>() ||
            targetTypeOf == _typeOf<_i35.WorkFlowResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i21.WorkflowFormFieldPermissionEntity>() ||
            sourceTypeOf ==
                _typeOf<_i21.WorkflowFormFieldPermissionEntity?>()) &&
        (targetTypeOf == _typeOf<_i20.WorkflowFormFieldPermissionsResponse>() ||
            targetTypeOf ==
                _typeOf<_i20.WorkflowFormFieldPermissionsResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFormEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFormEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFormResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFormResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFormFieldEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFormFieldEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFormFieldResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFormFieldResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFieldValuesEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFieldValuesEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFieldValuesResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFieldValuesResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFormOptionEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFormOptionEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFormOptionResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFormOptionResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFormSelectionEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFormSelectionEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFormSelectionResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFormSelectionResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFormColumnEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFormColumnEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFormColumnResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFormColumnResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i40.WorkFlowStartNodeEntity>() ||
            sourceTypeOf == _typeOf<_i40.WorkFlowStartNodeEntity?>()) &&
        (targetTypeOf == _typeOf<_i39.WorkFlowStartNodeResponse>() ||
            targetTypeOf == _typeOf<_i39.WorkFlowStartNodeResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i42.WorkFlowUserInfoEntity>() ||
            sourceTypeOf == _typeOf<_i42.WorkFlowUserInfoEntity?>()) &&
        (targetTypeOf == _typeOf<_i41.WorkFlowUserInfoResponse>() ||
            targetTypeOf == _typeOf<_i41.WorkFlowUserInfoResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i42.WorkFlowDeparmentEntity>() ||
            sourceTypeOf == _typeOf<_i42.WorkFlowDeparmentEntity?>()) &&
        (targetTypeOf == _typeOf<_i41.WorkFlowDeparmentResponse>() ||
            targetTypeOf == _typeOf<_i41.WorkFlowDeparmentResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i25.WorkflowAdvanceSettingEntity>() ||
            sourceTypeOf == _typeOf<_i25.WorkflowAdvanceSettingEntity?>()) &&
        (targetTypeOf == _typeOf<_i24.WorkflowAdvanceSettingResponse>() ||
            targetTypeOf == _typeOf<_i24.WorkflowAdvanceSettingResponse?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowActionEntity>() ||
            sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowActionEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowActionResponse>() ||
            targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowActionResponse?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAdminOptionEntity>() ||
            sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAdminOptionEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAdminOptionResponse>() ||
            targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAdminOptionResponse?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowOptionEntity>() ||
            sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowOptionEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowOptionResponse>() ||
            targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowOptionResponse?>())) {
      return true;
    }
    if ((sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingRateOptionEntity>() ||
            sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingRateOptionEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingRateOptionResponse>() ||
            targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingRateOptionResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i32.TicketOnHoldRequestResponse>() ||
            sourceTypeOf == _typeOf<_i32.TicketOnHoldRequestResponse?>()) &&
        (targetTypeOf == _typeOf<_i31.TicketOnHoldRequestEntity>() ||
            targetTypeOf == _typeOf<_i31.TicketOnHoldRequestEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i34.TicketReopenRequestResponse>() ||
            sourceTypeOf == _typeOf<_i34.TicketReopenRequestResponse?>()) &&
        (targetTypeOf == _typeOf<_i33.TicketReopenRequestEntity>() ||
            targetTypeOf == _typeOf<_i33.TicketReopenRequestEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i45.TicketSpamRequestResponse>() ||
            sourceTypeOf == _typeOf<_i45.TicketSpamRequestResponse?>()) &&
        (targetTypeOf == _typeOf<_i46.TicketSpamRequestEntity>() ||
            targetTypeOf == _typeOf<_i46.TicketSpamRequestEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i46.TicketSpamRequestEntity>() ||
            sourceTypeOf == _typeOf<_i46.TicketSpamRequestEntity?>()) &&
        (targetTypeOf == _typeOf<_i45.TicketSpamRequestResponse>() ||
            targetTypeOf == _typeOf<_i45.TicketSpamRequestResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i44.WorkflowTagEntity>() ||
            sourceTypeOf == _typeOf<_i44.WorkflowTagEntity?>()) &&
        (targetTypeOf == _typeOf<_i43.WorkflowTagResponse>() ||
            targetTypeOf == _typeOf<_i43.WorkflowTagResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i47.TicketIsTicketAssigneeResponse>() ||
            sourceTypeOf == _typeOf<_i47.TicketIsTicketAssigneeResponse?>()) &&
        (targetTypeOf == _typeOf<_i48.TicketIsTicketAssigneeEntity>() ||
            targetTypeOf == _typeOf<_i48.TicketIsTicketAssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i48.TicketIsTicketAssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i48.TicketIsTicketAssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i47.TicketIsTicketAssigneeResponse>() ||
            targetTypeOf == _typeOf<_i47.TicketIsTicketAssigneeResponse?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i49.WorkFlowGroupWrapperResponse>() ||
            sourceTypeOf == _typeOf<_i49.WorkFlowGroupWrapperResponse?>()) &&
        (targetTypeOf == _typeOf<_i50.WorkFlowGroupWrapperEntity>() ||
            targetTypeOf == _typeOf<_i50.WorkFlowGroupWrapperEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i49.WorkFlowGroupResponse>() ||
            sourceTypeOf == _typeOf<_i49.WorkFlowGroupResponse?>()) &&
        (targetTypeOf == _typeOf<_i50.WorkFlowGroupEntity>() ||
            targetTypeOf == _typeOf<_i50.WorkFlowGroupEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i51.Assignee>() ||
            sourceTypeOf == _typeOf<_i51.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i52.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i52.GPUserEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i53.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i53.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i52.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i52.GPUserEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i52.GPUserEntity>() ||
            sourceTypeOf == _typeOf<_i52.GPUserEntity?>()) &&
        (targetTypeOf == _typeOf<_i53.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i53.AssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i51.Assignee>() ||
            sourceTypeOf == _typeOf<_i51.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i53.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i53.AssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i51.Work>() ||
            sourceTypeOf == _typeOf<_i51.Work?>()) &&
        (targetTypeOf == _typeOf<_i54.WorkEntity>() ||
            targetTypeOf == _typeOf<_i54.WorkEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i51.Info>() ||
            sourceTypeOf == _typeOf<_i51.Info?>()) &&
        (targetTypeOf == _typeOf<_i55.InfoEntity>() ||
            targetTypeOf == _typeOf<_i55.InfoEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i56.Conversation>() ||
            sourceTypeOf == _typeOf<_i56.Conversation?>()) &&
        (targetTypeOf == _typeOf<_i57.ConversationEntity>() ||
            targetTypeOf == _typeOf<_i57.ConversationEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i58.OrganizationDepartment>() ||
            sourceTypeOf == _typeOf<_i58.OrganizationDepartment?>()) &&
        (targetTypeOf == _typeOf<_i59.OrganizationDepartmentEntity>() ||
            targetTypeOf == _typeOf<_i59.OrganizationDepartmentEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i60.OrganizationRole>() ||
            sourceTypeOf == _typeOf<_i60.OrganizationRole?>()) &&
        (targetTypeOf == _typeOf<_i61.OrganizationRoleEntity>() ||
            targetTypeOf == _typeOf<_i61.OrganizationRoleEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i62.ChatBotModel>() ||
            sourceTypeOf == _typeOf<_i62.ChatBotModel?>()) &&
        (targetTypeOf == _typeOf<_i63.ChatBotEntity>() ||
            targetTypeOf == _typeOf<_i63.ChatBotEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i64.SelectInviteesOptions>() ||
            sourceTypeOf == _typeOf<_i64.SelectInviteesOptions?>()) &&
        (targetTypeOf == _typeOf<_i65.SelectMemberEntity>() ||
            targetTypeOf == _typeOf<_i65.SelectMemberEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i53.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i53.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i51.Assignee>() ||
            targetTypeOf == _typeOf<_i51.Assignee?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i54.WorkEntity>() ||
            sourceTypeOf == _typeOf<_i54.WorkEntity?>()) &&
        (targetTypeOf == _typeOf<_i51.Work>() ||
            targetTypeOf == _typeOf<_i51.Work?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i55.InfoEntity>() ||
            sourceTypeOf == _typeOf<_i55.InfoEntity?>()) &&
        (targetTypeOf == _typeOf<_i51.Info>() ||
            targetTypeOf == _typeOf<_i51.Info?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i57.ConversationEntity>() ||
            sourceTypeOf == _typeOf<_i57.ConversationEntity?>()) &&
        (targetTypeOf == _typeOf<_i56.Conversation>() ||
            targetTypeOf == _typeOf<_i56.Conversation?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i61.OrganizationRoleEntity>() ||
            sourceTypeOf == _typeOf<_i61.OrganizationRoleEntity?>()) &&
        (targetTypeOf == _typeOf<_i60.OrganizationRole>() ||
            targetTypeOf == _typeOf<_i60.OrganizationRole?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i59.OrganizationDepartmentEntity>() ||
            sourceTypeOf == _typeOf<_i59.OrganizationDepartmentEntity?>()) &&
        (targetTypeOf == _typeOf<_i58.OrganizationDepartment>() ||
            targetTypeOf == _typeOf<_i58.OrganizationDepartment?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i63.ChatBotEntity>() ||
            sourceTypeOf == _typeOf<_i63.ChatBotEntity?>()) &&
        (targetTypeOf == _typeOf<_i62.ChatBotModel>() ||
            targetTypeOf == _typeOf<_i62.ChatBotModel?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i66.UploadFileURLResponseModel>() ||
            sourceTypeOf == _typeOf<_i66.UploadFileURLResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i67.GpAttachmentFileUrlEntity>() ||
            targetTypeOf == _typeOf<_i67.GpAttachmentFileUrlEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i68.UploadFileResponseModel>() ||
            sourceTypeOf == _typeOf<_i68.UploadFileResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i69.UploadFileResponseModelV2>() ||
            sourceTypeOf == _typeOf<_i69.UploadFileResponseModelV2?>()) &&
        (targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i70.UploadImageResponseModel>() ||
            sourceTypeOf == _typeOf<_i70.UploadImageResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i67.GpAttachmentFileUrlEntity>() ||
            sourceTypeOf == _typeOf<_i67.GpAttachmentFileUrlEntity?>()) &&
        (targetTypeOf == _typeOf<_i66.UploadFileURLResponseModel>() ||
            targetTypeOf == _typeOf<_i66.UploadFileURLResponseModel?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i67.GPAttachmentFileEntity>() ||
            sourceTypeOf == _typeOf<_i67.GPAttachmentFileEntity?>()) &&
        (targetTypeOf == _typeOf<_i68.UploadFileResponseModel>() ||
            targetTypeOf == _typeOf<_i68.UploadFileResponseModel?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i67.GPAttachmentFileEntity>() ||
            sourceTypeOf == _typeOf<_i67.GPAttachmentFileEntity?>()) &&
        (targetTypeOf == _typeOf<_i69.UploadFileResponseModelV2>() ||
            targetTypeOf == _typeOf<_i69.UploadFileResponseModelV2?>())) {
      return true;
    }
    if (recursive) {
      for (final mappr in _delegates) {
        if (mappr.canConvert<SOURCE, TARGET>()) {
          return true;
        }
      }
    }
    return false;
  }

  
  
  @override
  TARGET convert<SOURCE, TARGET>(SOURCE? model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _convert(model)!;
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convert(model)!;
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  TARGET? tryConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _safeConvert(
        model,
        onMappingError: onMappingError,
      );
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvert(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    return null;
  }

  
  
  @override
  Iterable<TARGET> convertIterable<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET>((item) => _convert(item)!);
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertIterable(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Iterable<TARGET?> tryConvertIterable<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET?>(
          (item) => _safeConvert(item, onMappingError: onMappingError));
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertIterable(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  List<TARGET> convertList<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertList(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  List<TARGET?> tryConvertList<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertList(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  Set<TARGET> convertSet<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertSet(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Set<TARGET?> tryConvertSet<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertSet(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  TARGET? _convert<SOURCE, TARGET>(
    SOURCE? model, {
    bool canReturnNull = false,
  }) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.TicketListResponse>() ||
            sourceTypeOf == _typeOf<_i2.TicketListResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.TicketEntity>() ||
            targetTypeOf == _typeOf<_i3.TicketEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$TicketListResponse_To__i3$TicketEntity(
          (model as _i2.TicketListResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.TicketCreatorResponse>() ||
            sourceTypeOf == _typeOf<_i4.TicketCreatorResponse?>()) &&
        (targetTypeOf == _typeOf<_i5.TicketCreatorEntity>() ||
            targetTypeOf == _typeOf<_i5.TicketCreatorEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity(
          (model as _i4.TicketCreatorResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i6.TicketActivityContentResponse>() ||
            sourceTypeOf == _typeOf<_i6.TicketActivityContentResponse?>()) &&
        (targetTypeOf == _typeOf<_i7.TicketActivityContentEntity>() ||
            targetTypeOf == _typeOf<_i7.TicketActivityContentEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i6$TicketActivityContentResponse_To__i7$TicketActivityContentEntity(
          (model as _i6.TicketActivityContentResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i6.TicketActivityContentItemResponse>() ||
            sourceTypeOf ==
                _typeOf<_i6.TicketActivityContentItemResponse?>()) &&
        (targetTypeOf == _typeOf<_i7.TicketActivityContentItemEntity>() ||
            targetTypeOf == _typeOf<_i7.TicketActivityContentItemEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i6$TicketActivityContentItemResponse_To__i7$TicketActivityContentItemEntity(
          (model as _i6.TicketActivityContentItemResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i6.TicketHighlightResponse>() ||
            sourceTypeOf == _typeOf<_i6.TicketHighlightResponse?>()) &&
        (targetTypeOf == _typeOf<_i7.TicketHighlightEntity>() ||
            targetTypeOf == _typeOf<_i7.TicketHighlightEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i6$TicketHighlightResponse_To__i7$TicketHighlightEntity(
          (model as _i6.TicketHighlightResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i2.RefTicketResponse>() ||
            sourceTypeOf == _typeOf<_i2.RefTicketResponse?>()) &&
        (targetTypeOf == _typeOf<_i3.RefTicketEntity>() ||
            targetTypeOf == _typeOf<_i3.RefTicketEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$RefTicketResponse_To__i3$RefTicketEntity(
          (model as _i2.RefTicketResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i8.TicketActivityResponse>() ||
            sourceTypeOf == _typeOf<_i8.TicketActivityResponse?>()) &&
        (targetTypeOf == _typeOf<_i9.TicketActivityEntity>() ||
            targetTypeOf == _typeOf<_i9.TicketActivityEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i8$TicketActivityResponse_To__i9$TicketActivityEntity(
          (model as _i8.TicketActivityResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i10.TicketShiftDetailResponse>() ||
            sourceTypeOf == _typeOf<_i10.TicketShiftDetailResponse?>()) &&
        (targetTypeOf == _typeOf<_i11.TicketShiftDetailEntity>() ||
            targetTypeOf == _typeOf<_i11.TicketShiftDetailEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i10$TicketShiftDetailResponse_To__i11$TicketShiftDetailEntity(
          (model as _i10.TicketShiftDetailResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i12.TicketAdditionalRequestResponse>() ||
            sourceTypeOf == _typeOf<_i12.TicketAdditionalRequestResponse?>()) &&
        (targetTypeOf == _typeOf<_i13.TicketAdditionalRequestEntity>() ||
            targetTypeOf == _typeOf<_i13.TicketAdditionalRequestEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i12$TicketAdditionalRequestResponse_To__i13$TicketAdditionalRequestEntity(
          (model as _i12.TicketAdditionalRequestResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i14.TicketSLAResponse>() ||
            sourceTypeOf == _typeOf<_i14.TicketSLAResponse?>()) &&
        (targetTypeOf == _typeOf<_i15.TicketSLAEntity>() ||
            targetTypeOf == _typeOf<_i15.TicketSLAEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i14$TicketSLAResponse_To__i15$TicketSLAEntity(
          (model as _i14.TicketSLAResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i16.TicketSLAAggResponse>() ||
            sourceTypeOf == _typeOf<_i16.TicketSLAAggResponse?>()) &&
        (targetTypeOf == _typeOf<_i17.TicketSLAAggEntity>() ||
            targetTypeOf == _typeOf<_i17.TicketSLAAggEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i16$TicketSLAAggResponse_To__i17$TicketSLAAggEntity(
          (model as _i16.TicketSLAAggResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i16.TicketShiftAggResponse>() ||
            sourceTypeOf == _typeOf<_i16.TicketShiftAggResponse?>()) &&
        (targetTypeOf == _typeOf<_i17.TicketShiftAggEntity>() ||
            targetTypeOf == _typeOf<_i17.TicketShiftAggEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i16$TicketShiftAggResponse_To__i17$TicketShiftAggEntity(
          (model as _i16.TicketShiftAggResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i14.TicketSLAPriorityLevelsResponse>() ||
            sourceTypeOf == _typeOf<_i14.TicketSLAPriorityLevelsResponse?>()) &&
        (targetTypeOf == _typeOf<_i15.TicketSLAPriorityLevelsEntity>() ||
            targetTypeOf == _typeOf<_i15.TicketSLAPriorityLevelsEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i14$TicketSLAPriorityLevelsResponse_To__i15$TicketSLAPriorityLevelsEntity(
          (model as _i14.TicketSLAPriorityLevelsResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i14.TicketSLAPriorityLevelFirstResponse>() ||
            sourceTypeOf ==
                _typeOf<_i14.TicketSLAPriorityLevelFirstResponse?>()) &&
        (targetTypeOf == _typeOf<_i15.TicketSLAPriorityLevelFirstEntity>() ||
            targetTypeOf ==
                _typeOf<_i15.TicketSLAPriorityLevelFirstEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i14$TicketSLAPriorityLevelFirstResponse_To__i15$TicketSLAPriorityLevelFirstEntity(
          (model as _i14.TicketSLAPriorityLevelFirstResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i14.TicketSLANotificationResponse>() ||
            sourceTypeOf == _typeOf<_i14.TicketSLANotificationResponse?>()) &&
        (targetTypeOf == _typeOf<_i15.TicketSLANotificationEntity>() ||
            targetTypeOf == _typeOf<_i15.TicketSLANotificationEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i14$TicketSLANotificationResponse_To__i15$TicketSLANotificationEntity(
          (model as _i14.TicketSLANotificationResponse?)) as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i14.TicketSLANotificationDurationResponse>() ||
            sourceTypeOf ==
                _typeOf<_i14.TicketSLANotificationDurationResponse?>()) &&
        (targetTypeOf == _typeOf<_i15.TicketSLANotificationDurationEntity>() ||
            targetTypeOf ==
                _typeOf<_i15.TicketSLANotificationDurationEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i14$TicketSLANotificationDurationResponse_To__i15$TicketSLANotificationDurationEntity(
          (model as _i14.TicketSLANotificationDurationResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i18.TickeUserRoleResponse>() ||
            sourceTypeOf == _typeOf<_i18.TickeUserRoleResponse?>()) &&
        (targetTypeOf == _typeOf<_i19.TickeUserRoleEntity>() ||
            targetTypeOf == _typeOf<_i19.TickeUserRoleEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i18$TickeUserRoleResponse_To__i19$TickeUserRoleEntity(
          (model as _i18.TickeUserRoleResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i16.TicketFlowChartResponse>() ||
            sourceTypeOf == _typeOf<_i16.TicketFlowChartResponse?>()) &&
        (targetTypeOf == _typeOf<_i17.TicketFlowChartEntity>() ||
            targetTypeOf == _typeOf<_i17.TicketFlowChartEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i16$TicketFlowChartResponse_To__i17$TicketFlowChartEntity(
          (model as _i16.TicketFlowChartResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i16.TicketFlowchartNodeResponse>() ||
            sourceTypeOf == _typeOf<_i16.TicketFlowchartNodeResponse?>()) &&
        (targetTypeOf == _typeOf<_i17.TicketFlowChartNodeEntity>() ||
            targetTypeOf == _typeOf<_i17.TicketFlowChartNodeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i16$TicketFlowchartNodeResponse_To__i17$TicketFlowChartNodeEntity(
          (model as _i16.TicketFlowchartNodeResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i20.TicketNodeOptionResponse>() ||
            sourceTypeOf == _typeOf<_i20.TicketNodeOptionResponse?>()) &&
        (targetTypeOf == _typeOf<_i21.TicketNodeOptionEntity>() ||
            targetTypeOf == _typeOf<_i21.TicketNodeOptionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i20$TicketNodeOptionResponse_To__i21$TicketNodeOptionEntity(
          (model as _i20.TicketNodeOptionResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i16.TicketFlowchartEdgeResponse>() ||
            sourceTypeOf == _typeOf<_i16.TicketFlowchartEdgeResponse?>()) &&
        (targetTypeOf == _typeOf<_i17.TicketFlowchartEdgeEntity>() ||
            targetTypeOf == _typeOf<_i17.TicketFlowchartEdgeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i16$TicketFlowchartEdgeResponse_To__i17$TicketFlowchartEdgeEntity(
          (model as _i16.TicketFlowchartEdgeResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i20.TicketNodeResponse>() ||
            sourceTypeOf == _typeOf<_i20.TicketNodeResponse?>()) &&
        (targetTypeOf == _typeOf<_i21.TicketNodeEntity>() ||
            targetTypeOf == _typeOf<_i21.TicketNodeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i20$TicketNodeResponse_To__i21$TicketNodeEntity(
          (model as _i20.TicketNodeResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i22.TicketAssigneeResponse>() ||
            sourceTypeOf == _typeOf<_i22.TicketAssigneeResponse?>()) &&
        (targetTypeOf == _typeOf<_i23.TicketAssigneeEntity>() ||
            targetTypeOf == _typeOf<_i23.TicketAssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
          (model as _i22.TicketAssigneeResponse?)) as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAssigneeResponse>() ||
            sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAssigneeResponse?>()) &&
        (targetTypeOf == _typeOf<_i25.WorkflowAdvanceSettingAssigneeEntity>() ||
            targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i24$WorkflowAdvanceSettingAssigneeResponse_To__i25$WorkflowAdvanceSettingAssigneeEntity(
          (model as _i24.WorkflowAdvanceSettingAssigneeResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i26.TicketCommentResponse>() ||
            sourceTypeOf == _typeOf<_i26.TicketCommentResponse?>()) &&
        (targetTypeOf == _typeOf<_i27.TicketCommentEntity>() ||
            targetTypeOf == _typeOf<_i27.TicketCommentEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i26$TicketCommentResponse_To__i27$TicketCommentEntity(
          (model as _i26.TicketCommentResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i26.CommentAsResponse>() ||
            sourceTypeOf == _typeOf<_i26.CommentAsResponse?>()) &&
        (targetTypeOf == _typeOf<_i28.CommentAs>() ||
            targetTypeOf == _typeOf<_i28.CommentAs?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i26$CommentAsResponse_To__i28$CommentAs(
          (model as _i26.CommentAsResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i26.MentionResponse>() ||
            sourceTypeOf == _typeOf<_i26.MentionResponse?>()) &&
        (targetTypeOf == _typeOf<_i28.Mentions>() ||
            targetTypeOf == _typeOf<_i28.Mentions?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i26$MentionResponse_To__i28$Mentions(
          (model as _i26.MentionResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i26.MediasResponse>() ||
            sourceTypeOf == _typeOf<_i26.MediasResponse?>()) &&
        (targetTypeOf == _typeOf<_i28.Medias>() ||
            targetTypeOf == _typeOf<_i28.Medias?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i26$MediasResponse_To__i28$Medias(
          (model as _i26.MediasResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i27.TicketCommentEntity>() ||
            sourceTypeOf == _typeOf<_i27.TicketCommentEntity?>()) &&
        (targetTypeOf == _typeOf<_i29.TicketPostCommentsRequestParams>() ||
            targetTypeOf == _typeOf<_i29.TicketPostCommentsRequestParams?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i27$TicketCommentEntity_To__i29$TicketPostCommentsRequestParams(
          (model as _i27.TicketCommentEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i28.Comment>() ||
            sourceTypeOf == _typeOf<_i28.Comment?>()) &&
        (targetTypeOf == _typeOf<_i30.TicketEditCommentsRequestParams>() ||
            targetTypeOf == _typeOf<_i30.TicketEditCommentsRequestParams?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i28$Comment_To__i30$TicketEditCommentsRequestParams(
          (model as _i28.Comment?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i28.Comment>() ||
            sourceTypeOf == _typeOf<_i28.Comment?>()) &&
        (targetTypeOf == _typeOf<_i27.TicketCommentEntity>() ||
            targetTypeOf == _typeOf<_i27.TicketCommentEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i28$Comment_To__i27$TicketCommentEntity(
          (model as _i28.Comment?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i20.TicketNodeTagResponse>() ||
            sourceTypeOf == _typeOf<_i20.TicketNodeTagResponse?>()) &&
        (targetTypeOf == _typeOf<_i21.TicketNodeTagEntity>() ||
            targetTypeOf == _typeOf<_i21.TicketNodeTagEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i20$TicketNodeTagResponse_To__i21$TicketNodeTagEntity(
          (model as _i20.TicketNodeTagResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i3.TicketEntity>() ||
            sourceTypeOf == _typeOf<_i3.TicketEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.TicketListResponse>() ||
            targetTypeOf == _typeOf<_i2.TicketListResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i3$TicketEntity_To__i2$TicketListResponse(
          (model as _i3.TicketEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i5.TicketCreatorEntity>() ||
            sourceTypeOf == _typeOf<_i5.TicketCreatorEntity?>()) &&
        (targetTypeOf == _typeOf<_i4.TicketCreatorResponse>() ||
            targetTypeOf == _typeOf<_i4.TicketCreatorResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse(
          (model as _i5.TicketCreatorEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i7.TicketActivityContentEntity>() ||
            sourceTypeOf == _typeOf<_i7.TicketActivityContentEntity?>()) &&
        (targetTypeOf == _typeOf<_i6.TicketActivityContentResponse>() ||
            targetTypeOf == _typeOf<_i6.TicketActivityContentResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i7$TicketActivityContentEntity_To__i6$TicketActivityContentResponse(
          (model as _i7.TicketActivityContentEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i7.TicketActivityContentItemEntity>() ||
            sourceTypeOf == _typeOf<_i7.TicketActivityContentItemEntity?>()) &&
        (targetTypeOf == _typeOf<_i6.TicketActivityContentItemResponse>() ||
            targetTypeOf ==
                _typeOf<_i6.TicketActivityContentItemResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i7$TicketActivityContentItemEntity_To__i6$TicketActivityContentItemResponse(
          (model as _i7.TicketActivityContentItemEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i7.TicketHighlightEntity>() ||
            sourceTypeOf == _typeOf<_i7.TicketHighlightEntity?>()) &&
        (targetTypeOf == _typeOf<_i6.TicketHighlightResponse>() ||
            targetTypeOf == _typeOf<_i6.TicketHighlightResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i7$TicketHighlightEntity_To__i6$TicketHighlightResponse(
          (model as _i7.TicketHighlightEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i3.RefTicketEntity>() ||
            sourceTypeOf == _typeOf<_i3.RefTicketEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.RefTicketResponse>() ||
            targetTypeOf == _typeOf<_i2.RefTicketResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i3$RefTicketEntity_To__i2$RefTicketResponse(
          (model as _i3.RefTicketEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.TicketActivityEntity>() ||
            sourceTypeOf == _typeOf<_i9.TicketActivityEntity?>()) &&
        (targetTypeOf == _typeOf<_i8.TicketActivityResponse>() ||
            targetTypeOf == _typeOf<_i8.TicketActivityResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$TicketActivityEntity_To__i8$TicketActivityResponse(
          (model as _i9.TicketActivityEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i11.TicketShiftDetailEntity>() ||
            sourceTypeOf == _typeOf<_i11.TicketShiftDetailEntity?>()) &&
        (targetTypeOf == _typeOf<_i10.TicketShiftDetailResponse>() ||
            targetTypeOf == _typeOf<_i10.TicketShiftDetailResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i11$TicketShiftDetailEntity_To__i10$TicketShiftDetailResponse(
          (model as _i11.TicketShiftDetailEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i13.TicketAdditionalRequestEntity>() ||
            sourceTypeOf == _typeOf<_i13.TicketAdditionalRequestEntity?>()) &&
        (targetTypeOf == _typeOf<_i12.TicketAdditionalRequestResponse>() ||
            targetTypeOf == _typeOf<_i12.TicketAdditionalRequestResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i13$TicketAdditionalRequestEntity_To__i12$TicketAdditionalRequestResponse(
          (model as _i13.TicketAdditionalRequestEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i31.TicketOnHoldRequestEntity>() ||
            sourceTypeOf == _typeOf<_i31.TicketOnHoldRequestEntity?>()) &&
        (targetTypeOf == _typeOf<_i32.TicketOnHoldRequestResponse>() ||
            targetTypeOf == _typeOf<_i32.TicketOnHoldRequestResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i31$TicketOnHoldRequestEntity_To__i32$TicketOnHoldRequestResponse(
          (model as _i31.TicketOnHoldRequestEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i33.TicketReopenRequestEntity>() ||
            sourceTypeOf == _typeOf<_i33.TicketReopenRequestEntity?>()) &&
        (targetTypeOf == _typeOf<_i34.TicketReopenRequestResponse>() ||
            targetTypeOf == _typeOf<_i34.TicketReopenRequestResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i33$TicketReopenRequestEntity_To__i34$TicketReopenRequestResponse(
          (model as _i33.TicketReopenRequestEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i17.TicketSLAAggEntity>() ||
            sourceTypeOf == _typeOf<_i17.TicketSLAAggEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.TicketSLAAggResponse>() ||
            targetTypeOf == _typeOf<_i16.TicketSLAAggResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i17$TicketSLAAggEntity_To__i16$TicketSLAAggResponse(
          (model as _i17.TicketSLAAggEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i17.TicketShiftAggEntity>() ||
            sourceTypeOf == _typeOf<_i17.TicketShiftAggEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.TicketShiftAggResponse>() ||
            targetTypeOf == _typeOf<_i16.TicketShiftAggResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i17$TicketShiftAggEntity_To__i16$TicketShiftAggResponse(
          (model as _i17.TicketShiftAggEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.TicketSLAEntity>() ||
            sourceTypeOf == _typeOf<_i15.TicketSLAEntity?>()) &&
        (targetTypeOf == _typeOf<_i14.TicketSLAResponse>() ||
            targetTypeOf == _typeOf<_i14.TicketSLAResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$TicketSLAEntity_To__i14$TicketSLAResponse(
          (model as _i15.TicketSLAEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.TicketSLAPriorityLevelsEntity>() ||
            sourceTypeOf == _typeOf<_i15.TicketSLAPriorityLevelsEntity?>()) &&
        (targetTypeOf == _typeOf<_i14.TicketSLAPriorityLevelsResponse>() ||
            targetTypeOf == _typeOf<_i14.TicketSLAPriorityLevelsResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$TicketSLAPriorityLevelsEntity_To__i14$TicketSLAPriorityLevelsResponse(
          (model as _i15.TicketSLAPriorityLevelsEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.TicketSLAPriorityLevelFirstEntity>() ||
            sourceTypeOf ==
                _typeOf<_i15.TicketSLAPriorityLevelFirstEntity?>()) &&
        (targetTypeOf == _typeOf<_i14.TicketSLAPriorityLevelFirstResponse>() ||
            targetTypeOf ==
                _typeOf<_i14.TicketSLAPriorityLevelFirstResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$TicketSLAPriorityLevelFirstEntity_To__i14$TicketSLAPriorityLevelFirstResponse(
          (model as _i15.TicketSLAPriorityLevelFirstEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.TicketSLANotificationEntity>() ||
            sourceTypeOf == _typeOf<_i15.TicketSLANotificationEntity?>()) &&
        (targetTypeOf == _typeOf<_i14.TicketSLANotificationResponse>() ||
            targetTypeOf == _typeOf<_i14.TicketSLANotificationResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$TicketSLANotificationEntity_To__i14$TicketSLANotificationResponse(
          (model as _i15.TicketSLANotificationEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.TicketSLANotificationDurationEntity>() ||
            sourceTypeOf ==
                _typeOf<_i15.TicketSLANotificationDurationEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i14.TicketSLANotificationDurationResponse>() ||
            targetTypeOf ==
                _typeOf<_i14.TicketSLANotificationDurationResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$TicketSLANotificationDurationEntity_To__i14$TicketSLANotificationDurationResponse(
          (model as _i15.TicketSLANotificationDurationEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i19.TickeUserRoleEntity>() ||
            sourceTypeOf == _typeOf<_i19.TickeUserRoleEntity?>()) &&
        (targetTypeOf == _typeOf<_i18.TickeUserRoleResponse>() ||
            targetTypeOf == _typeOf<_i18.TickeUserRoleResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i19$TickeUserRoleEntity_To__i18$TickeUserRoleResponse(
          (model as _i19.TickeUserRoleEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i17.TicketFlowChartEntity>() ||
            sourceTypeOf == _typeOf<_i17.TicketFlowChartEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.TicketFlowChartResponse>() ||
            targetTypeOf == _typeOf<_i16.TicketFlowChartResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i17$TicketFlowChartEntity_To__i16$TicketFlowChartResponse(
          (model as _i17.TicketFlowChartEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i17.TicketFlowChartNodeEntity>() ||
            sourceTypeOf == _typeOf<_i17.TicketFlowChartNodeEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.TicketFlowchartNodeResponse>() ||
            targetTypeOf == _typeOf<_i16.TicketFlowchartNodeResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i17$TicketFlowChartNodeEntity_To__i16$TicketFlowchartNodeResponse(
          (model as _i17.TicketFlowChartNodeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i21.TicketNodeOptionEntity>() ||
            sourceTypeOf == _typeOf<_i21.TicketNodeOptionEntity?>()) &&
        (targetTypeOf == _typeOf<_i20.TicketNodeOptionResponse>() ||
            targetTypeOf == _typeOf<_i20.TicketNodeOptionResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i21$TicketNodeOptionEntity_To__i20$TicketNodeOptionResponse(
          (model as _i21.TicketNodeOptionEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i17.TicketFlowchartEdgeEntity>() ||
            sourceTypeOf == _typeOf<_i17.TicketFlowchartEdgeEntity?>()) &&
        (targetTypeOf == _typeOf<_i16.TicketFlowchartEdgeResponse>() ||
            targetTypeOf == _typeOf<_i16.TicketFlowchartEdgeResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i17$TicketFlowchartEdgeEntity_To__i16$TicketFlowchartEdgeResponse(
          (model as _i17.TicketFlowchartEdgeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i21.TicketNodeEntity>() ||
            sourceTypeOf == _typeOf<_i21.TicketNodeEntity?>()) &&
        (targetTypeOf == _typeOf<_i20.TicketNodeResponse>() ||
            targetTypeOf == _typeOf<_i20.TicketNodeResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i21$TicketNodeEntity_To__i20$TicketNodeResponse(
          (model as _i21.TicketNodeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i23.TicketAssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i23.TicketAssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i22.TicketAssigneeResponse>() ||
            targetTypeOf == _typeOf<_i22.TicketAssigneeResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
          (model as _i23.TicketAssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i25.WorkflowAdvanceSettingAssigneeEntity>() ||
            sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAssigneeEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAssigneeResponse>() ||
            targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAssigneeResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i25$WorkflowAdvanceSettingAssigneeEntity_To__i24$WorkflowAdvanceSettingAssigneeResponse(
          (model as _i25.WorkflowAdvanceSettingAssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i21.TicketNodeTagEntity>() ||
            sourceTypeOf == _typeOf<_i21.TicketNodeTagEntity?>()) &&
        (targetTypeOf == _typeOf<_i20.TicketNodeTagResponse>() ||
            targetTypeOf == _typeOf<_i20.TicketNodeTagResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i21$TicketNodeTagEntity_To__i20$TicketNodeTagResponse(
          (model as _i21.TicketNodeTagEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i35.WorkFlowWrapperResponse>() ||
            sourceTypeOf == _typeOf<_i35.WorkFlowWrapperResponse?>()) &&
        (targetTypeOf == _typeOf<_i36.WorkFlowWrapperEntity>() ||
            targetTypeOf == _typeOf<_i36.WorkFlowWrapperEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i35$WorkFlowWrapperResponse_To__i36$WorkFlowWrapperEntity(
          (model as _i35.WorkFlowWrapperResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i35.WorkFlowResponse>() ||
            sourceTypeOf == _typeOf<_i35.WorkFlowResponse?>()) &&
        (targetTypeOf == _typeOf<_i36.WorkFlowEntity>() ||
            targetTypeOf == _typeOf<_i36.WorkFlowEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i35$WorkFlowResponse_To__i36$WorkFlowEntity(
          (model as _i35.WorkFlowResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i20.WorkflowFormFieldPermissionsResponse>() ||
            sourceTypeOf ==
                _typeOf<_i20.WorkflowFormFieldPermissionsResponse?>()) &&
        (targetTypeOf == _typeOf<_i21.WorkflowFormFieldPermissionEntity>() ||
            targetTypeOf ==
                _typeOf<_i21.WorkflowFormFieldPermissionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i20$WorkflowFormFieldPermissionsResponse_To__i21$WorkflowFormFieldPermissionEntity(
          (model as _i20.WorkflowFormFieldPermissionsResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFormResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFormResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFormEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFormEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i37$WorkFlowFormResponse_To__i38$WorkFlowFormEntity(
          (model as _i37.WorkFlowFormResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFormFieldResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFormFieldResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFormFieldEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFormFieldEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i37$WorkFlowFormFieldResponse_To__i38$WorkFlowFormFieldEntity(
          (model as _i37.WorkFlowFormFieldResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFieldValuesResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFieldValuesResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFieldValuesEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFieldValuesEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i37$WorkFlowFieldValuesResponse_To__i38$WorkFlowFieldValuesEntity(
          (model as _i37.WorkFlowFieldValuesResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFormOptionResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFormOptionResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFormOptionEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFormOptionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i37$WorkFlowFormOptionResponse_To__i38$WorkFlowFormOptionEntity(
          (model as _i37.WorkFlowFormOptionResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFormSelectionResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFormSelectionResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFormSelectionEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFormSelectionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i37$WorkFlowFormSelectionResponse_To__i38$WorkFlowFormSelectionEntity(
          (model as _i37.WorkFlowFormSelectionResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i37.WorkFlowFormColumnResponse>() ||
            sourceTypeOf == _typeOf<_i37.WorkFlowFormColumnResponse?>()) &&
        (targetTypeOf == _typeOf<_i38.WorkFlowFormColumnEntity>() ||
            targetTypeOf == _typeOf<_i38.WorkFlowFormColumnEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i37$WorkFlowFormColumnResponse_To__i38$WorkFlowFormColumnEntity(
          (model as _i37.WorkFlowFormColumnResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i39.WorkFlowStartNodeResponse>() ||
            sourceTypeOf == _typeOf<_i39.WorkFlowStartNodeResponse?>()) &&
        (targetTypeOf == _typeOf<_i40.WorkFlowStartNodeEntity>() ||
            targetTypeOf == _typeOf<_i40.WorkFlowStartNodeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i39$WorkFlowStartNodeResponse_To__i40$WorkFlowStartNodeEntity(
          (model as _i39.WorkFlowStartNodeResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i41.WorkFlowUserInfoResponse>() ||
            sourceTypeOf == _typeOf<_i41.WorkFlowUserInfoResponse?>()) &&
        (targetTypeOf == _typeOf<_i42.WorkFlowUserInfoEntity>() ||
            targetTypeOf == _typeOf<_i42.WorkFlowUserInfoEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i41$WorkFlowUserInfoResponse_To__i42$WorkFlowUserInfoEntity(
          (model as _i41.WorkFlowUserInfoResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i41.WorkFlowDeparmentResponse>() ||
            sourceTypeOf == _typeOf<_i41.WorkFlowDeparmentResponse?>()) &&
        (targetTypeOf == _typeOf<_i42.WorkFlowDeparmentEntity>() ||
            targetTypeOf == _typeOf<_i42.WorkFlowDeparmentEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i41$WorkFlowDeparmentResponse_To__i42$WorkFlowDeparmentEntity(
          (model as _i41.WorkFlowDeparmentResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i24.WorkflowAdvanceSettingResponse>() ||
            sourceTypeOf == _typeOf<_i24.WorkflowAdvanceSettingResponse?>()) &&
        (targetTypeOf == _typeOf<_i25.WorkflowAdvanceSettingEntity>() ||
            targetTypeOf == _typeOf<_i25.WorkflowAdvanceSettingEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i24$WorkflowAdvanceSettingResponse_To__i25$WorkflowAdvanceSettingEntity(
          (model as _i24.WorkflowAdvanceSettingResponse?)) as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowActionResponse>() ||
            sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowActionResponse?>()) &&
        (targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowActionEntity>() ||
            targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowActionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i24$WorkflowAdvanceSettingAllowActionResponse_To__i25$WorkflowAdvanceSettingAllowActionEntity(
              (model as _i24.WorkflowAdvanceSettingAllowActionResponse?))
          as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAdminOptionResponse>() ||
            sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAdminOptionResponse?>()) &&
        (targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAdminOptionEntity>() ||
            targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAdminOptionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i24$WorkflowAdvanceSettingAdminOptionResponse_To__i25$WorkflowAdvanceSettingAdminOptionEntity(
              (model as _i24.WorkflowAdvanceSettingAdminOptionResponse?))
          as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowOptionResponse>() ||
            sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowOptionResponse?>()) &&
        (targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowOptionEntity>() ||
            targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowOptionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i24$WorkflowAdvanceSettingAllowOptionResponse_To__i25$WorkflowAdvanceSettingAllowOptionEntity(
              (model as _i24.WorkflowAdvanceSettingAllowOptionResponse?))
          as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingRateOptionResponse>() ||
            sourceTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingRateOptionResponse?>()) &&
        (targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingRateOptionEntity>() ||
            targetTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingRateOptionEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i24$WorkflowAdvanceSettingRateOptionResponse_To__i25$WorkflowAdvanceSettingRateOptionEntity(
          (model as _i24.WorkflowAdvanceSettingRateOptionResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i43.WorkflowTagResponse>() ||
            sourceTypeOf == _typeOf<_i43.WorkflowTagResponse?>()) &&
        (targetTypeOf == _typeOf<_i44.WorkflowTagEntity>() ||
            targetTypeOf == _typeOf<_i44.WorkflowTagEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i43$WorkflowTagResponse_To__i44$WorkflowTagEntity(
          (model as _i43.WorkflowTagResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i36.WorkFlowWrapperEntity>() ||
            sourceTypeOf == _typeOf<_i36.WorkFlowWrapperEntity?>()) &&
        (targetTypeOf == _typeOf<_i35.WorkFlowWrapperResponse>() ||
            targetTypeOf == _typeOf<_i35.WorkFlowWrapperResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i36$WorkFlowWrapperEntity_To__i35$WorkFlowWrapperResponse(
          (model as _i36.WorkFlowWrapperEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i36.WorkFlowEntity>() ||
            sourceTypeOf == _typeOf<_i36.WorkFlowEntity?>()) &&
        (targetTypeOf == _typeOf<_i35.WorkFlowResponse>() ||
            targetTypeOf == _typeOf<_i35.WorkFlowResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i36$WorkFlowEntity_To__i35$WorkFlowResponse(
          (model as _i36.WorkFlowEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i21.WorkflowFormFieldPermissionEntity>() ||
            sourceTypeOf ==
                _typeOf<_i21.WorkflowFormFieldPermissionEntity?>()) &&
        (targetTypeOf == _typeOf<_i20.WorkflowFormFieldPermissionsResponse>() ||
            targetTypeOf ==
                _typeOf<_i20.WorkflowFormFieldPermissionsResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i21$WorkflowFormFieldPermissionEntity_To__i20$WorkflowFormFieldPermissionsResponse(
          (model as _i21.WorkflowFormFieldPermissionEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFormEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFormEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFormResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFormResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i38$WorkFlowFormEntity_To__i37$WorkFlowFormResponse(
          (model as _i38.WorkFlowFormEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFormFieldEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFormFieldEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFormFieldResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFormFieldResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i38$WorkFlowFormFieldEntity_To__i37$WorkFlowFormFieldResponse(
          (model as _i38.WorkFlowFormFieldEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFieldValuesEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFieldValuesEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFieldValuesResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFieldValuesResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i38$WorkFlowFieldValuesEntity_To__i37$WorkFlowFieldValuesResponse(
          (model as _i38.WorkFlowFieldValuesEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFormOptionEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFormOptionEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFormOptionResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFormOptionResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i38$WorkFlowFormOptionEntity_To__i37$WorkFlowFormOptionResponse(
          (model as _i38.WorkFlowFormOptionEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFormSelectionEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFormSelectionEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFormSelectionResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFormSelectionResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i38$WorkFlowFormSelectionEntity_To__i37$WorkFlowFormSelectionResponse(
          (model as _i38.WorkFlowFormSelectionEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i38.WorkFlowFormColumnEntity>() ||
            sourceTypeOf == _typeOf<_i38.WorkFlowFormColumnEntity?>()) &&
        (targetTypeOf == _typeOf<_i37.WorkFlowFormColumnResponse>() ||
            targetTypeOf == _typeOf<_i37.WorkFlowFormColumnResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i38$WorkFlowFormColumnEntity_To__i37$WorkFlowFormColumnResponse(
          (model as _i38.WorkFlowFormColumnEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i40.WorkFlowStartNodeEntity>() ||
            sourceTypeOf == _typeOf<_i40.WorkFlowStartNodeEntity?>()) &&
        (targetTypeOf == _typeOf<_i39.WorkFlowStartNodeResponse>() ||
            targetTypeOf == _typeOf<_i39.WorkFlowStartNodeResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i40$WorkFlowStartNodeEntity_To__i39$WorkFlowStartNodeResponse(
          (model as _i40.WorkFlowStartNodeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i42.WorkFlowUserInfoEntity>() ||
            sourceTypeOf == _typeOf<_i42.WorkFlowUserInfoEntity?>()) &&
        (targetTypeOf == _typeOf<_i41.WorkFlowUserInfoResponse>() ||
            targetTypeOf == _typeOf<_i41.WorkFlowUserInfoResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i42$WorkFlowUserInfoEntity_To__i41$WorkFlowUserInfoResponse(
          (model as _i42.WorkFlowUserInfoEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i42.WorkFlowDeparmentEntity>() ||
            sourceTypeOf == _typeOf<_i42.WorkFlowDeparmentEntity?>()) &&
        (targetTypeOf == _typeOf<_i41.WorkFlowDeparmentResponse>() ||
            targetTypeOf == _typeOf<_i41.WorkFlowDeparmentResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i42$WorkFlowDeparmentEntity_To__i41$WorkFlowDeparmentResponse(
          (model as _i42.WorkFlowDeparmentEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i25.WorkflowAdvanceSettingEntity>() ||
            sourceTypeOf == _typeOf<_i25.WorkflowAdvanceSettingEntity?>()) &&
        (targetTypeOf == _typeOf<_i24.WorkflowAdvanceSettingResponse>() ||
            targetTypeOf == _typeOf<_i24.WorkflowAdvanceSettingResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i25$WorkflowAdvanceSettingEntity_To__i24$WorkflowAdvanceSettingResponse(
          (model as _i25.WorkflowAdvanceSettingEntity?)) as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowActionEntity>() ||
            sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowActionEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowActionResponse>() ||
            targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowActionResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i25$WorkflowAdvanceSettingAllowActionEntity_To__i24$WorkflowAdvanceSettingAllowActionResponse(
          (model as _i25.WorkflowAdvanceSettingAllowActionEntity?)) as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAdminOptionEntity>() ||
            sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAdminOptionEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAdminOptionResponse>() ||
            targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAdminOptionResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i25$WorkflowAdvanceSettingAdminOptionEntity_To__i24$WorkflowAdvanceSettingAdminOptionResponse(
          (model as _i25.WorkflowAdvanceSettingAdminOptionEntity?)) as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowOptionEntity>() ||
            sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingAllowOptionEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowOptionResponse>() ||
            targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingAllowOptionResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i25$WorkflowAdvanceSettingAllowOptionEntity_To__i24$WorkflowAdvanceSettingAllowOptionResponse(
          (model as _i25.WorkflowAdvanceSettingAllowOptionEntity?)) as TARGET);
    }
    if ((sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingRateOptionEntity>() ||
            sourceTypeOf ==
                _typeOf<_i25.WorkflowAdvanceSettingRateOptionEntity?>()) &&
        (targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingRateOptionResponse>() ||
            targetTypeOf ==
                _typeOf<_i24.WorkflowAdvanceSettingRateOptionResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i25$WorkflowAdvanceSettingRateOptionEntity_To__i24$WorkflowAdvanceSettingRateOptionResponse(
          (model as _i25.WorkflowAdvanceSettingRateOptionEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i32.TicketOnHoldRequestResponse>() ||
            sourceTypeOf == _typeOf<_i32.TicketOnHoldRequestResponse?>()) &&
        (targetTypeOf == _typeOf<_i31.TicketOnHoldRequestEntity>() ||
            targetTypeOf == _typeOf<_i31.TicketOnHoldRequestEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i32$TicketOnHoldRequestResponse_To__i31$TicketOnHoldRequestEntity(
          (model as _i32.TicketOnHoldRequestResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i34.TicketReopenRequestResponse>() ||
            sourceTypeOf == _typeOf<_i34.TicketReopenRequestResponse?>()) &&
        (targetTypeOf == _typeOf<_i33.TicketReopenRequestEntity>() ||
            targetTypeOf == _typeOf<_i33.TicketReopenRequestEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i34$TicketReopenRequestResponse_To__i33$TicketReopenRequestEntity(
          (model as _i34.TicketReopenRequestResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i45.TicketSpamRequestResponse>() ||
            sourceTypeOf == _typeOf<_i45.TicketSpamRequestResponse?>()) &&
        (targetTypeOf == _typeOf<_i46.TicketSpamRequestEntity>() ||
            targetTypeOf == _typeOf<_i46.TicketSpamRequestEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i45$TicketSpamRequestResponse_To__i46$TicketSpamRequestEntity(
          (model as _i45.TicketSpamRequestResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i46.TicketSpamRequestEntity>() ||
            sourceTypeOf == _typeOf<_i46.TicketSpamRequestEntity?>()) &&
        (targetTypeOf == _typeOf<_i45.TicketSpamRequestResponse>() ||
            targetTypeOf == _typeOf<_i45.TicketSpamRequestResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i46$TicketSpamRequestEntity_To__i45$TicketSpamRequestResponse(
          (model as _i46.TicketSpamRequestEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i44.WorkflowTagEntity>() ||
            sourceTypeOf == _typeOf<_i44.WorkflowTagEntity?>()) &&
        (targetTypeOf == _typeOf<_i43.WorkflowTagResponse>() ||
            targetTypeOf == _typeOf<_i43.WorkflowTagResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i44$WorkflowTagEntity_To__i43$WorkflowTagResponse(
          (model as _i44.WorkflowTagEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i47.TicketIsTicketAssigneeResponse>() ||
            sourceTypeOf == _typeOf<_i47.TicketIsTicketAssigneeResponse?>()) &&
        (targetTypeOf == _typeOf<_i48.TicketIsTicketAssigneeEntity>() ||
            targetTypeOf == _typeOf<_i48.TicketIsTicketAssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i47$TicketIsTicketAssigneeResponse_To__i48$TicketIsTicketAssigneeEntity(
          (model as _i47.TicketIsTicketAssigneeResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i48.TicketIsTicketAssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i48.TicketIsTicketAssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i47.TicketIsTicketAssigneeResponse>() ||
            targetTypeOf == _typeOf<_i47.TicketIsTicketAssigneeResponse?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i48$TicketIsTicketAssigneeEntity_To__i47$TicketIsTicketAssigneeResponse(
          (model as _i48.TicketIsTicketAssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i49.WorkFlowGroupWrapperResponse>() ||
            sourceTypeOf == _typeOf<_i49.WorkFlowGroupWrapperResponse?>()) &&
        (targetTypeOf == _typeOf<_i50.WorkFlowGroupWrapperEntity>() ||
            targetTypeOf == _typeOf<_i50.WorkFlowGroupWrapperEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i49$WorkFlowGroupWrapperResponse_To__i50$WorkFlowGroupWrapperEntity(
          (model as _i49.WorkFlowGroupWrapperResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i49.WorkFlowGroupResponse>() ||
            sourceTypeOf == _typeOf<_i49.WorkFlowGroupResponse?>()) &&
        (targetTypeOf == _typeOf<_i50.WorkFlowGroupEntity>() ||
            targetTypeOf == _typeOf<_i50.WorkFlowGroupEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i49$WorkFlowGroupResponse_To__i50$WorkFlowGroupEntity(
          (model as _i49.WorkFlowGroupResponse?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i51.Assignee>() ||
            sourceTypeOf == _typeOf<_i51.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i52.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i52.GPUserEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i51$Assignee_To__i52$GPUserEntity((model as _i51.Assignee?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i53.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i53.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i52.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i52.GPUserEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i53$AssigneeEntity_To__i52$GPUserEntity(
          (model as _i53.AssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i52.GPUserEntity>() ||
            sourceTypeOf == _typeOf<_i52.GPUserEntity?>()) &&
        (targetTypeOf == _typeOf<_i53.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i53.AssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i52$GPUserEntity_To__i53$AssigneeEntity(
          (model as _i52.GPUserEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i51.Assignee>() ||
            sourceTypeOf == _typeOf<_i51.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i53.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i53.AssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i51$Assignee_To__i53$AssigneeEntity(
          (model as _i51.Assignee?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i51.Work>() ||
            sourceTypeOf == _typeOf<_i51.Work?>()) &&
        (targetTypeOf == _typeOf<_i54.WorkEntity>() ||
            targetTypeOf == _typeOf<_i54.WorkEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i51$Work_To__i54$WorkEntity((model as _i51.Work?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i51.Info>() ||
            sourceTypeOf == _typeOf<_i51.Info?>()) &&
        (targetTypeOf == _typeOf<_i55.InfoEntity>() ||
            targetTypeOf == _typeOf<_i55.InfoEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i51$Info_To__i55$InfoEntity((model as _i51.Info?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i56.Conversation>() ||
            sourceTypeOf == _typeOf<_i56.Conversation?>()) &&
        (targetTypeOf == _typeOf<_i57.ConversationEntity>() ||
            targetTypeOf == _typeOf<_i57.ConversationEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i56$Conversation_To__i57$ConversationEntity(
          (model as _i56.Conversation?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i58.OrganizationDepartment>() ||
            sourceTypeOf == _typeOf<_i58.OrganizationDepartment?>()) &&
        (targetTypeOf == _typeOf<_i59.OrganizationDepartmentEntity>() ||
            targetTypeOf == _typeOf<_i59.OrganizationDepartmentEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i58$OrganizationDepartment_To__i59$OrganizationDepartmentEntity(
          (model as _i58.OrganizationDepartment?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i60.OrganizationRole>() ||
            sourceTypeOf == _typeOf<_i60.OrganizationRole?>()) &&
        (targetTypeOf == _typeOf<_i61.OrganizationRoleEntity>() ||
            targetTypeOf == _typeOf<_i61.OrganizationRoleEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i60$OrganizationRole_To__i61$OrganizationRoleEntity(
          (model as _i60.OrganizationRole?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i62.ChatBotModel>() ||
            sourceTypeOf == _typeOf<_i62.ChatBotModel?>()) &&
        (targetTypeOf == _typeOf<_i63.ChatBotEntity>() ||
            targetTypeOf == _typeOf<_i63.ChatBotEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i62$ChatBotModel_To__i63$ChatBotEntity(
          (model as _i62.ChatBotModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i64.SelectInviteesOptions>() ||
            sourceTypeOf == _typeOf<_i64.SelectInviteesOptions?>()) &&
        (targetTypeOf == _typeOf<_i65.SelectMemberEntity>() ||
            targetTypeOf == _typeOf<_i65.SelectMemberEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i64$SelectInviteesOptions_To__i65$SelectMemberEntity(
          (model as _i64.SelectInviteesOptions?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i53.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i53.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i51.Assignee>() ||
            targetTypeOf == _typeOf<_i51.Assignee?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i53$AssigneeEntity_To__i51$Assignee(
          (model as _i53.AssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i54.WorkEntity>() ||
            sourceTypeOf == _typeOf<_i54.WorkEntity?>()) &&
        (targetTypeOf == _typeOf<_i51.Work>() ||
            targetTypeOf == _typeOf<_i51.Work?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i54$WorkEntity_To__i51$Work((model as _i54.WorkEntity?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i55.InfoEntity>() ||
            sourceTypeOf == _typeOf<_i55.InfoEntity?>()) &&
        (targetTypeOf == _typeOf<_i51.Info>() ||
            targetTypeOf == _typeOf<_i51.Info?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i55$InfoEntity_To__i51$Info((model as _i55.InfoEntity?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i57.ConversationEntity>() ||
            sourceTypeOf == _typeOf<_i57.ConversationEntity?>()) &&
        (targetTypeOf == _typeOf<_i56.Conversation>() ||
            targetTypeOf == _typeOf<_i56.Conversation?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i57$ConversationEntity_To__i56$Conversation(
          (model as _i57.ConversationEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i61.OrganizationRoleEntity>() ||
            sourceTypeOf == _typeOf<_i61.OrganizationRoleEntity?>()) &&
        (targetTypeOf == _typeOf<_i60.OrganizationRole>() ||
            targetTypeOf == _typeOf<_i60.OrganizationRole?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i61$OrganizationRoleEntity_To__i60$OrganizationRole(
          (model as _i61.OrganizationRoleEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i59.OrganizationDepartmentEntity>() ||
            sourceTypeOf == _typeOf<_i59.OrganizationDepartmentEntity?>()) &&
        (targetTypeOf == _typeOf<_i58.OrganizationDepartment>() ||
            targetTypeOf == _typeOf<_i58.OrganizationDepartment?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i59$OrganizationDepartmentEntity_To__i58$OrganizationDepartment(
          (model as _i59.OrganizationDepartmentEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i63.ChatBotEntity>() ||
            sourceTypeOf == _typeOf<_i63.ChatBotEntity?>()) &&
        (targetTypeOf == _typeOf<_i62.ChatBotModel>() ||
            targetTypeOf == _typeOf<_i62.ChatBotModel?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i63$ChatBotEntity_To__i62$ChatBotModel(
          (model as _i63.ChatBotEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i66.UploadFileURLResponseModel>() ||
            sourceTypeOf == _typeOf<_i66.UploadFileURLResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i67.GpAttachmentFileUrlEntity>() ||
            targetTypeOf == _typeOf<_i67.GpAttachmentFileUrlEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i66$UploadFileURLResponseModel_To__i67$GpAttachmentFileUrlEntity(
          (model as _i66.UploadFileURLResponseModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i68.UploadFileResponseModel>() ||
            sourceTypeOf == _typeOf<_i68.UploadFileResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i68$UploadFileResponseModel_To__i67$GPAttachmentFileEntity(
          (model as _i68.UploadFileResponseModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i69.UploadFileResponseModelV2>() ||
            sourceTypeOf == _typeOf<_i69.UploadFileResponseModelV2?>()) &&
        (targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i69$UploadFileResponseModelV2_To__i67$GPAttachmentFileEntity(
          (model as _i69.UploadFileResponseModelV2?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i70.UploadImageResponseModel>() ||
            sourceTypeOf == _typeOf<_i70.UploadImageResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i67.GPAttachmentFileEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i70$UploadImageResponseModel_To__i67$GPAttachmentFileEntity(
          (model as _i70.UploadImageResponseModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i67.GpAttachmentFileUrlEntity>() ||
            sourceTypeOf == _typeOf<_i67.GpAttachmentFileUrlEntity?>()) &&
        (targetTypeOf == _typeOf<_i66.UploadFileURLResponseModel>() ||
            targetTypeOf == _typeOf<_i66.UploadFileURLResponseModel?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i67$GpAttachmentFileUrlEntity_To__i66$UploadFileURLResponseModel(
          (model as _i67.GpAttachmentFileUrlEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i67.GPAttachmentFileEntity>() ||
            sourceTypeOf == _typeOf<_i67.GPAttachmentFileEntity?>()) &&
        (targetTypeOf == _typeOf<_i68.UploadFileResponseModel>() ||
            targetTypeOf == _typeOf<_i68.UploadFileResponseModel?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i67$GPAttachmentFileEntity_To__i68$UploadFileResponseModel(
          (model as _i67.GPAttachmentFileEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i67.GPAttachmentFileEntity>() ||
            sourceTypeOf == _typeOf<_i67.GPAttachmentFileEntity?>()) &&
        (targetTypeOf == _typeOf<_i69.UploadFileResponseModelV2>() ||
            targetTypeOf == _typeOf<_i69.UploadFileResponseModelV2?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i67$GPAttachmentFileEntity_To__i69$UploadFileResponseModelV2(
          (model as _i67.GPAttachmentFileEntity?)) as TARGET);
    }
    throw Exception('No ${model.runtimeType} -> $targetTypeOf mapping.');
  }

  TARGET? _safeConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (!useSafeMapping<SOURCE, TARGET>()) {
      return _convert(
        model,
        canReturnNull: true,
      );
    }
    try {
      return _convert(
        model,
        canReturnNull: true,
      );
    } catch (e, s) {
      onMappingError?.call(e, s, model);
      return null;
    }
  }

  
  
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return false;
  }

  _i3.TicketEntity _map__i2$TicketListResponse_To__i3$TicketEntity(
      _i2.TicketListResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketListResponse → TicketEntity failed because TicketListResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketListResponse, TicketEntity> to handle null values during mapping.');
    }
    return _i3.TicketEntity(
      id: _i71.TicketEntityMapper.mapId(model),
      code: model.code,
      title: model.title,
      assigneeId: model.assigneeId,
      currentNodeId: model.currentNodeId,
      priority: model.priority,
      status: model.status,
      nodeStatus: model.nodeStatus,
      workspaceId: model.workspaceId,
      workflowId: model.workflowId,
      workflow: _map__i35$WorkFlowResponse_To__i36$WorkFlowEntity_Nullable(
          model.workflow),
      currentNodeName: model.currentNodeName,
      reopened: model.reopened,
      ratingPoint: model.ratingPoint,
      isPrivate: model.isPrivate,
      closedAt: model.closedAt,
      nodeDeadlineAt: model.nodeDeadlineAt,
      latestRecentActivityAt: model.latestRecentActivityAt,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      deletedAt: model.deletedAt,
      deadlineFirstResponseAt: model.deadlineFirstResponseAt,
      firstResponseAt: model.firstResponseAt,
      deadlineProcessAt: model.deadlineProcessAt,
      processAt: model.processAt,
      absoluteSecondsUntilResponseDeadline:
          model.absoluteSecondsUntilResponseDeadline,
      absoluteSecondsUntilProcessDeadline:
          model.absoluteSecondsUntilProcessDeadline,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      creator:
          _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity_Nullable(
              model.creator),
      assignee:
          _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity_Nullable(
              model.assignee),
      sla: _map__i14$TicketSLAResponse_To__i15$TicketSLAEntity_Nullable(
          model.sla),
      recentActivity:
          _map__i8$TicketActivityResponse_To__i9$TicketActivityEntity_Nullable(
              model.recentActivity),
      userRole:
          _map__i18$TickeUserRoleResponse_To__i19$TickeUserRoleEntity_Nullable(
              model.userRole),
      fieldValues: model.fieldValues
          ?.map<_i38.WorkFlowFieldValuesEntity>((value) =>
              _map__i37$WorkFlowFieldValuesResponse_To__i38$WorkFlowFieldValuesEntity(
                  value))
          .toList(),
      refTickets: model.refTickets
          ?.map<_i3.RefTicketEntity>((value) =>
              _map__i2$RefTicketResponse_To__i3$RefTicketEntity(value))
          .toList(),
      refTicketIds: model.refTicketIds,
      adminsWorkflowGroup: model.adminsWorkflowGroup
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      review: model.review,
      adminsWorkflow: model.adminsWorkflow
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
    );
  }

  _i5.TicketCreatorEntity
      _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity(
          _i4.TicketCreatorResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketCreatorResponse → TicketCreatorEntity failed because TicketCreatorResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketCreatorResponse, TicketCreatorEntity> to handle null values during mapping.');
    }
    return _i5.TicketCreatorEntity(
      id: model.id,
      displayName: model.displayName,
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
      email: model.email,
      phoneNumber: model.phoneNumber,
      title: model.title,
      department: model.department,
      companyName: model.companyName,
      emloyeeCode: model.emloyeeCode,
      identifierCode: model.identifierCode,
      loginType: model.loginType,
      region: model.region,
      regionId: model.regionId,
    );
  }

  _i7.TicketActivityContentEntity
      _map__i6$TicketActivityContentResponse_To__i7$TicketActivityContentEntity(
          _i6.TicketActivityContentResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketActivityContentResponse → TicketActivityContentEntity failed because TicketActivityContentResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketActivityContentResponse, TicketActivityContentEntity> to handle null values during mapping.');
    }
    return _i7.TicketActivityContentEntity(
      en: _map__i6$TicketActivityContentItemResponse_To__i7$TicketActivityContentItemEntity(
          model.en),
      vi: _map__i6$TicketActivityContentItemResponse_To__i7$TicketActivityContentItemEntity(
          model.vi),
    );
  }

  _i7.TicketActivityContentItemEntity
      _map__i6$TicketActivityContentItemResponse_To__i7$TicketActivityContentItemEntity(
          _i6.TicketActivityContentItemResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketActivityContentItemResponse → TicketActivityContentItemEntity failed because TicketActivityContentItemResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketActivityContentItemResponse, TicketActivityContentItemEntity> to handle null values during mapping.');
    }
    return _i7.TicketActivityContentItemEntity(
      markdownText: model.markdownText,
      text: model.text,
      highlights: model.highlights
          ?.map<_i7.TicketHighlightEntity>((value) =>
              _map__i6$TicketHighlightResponse_To__i7$TicketHighlightEntity(
                  value))
          .toList(),
    );
  }

  _i7.TicketHighlightEntity
      _map__i6$TicketHighlightResponse_To__i7$TicketHighlightEntity(
          _i6.TicketHighlightResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketHighlightResponse → TicketHighlightEntity failed because TicketHighlightResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketHighlightResponse, TicketHighlightEntity> to handle null values during mapping.');
    }
    return _i7.TicketHighlightEntity(
      offset: model.offset,
      length: model.length,
    );
  }

  _i3.RefTicketEntity _map__i2$RefTicketResponse_To__i3$RefTicketEntity(
      _i2.RefTicketResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping RefTicketResponse → RefTicketEntity failed because RefTicketResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<RefTicketResponse, RefTicketEntity> to handle null values during mapping.');
    }
    return _i3.RefTicketEntity(
      id: model.id,
      code: model.code,
      title: model.title,
      status: model.status,
      createdAt: model.createdAt,
      createBy: model.createBy,
      createdByInfo:
          _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity_Nullable(
              model.createdByInfo),
    );
  }

  _i9.TicketActivityEntity
      _map__i8$TicketActivityResponse_To__i9$TicketActivityEntity(
          _i8.TicketActivityResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketActivityResponse → TicketActivityEntity failed because TicketActivityResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketActivityResponse, TicketActivityEntity> to handle null values during mapping.');
    }
    return _i9.TicketActivityEntity(
      id: model.id,
      workspaceId: model.workspaceId,
      ticketId: model.ticketId,
      workflowId: model.workflowId,
      actorId: model.actorId,
      actorType: model.actorType,
      type: model.type,
      targetId: model.targetId,
      objectId: model.objectId,
      actor: _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity_Nullable(
          model.actor),
      createdAt: model.createdAt,
      content:
          _map__i6$TicketActivityContentResponse_To__i7$TicketActivityContentEntity_Nullable(
              model.content),
      additionalRequest:
          _map__i12$TicketAdditionalRequestResponse_To__i13$TicketAdditionalRequestEntity_Nullable(
              model.additionalRequest),
      onHoldRequest:
          _map__i32$TicketOnHoldRequestResponse_To__i31$TicketOnHoldRequestEntity_Nullable(
              model.onHoldRequest),
      reopenRequest:
          _map__i34$TicketReopenRequestResponse_To__i33$TicketReopenRequestEntity_Nullable(
              model.reopenRequest),
      reason: model.reason,
    );
  }

  _i11.TicketShiftDetailEntity
      _map__i10$TicketShiftDetailResponse_To__i11$TicketShiftDetailEntity(
          _i10.TicketShiftDetailResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketShiftDetailResponse → TicketShiftDetailEntity failed because TicketShiftDetailResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketShiftDetailResponse, TicketShiftDetailEntity> to handle null values during mapping.');
    }
    return _i11.TicketShiftDetailEntity(
      id: model.id,
      workspaceId: model.workspaceId,
      name: model.name,
      description: model.description,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i13.TicketAdditionalRequestEntity
      _map__i12$TicketAdditionalRequestResponse_To__i13$TicketAdditionalRequestEntity(
          _i12.TicketAdditionalRequestResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketAdditionalRequestResponse → TicketAdditionalRequestEntity failed because TicketAdditionalRequestResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketAdditionalRequestResponse, TicketAdditionalRequestEntity> to handle null values during mapping.');
    }
    return _i13.TicketAdditionalRequestEntity(
      workspaceId: model.workspaceId,
      ticketId: model.ticketId,
      nodeId: model.nodeId,
      id: model.id,
      contentNeedAdd: model.contentNeedAdd,
      responseToRequester: model.responseToRequester,
      targetId: model.targetId,
      requesterId: model.requesterId,
      attachedFiles: model.attachedFiles
          ?.map<_i67.GPAttachmentFileEntity>((value) =>
              _map__i69$UploadFileResponseModelV2_To__i67$GPAttachmentFileEntity(
                  value))
          .toList(),
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i15.TicketSLAEntity _map__i14$TicketSLAResponse_To__i15$TicketSLAEntity(
      _i14.TicketSLAResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLAResponse → TicketSLAEntity failed because TicketSLAResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLAResponse, TicketSLAEntity> to handle null values during mapping.');
    }
    return _i15.TicketSLAEntity(
      id: model.id,
      shiftId: model.shiftId,
      workspaceId: model.workspaceId,
      name: model.name,
      status: model.status,
      slaPriorityLevels: model.slaPriorityLevels
          ?.map<_i15.TicketSLAPriorityLevelsEntity>((value) =>
              _map__i14$TicketSLAPriorityLevelsResponse_To__i15$TicketSLAPriorityLevelsEntity(
                  value))
          .toList(),
      slaNotifications: model.slaNotifications
          ?.map<_i15.TicketSLANotificationEntity>((value) =>
              _map__i14$TicketSLANotificationResponse_To__i15$TicketSLANotificationEntity(
                  value))
          .toList(),
      isDefault: model.isDefault,
      isDeleted: model.isDeleted,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i17.TicketSLAAggEntity
      _map__i16$TicketSLAAggResponse_To__i17$TicketSLAAggEntity(
          _i16.TicketSLAAggResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLAAggResponse → TicketSLAAggEntity failed because TicketSLAAggResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLAAggResponse, TicketSLAAggEntity> to handle null values during mapping.');
    }
    return _i17.TicketSLAAggEntity(
      shiftAgg:
          _map__i16$TicketShiftAggResponse_To__i17$TicketShiftAggEntity_Nullable(
              model.shiftAgg),
      sla: _map__i14$TicketSLAResponse_To__i15$TicketSLAEntity_Nullable(
          model.sla),
    );
  }

  _i17.TicketShiftAggEntity
      _map__i16$TicketShiftAggResponse_To__i17$TicketShiftAggEntity(
          _i16.TicketShiftAggResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketShiftAggResponse → TicketShiftAggEntity failed because TicketShiftAggResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketShiftAggResponse, TicketShiftAggEntity> to handle null values during mapping.');
    }
    return _i17.TicketShiftAggEntity(
      dateOffs: model.dateOffs,
      weeklySettings: model.weeklySettings,
      shift:
          _map__i10$TicketShiftDetailResponse_To__i11$TicketShiftDetailEntity_Nullable(
              model.shift),
    );
  }

  _i15.TicketSLAPriorityLevelsEntity
      _map__i14$TicketSLAPriorityLevelsResponse_To__i15$TicketSLAPriorityLevelsEntity(
          _i14.TicketSLAPriorityLevelsResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLAPriorityLevelsResponse → TicketSLAPriorityLevelsEntity failed because TicketSLAPriorityLevelsResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLAPriorityLevelsResponse, TicketSLAPriorityLevelsEntity> to handle null values during mapping.');
    }
    return _i15.TicketSLAPriorityLevelsEntity(
      type: model.type,
      firstResponse:
          _map__i14$TicketSLAPriorityLevelFirstResponse_To__i15$TicketSLAPriorityLevelFirstEntity_Nullable(
              model.firstResponse),
      resolve:
          _map__i14$TicketSLAPriorityLevelFirstResponse_To__i15$TicketSLAPriorityLevelFirstEntity_Nullable(
              model.resolve),
    );
  }

  _i15.TicketSLAPriorityLevelFirstEntity
      _map__i14$TicketSLAPriorityLevelFirstResponse_To__i15$TicketSLAPriorityLevelFirstEntity(
          _i14.TicketSLAPriorityLevelFirstResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLAPriorityLevelFirstResponse → TicketSLAPriorityLevelFirstEntity failed because TicketSLAPriorityLevelFirstResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLAPriorityLevelFirstResponse, TicketSLAPriorityLevelFirstEntity> to handle null values during mapping.');
    }
    return _i15.TicketSLAPriorityLevelFirstEntity(
      type: model.type,
      value: model.value,
    );
  }

  _i15.TicketSLANotificationEntity
      _map__i14$TicketSLANotificationResponse_To__i15$TicketSLANotificationEntity(
          _i14.TicketSLANotificationResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLANotificationResponse → TicketSLANotificationEntity failed because TicketSLANotificationResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLANotificationResponse, TicketSLANotificationEntity> to handle null values during mapping.');
    }
    return _i15.TicketSLANotificationEntity(
      duration:
          _map__i14$TicketSLANotificationDurationResponse_To__i15$TicketSLANotificationDurationEntity(
              model.duration),
      type: model.type,
    );
  }

  _i15.TicketSLANotificationDurationEntity
      _map__i14$TicketSLANotificationDurationResponse_To__i15$TicketSLANotificationDurationEntity(
          _i14.TicketSLANotificationDurationResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLANotificationDurationResponse → TicketSLANotificationDurationEntity failed because TicketSLANotificationDurationResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLANotificationDurationResponse, TicketSLANotificationDurationEntity> to handle null values during mapping.');
    }
    return _i15.TicketSLANotificationDurationEntity(
      type: model.type,
      value: model.value,
    );
  }

  _i19.TickeUserRoleEntity
      _map__i18$TickeUserRoleResponse_To__i19$TickeUserRoleEntity(
          _i18.TickeUserRoleResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TickeUserRoleResponse → TickeUserRoleEntity failed because TickeUserRoleResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TickeUserRoleResponse, TickeUserRoleEntity> to handle null values during mapping.');
    }
    return _i19.TickeUserRoleEntity(
      userWhoCanCancelTicket: model.userWhoCanCancelTicket,
      userWhoCanDeleteTicket: model.userWhoCanDeleteTicket,
      userWhoCanAcceptDeclineOnHoldRequest:
          model.userWhoCanAcceptDeclineOnHoldRequest,
      requester: model.requester,
      sameDepartmentWithRequester: model.sameDepartmentWithRequester,
      userWhoIsAdminWorkflow: model.userWhoIsAdminWorkflow,
      userRoleInNode: model.userRoleInNode,
    );
  }

  _i17.TicketFlowChartEntity
      _map__i16$TicketFlowChartResponse_To__i17$TicketFlowChartEntity(
          _i16.TicketFlowChartResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketFlowChartResponse → TicketFlowChartEntity failed because TicketFlowChartResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketFlowChartResponse, TicketFlowChartEntity> to handle null values during mapping.');
    }
    return _i17.TicketFlowChartEntity(
      workspaceId: model.workspaceId,
      ticketStatus: model.ticketStatus,
      nodes: model.nodes
          .map<_i17.TicketFlowChartNodeEntity>((value) =>
              _map__i16$TicketFlowchartNodeResponse_To__i17$TicketFlowChartNodeEntity(
                  value))
          .toList(),
      edges: model.edges
          .map<_i17.TicketFlowchartEdgeEntity>((value) =>
              _map__i16$TicketFlowchartEdgeResponse_To__i17$TicketFlowchartEdgeEntity(
                  value))
          .toList(),
      ticketId: model.ticketId,
      ticketStartAt: model.ticketStartAt,
      ticketActualProcessingDurationSec:
          model.ticketActualProcessingDurationSec,
      ticketActualFirstResponseAfterSec:
          model.ticketActualFirstResponseAfterSec,
      ticketAbsoluteSecondsUntilDeadline:
          model.ticketAbsoluteSecondsUntilDeadline,
      ticketAbsoluteSecondsUntilResponseDeadline:
          model.ticketAbsoluteSecondsUntilResponseDeadline,
      ticketDeadlineTs: model.ticketDeadlineTs,
      ticketResponseDeadlineTs: model.ticketResponseDeadlineTs,
      ticketFirstResponseAt: model.ticketFirstResponseAt,
      slaAgg:
          _map__i16$TicketSLAAggResponse_To__i17$TicketSLAAggEntity_Nullable(
              model.slaAgg),
    );
  }

  _i17.TicketFlowChartNodeEntity
      _map__i16$TicketFlowchartNodeResponse_To__i17$TicketFlowChartNodeEntity(
          _i16.TicketFlowchartNodeResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketFlowchartNodeResponse → TicketFlowChartNodeEntity failed because TicketFlowchartNodeResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketFlowchartNodeResponse, TicketFlowChartNodeEntity> to handle null values during mapping.');
    }
    return _i17.TicketFlowChartNodeEntity(
      workspaceId: model.workspaceId,
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      id: model.id,
      ticketId: model.ticketId,
      nodeId: model.nodeId,
      assigneeId: model.assigneeId,
      type: model.type,
      updatedBy: model.updatedBy,
      actualProcessingDurationSec: model.actualProcessingDurationSec,
      actualFirstResponseAfterSec: model.actualFirstResponseAfterSec,
      absoluteSecondsUntilDeadline: model.absoluteSecondsUntilDeadline,
      absoluteSecondsUntilResponseDeadline:
          model.absoluteSecondsUntilResponseDeadline,
      responseDeadlineTs: model.responseDeadlineTs,
      startedAt: model.startedAt,
      processedAt: model.processedAt,
      responseAt: model.responseAt,
      additionalRequestAt: model.additionalRequestAt,
      onHoldRequestAt: model.onHoldRequestAt,
      additionalRequestIn: model.additionalRequestIn,
      deadlineTs: model.deadlineTs,
      dateOffMap: model.dateOffMap,
      sla: _map__i14$TicketSLAResponse_To__i15$TicketSLAEntity_Nullable(
          model.sla),
      admins: model.admins
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      supporters: model.supporters
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      defaultFollowers: model.defaultFollowers
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      assignees: model.assignees
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      assignee:
          _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity_Nullable(
              model.assignee),
      option:
          _map__i20$TicketNodeOptionResponse_To__i21$TicketNodeOptionEntity_Nullable(
              model.option),
      noFirstResponseYet: model.noFirstResponseYet,
    );
  }

  _i21.TicketNodeOptionEntity
      _map__i20$TicketNodeOptionResponse_To__i21$TicketNodeOptionEntity(
          _i20.TicketNodeOptionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketNodeOptionResponse → TicketNodeOptionEntity failed because TicketNodeOptionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketNodeOptionResponse, TicketNodeOptionEntity> to handle null values during mapping.');
    }
    return _i21.TicketNodeOptionEntity(
      name: model.name,
      assigneeType: model.assigneeType,
      workflowFormFieldPermissions: model.workflowFormFieldPermissions
          ?.map<_i21.WorkflowFormFieldPermissionEntity>((value) =>
              _map__i20$WorkflowFormFieldPermissionsResponse_To__i21$WorkflowFormFieldPermissionEntity(
                  value))
          .toList(),
    );
  }

  _i17.TicketFlowchartEdgeEntity
      _map__i16$TicketFlowchartEdgeResponse_To__i17$TicketFlowchartEdgeEntity(
          _i16.TicketFlowchartEdgeResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketFlowchartEdgeResponse → TicketFlowchartEdgeEntity failed because TicketFlowchartEdgeResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketFlowchartEdgeResponse, TicketFlowchartEdgeEntity> to handle null values during mapping.');
    }
    return _i17.TicketFlowchartEdgeEntity(
      workspaceId: model.workspaceId,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      ticketId: model.ticketId,
      currentNodeId: model.currentNodeId,
      nextNodeId: model.nextNodeId,
    );
  }

  _i21.TicketNodeEntity _map__i20$TicketNodeResponse_To__i21$TicketNodeEntity(
      _i20.TicketNodeResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketNodeResponse → TicketNodeEntity failed because TicketNodeResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketNodeResponse, TicketNodeEntity> to handle null values during mapping.');
    }
    return _i21.TicketNodeEntity(
      id: model.id,
      workspaceId: model.workspaceId,
      ticketId: model.ticketId,
      nodeId: model.nodeId,
      assigneeId: model.assigneeId,
      status: model.status,
      type: model.type,
      updatedBy: model.updatedBy,
      actualProcessingDurationSec: model.actualProcessingDurationSec,
      absoluteSecondsUntilDeadline: model.absoluteSecondsUntilDeadline,
      actualFirstResponseAfterSec: model.actualFirstResponseAfterSec,
      absoluteSecondsUntilResponseDeadline:
          model.absoluteSecondsUntilResponseDeadline,
      responseDeadlineTs: model.responseDeadlineTs,
      startedAt: model.startedAt,
      processedAt: model.processedAt,
      responseAt: model.responseAt,
      additionalRequestAt: model.additionalRequestAt,
      onHoldRequestAt: model.onHoldRequestAt,
      additionalRequestIn: model.additionalRequestIn,
      onHoldRequestIn: model.onHoldRequestIn,
      deadlineTS: model.deadlineTS,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      dateOffMap: model.dateOffMap,
      ticketTags: model.ticketTags
          ?.map<_i21.TicketNodeTagEntity>((value) =>
              _map__i20$TicketNodeTagResponse_To__i21$TicketNodeTagEntity(
                  value))
          .toList(),
      admins: model.admins
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      supporters: model.supporters
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      defaultFollowers: model.defaultFollowers
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      assignees: model.assignees
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      assignee:
          _map__i51$Assignee_To__i53$AssigneeEntity_Nullable(model.assignee),
      sla: model.sla,
      assigneeGroup: model.assigneeGroup
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      tasks: model.tasks,
      option: _map__i20$TicketNodeOptionResponse_To__i21$TicketNodeOptionEntity(
          model.option),
      noFirstResponseYet: model.noFirstResponseYet,
    );
  }

  _i23.TicketAssigneeEntity
      _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
          _i22.TicketAssigneeResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketAssigneeResponse → TicketAssigneeEntity failed because TicketAssigneeResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketAssigneeResponse, TicketAssigneeEntity> to handle null values during mapping.');
    }
    return _i23.TicketAssigneeEntity(
      id: model.id,
      name: model.name,
      workspaceId: model.workspaceId,
      info: _map__i51$Assignee_To__i53$AssigneeEntity_Nullable(model.info),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      type: model.type,
    );
  }

  _i25.WorkflowAdvanceSettingAssigneeEntity
      _map__i24$WorkflowAdvanceSettingAssigneeResponse_To__i25$WorkflowAdvanceSettingAssigneeEntity(
          _i24.WorkflowAdvanceSettingAssigneeResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingAssigneeResponse → WorkflowAdvanceSettingAssigneeEntity failed because WorkflowAdvanceSettingAssigneeResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingAssigneeResponse, WorkflowAdvanceSettingAssigneeEntity> to handle null values during mapping.');
    }
    return _i25.WorkflowAdvanceSettingAssigneeEntity(
      id: model.id,
      workspaceId: model.workspaceId,
      members: model.members
          ?.map<_i23.TicketAssigneeEntity>((value) =>
              _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity(
                  value))
          .toList(),
      name: model.name,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      type: model.type,
    );
  }

  _i27.TicketCommentEntity
      _map__i26$TicketCommentResponse_To__i27$TicketCommentEntity(
          _i26.TicketCommentResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketCommentResponse → TicketCommentEntity failed because TicketCommentResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketCommentResponse, TicketCommentEntity> to handle null values during mapping.');
    }
    return _i27.TicketCommentEntity(
      parentId: model.parentId,
      commentAs: _map__i26$CommentAsResponse_To__i28$CommentAs_Nullable(
          model.commentAs),
      text: model.text,
      mentions: model.mentions
          ?.map<_i28.Mentions>(
              (value) => _map__i26$MentionResponse_To__i28$Mentions(value))
          .toList(),
      medias: model.medias
          ?.map<_i28.Medias>(
              (value) => _map__i26$MediasResponse_To__i28$Medias(value))
          .toList(),
      type: model.type,
      commentType: model.commentType,
      status: model.status,
      dataSource: model.dataSource,
      id: model.id,
      targetId: model.targetId,
      targetType: model.targetType,
      replyCount: model.replyCount,
      comments: _i71.TicketEntityMapper.mapTicketCommentCommentReplies(model),
      user: model.user,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i28.CommentAs _map__i26$CommentAsResponse_To__i28$CommentAs(
      _i26.CommentAsResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping CommentAsResponse → CommentAs failed because CommentAsResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<CommentAsResponse, CommentAs> to handle null values during mapping.');
    }
    return _i28.CommentAs(
      authorType: model.authorType,
      authorId: model.authorId,
    );
  }

  _i28.Mentions _map__i26$MentionResponse_To__i28$Mentions(
      _i26.MentionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping MentionResponse → Mentions failed because MentionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<MentionResponse, Mentions> to handle null values during mapping.');
    }
    return _i28.Mentions(
      offset: model.offset,
      length: model.length,
      mentionId: model.mentionId,
    );
  }

  _i28.Medias _map__i26$MediasResponse_To__i28$Medias(
      _i26.MediasResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping MediasResponse → Medias failed because MediasResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<MediasResponse, Medias> to handle null values during mapping.');
    }
    return _i28.Medias(
      id: model.id,
      type: model.type,
      width: model.width ?? 0,
      height: model.height ?? 0,
      src: model.src,
      size: model.size,
      name: model.name,
    )
      ..fileType = model.fileType
      ..thumbPattern = model.thumbPattern;
  }

  _i29.TicketPostCommentsRequestParams
      _map__i27$TicketCommentEntity_To__i29$TicketPostCommentsRequestParams(
          _i27.TicketCommentEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketCommentEntity → TicketPostCommentsRequestParams failed because TicketCommentEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketCommentEntity, TicketPostCommentsRequestParams> to handle null values during mapping.');
    }
    return _i29.TicketPostCommentsRequestParams(
      commentAs: model.commentAs,
      dataSource: model.dataSource,
      medias: model.medias,
      text: model.text,
      mentions: model.mentions,
      parentId: model.parentId,
      ticketNodeId: model.ticketNodeId,
      isPrivateNote: model.isPrivateNote,
      targetType: model.targetType,
      targetId: model.targetId,
    );
  }

  _i30.TicketEditCommentsRequestParams
      _map__i28$Comment_To__i30$TicketEditCommentsRequestParams(
          _i28.Comment? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Comment → TicketEditCommentsRequestParams failed because Comment was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Comment, TicketEditCommentsRequestParams> to handle null values during mapping.');
    }
    return _i30.TicketEditCommentsRequestParams(
      medias: model.medias,
      text: model.text,
      mentions: model.mentions,
    );
  }

  _i27.TicketCommentEntity _map__i28$Comment_To__i27$TicketCommentEntity(
      _i28.Comment? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Comment → TicketCommentEntity failed because Comment was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Comment, TicketCommentEntity> to handle null values during mapping.');
    }
    return _i27.TicketCommentEntity(
      parentId: model.parentId,
      commentAs: model.commentAs,
      text: model.text,
      mentions: model.mentions,
      medias: model.medias,
      type: model.type,
      commentType: model.commentType,
      status: model.status,
      dataSource: model.dataSource,
      id: model.id,
      targetId: model.targetId,
      targetType: model.targetType,
      replyCount: model.replyCount,
      comments: model.comments,
      user: model.user,
      page: model.page,
      group: model.group,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      replyHighLightComment: model.replyHighLightComment,
    )
      ..canReply = model.canReply
      ..commentMediaLocals = model.commentMediaLocals
      ..rxUseHighlightBg = model.rxUseHighlightBg;
  }

  _i21.TicketNodeTagEntity
      _map__i20$TicketNodeTagResponse_To__i21$TicketNodeTagEntity(
          _i20.TicketNodeTagResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketNodeTagResponse → TicketNodeTagEntity failed because TicketNodeTagResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketNodeTagResponse, TicketNodeTagEntity> to handle null values during mapping.');
    }
    return _i21.TicketNodeTagEntity(
      tagId: model.tagId,
      ticketId: model.ticketId,
      nodeId: model.nodeId,
      tag: _map__i43$WorkflowTagResponse_To__i44$WorkflowTagEntity(model.tag),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i2.TicketListResponse _map__i3$TicketEntity_To__i2$TicketListResponse(
      _i3.TicketEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketEntity → TicketListResponse failed because TicketEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketEntity, TicketListResponse> to handle null values during mapping.');
    }
    return _i2.TicketListResponse(
      id: model.id,
      code: model.code,
      title: model.title,
      assigneeId: model.assigneeId,
      currentNodeId: model.currentNodeId,
      priority: model.priority,
      status: model.status,
      nodeStatus: model.nodeStatus,
      workspaceId: model.workspaceId,
      workflowId: model.workflowId,
      workflow: _map__i36$WorkFlowEntity_To__i35$WorkFlowResponse_Nullable(
          model.workflow),
      currentNodeName: model.currentNodeName,
      reopened: model.reopened,
      ratingPoint: model.ratingPoint,
      isPrivate: model.isPrivate,
      closedAt: model.closedAt,
      nodeDeadlineAt: model.nodeDeadlineAt,
      latestRecentActivityAt: model.latestRecentActivityAt,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      deletedAt: model.deletedAt,
      deadlineFirstResponseAt: model.deadlineFirstResponseAt,
      firstResponseAt: model.firstResponseAt,
      deadlineProcessAt: model.deadlineProcessAt,
      processAt: model.processAt,
      absoluteSecondsUntilResponseDeadline:
          model.absoluteSecondsUntilResponseDeadline,
      absoluteSecondsUntilProcessDeadline:
          model.absoluteSecondsUntilProcessDeadline,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      creator:
          _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse_Nullable(
              model.creator),
      assignee:
          _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse_Nullable(
              model.assignee),
      sla: _map__i15$TicketSLAEntity_To__i14$TicketSLAResponse_Nullable(
          model.sla),
      recentActivity:
          _map__i9$TicketActivityEntity_To__i8$TicketActivityResponse_Nullable(
              model.recentActivity),
      userRole:
          _map__i19$TickeUserRoleEntity_To__i18$TickeUserRoleResponse_Nullable(
              model.userRole),
      fieldValues: model.fieldValues
          ?.map<_i37.WorkFlowFieldValuesResponse>((value) =>
              _map__i38$WorkFlowFieldValuesEntity_To__i37$WorkFlowFieldValuesResponse(
                  value))
          .toList(),
      refTickets: model.refTickets
          ?.map<_i2.RefTicketResponse>((value) =>
              _map__i3$RefTicketEntity_To__i2$RefTicketResponse(value))
          .toList(),
      refTicketIds: model.refTicketIds,
      adminsWorkflowGroup: model.adminsWorkflowGroup
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      review: model.review,
      adminsWorkflow: model.adminsWorkflow
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
    );
  }

  _i4.TicketCreatorResponse
      _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse(
          _i5.TicketCreatorEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketCreatorEntity → TicketCreatorResponse failed because TicketCreatorEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketCreatorEntity, TicketCreatorResponse> to handle null values during mapping.');
    }
    return _i4.TicketCreatorResponse(
      id: model.id,
      displayName: model.displayName,
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
      email: model.email,
      phoneNumber: model.phoneNumber,
      title: model.title,
      department: model.department,
      companyName: model.companyName,
      emloyeeCode: model.emloyeeCode,
      identifierCode: model.identifierCode,
      loginType: model.loginType,
      region: model.region,
      regionId: model.regionId,
    );
  }

  _i6.TicketActivityContentResponse
      _map__i7$TicketActivityContentEntity_To__i6$TicketActivityContentResponse(
          _i7.TicketActivityContentEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketActivityContentEntity → TicketActivityContentResponse failed because TicketActivityContentEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketActivityContentEntity, TicketActivityContentResponse> to handle null values during mapping.');
    }
    return _i6.TicketActivityContentResponse(
      en: _map__i7$TicketActivityContentItemEntity_To__i6$TicketActivityContentItemResponse_Nullable(
          model.en),
      vi: _map__i7$TicketActivityContentItemEntity_To__i6$TicketActivityContentItemResponse_Nullable(
          model.vi),
    );
  }

  _i6.TicketActivityContentItemResponse
      _map__i7$TicketActivityContentItemEntity_To__i6$TicketActivityContentItemResponse(
          _i7.TicketActivityContentItemEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketActivityContentItemEntity → TicketActivityContentItemResponse failed because TicketActivityContentItemEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketActivityContentItemEntity, TicketActivityContentItemResponse> to handle null values during mapping.');
    }
    return _i6.TicketActivityContentItemResponse(
      markdownText: model.markdownText,
      text: model.text,
      highlights: model.highlights
          ?.map<_i6.TicketHighlightResponse>((value) =>
              _map__i7$TicketHighlightEntity_To__i6$TicketHighlightResponse(
                  value))
          .toList(),
    );
  }

  _i6.TicketHighlightResponse
      _map__i7$TicketHighlightEntity_To__i6$TicketHighlightResponse(
          _i7.TicketHighlightEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketHighlightEntity → TicketHighlightResponse failed because TicketHighlightEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketHighlightEntity, TicketHighlightResponse> to handle null values during mapping.');
    }
    return _i6.TicketHighlightResponse(
      offset: model.offset,
      length: model.length,
    );
  }

  _i2.RefTicketResponse _map__i3$RefTicketEntity_To__i2$RefTicketResponse(
      _i3.RefTicketEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping RefTicketEntity → RefTicketResponse failed because RefTicketEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<RefTicketEntity, RefTicketResponse> to handle null values during mapping.');
    }
    return _i2.RefTicketResponse(
      id: model.id,
      code: model.code,
      title: model.title,
      status: model.status,
      createdAt: model.createdAt,
      createBy: model.createBy,
      createdByInfo:
          _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse_Nullable(
              model.createdByInfo),
    );
  }

  _i8.TicketActivityResponse
      _map__i9$TicketActivityEntity_To__i8$TicketActivityResponse(
          _i9.TicketActivityEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketActivityEntity → TicketActivityResponse failed because TicketActivityEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketActivityEntity, TicketActivityResponse> to handle null values during mapping.');
    }
    return _i8.TicketActivityResponse(
      workspaceId: model.workspaceId,
      id: model.id,
      ticketId: model.ticketId,
      workflowId: model.workflowId,
      actorId: model.actorId,
      actorType: model.actorType,
      type: model.type,
      targetId: model.targetId,
      objectId: model.objectId,
      actor: _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse_Nullable(
          model.actor),
      createdAt: model.createdAt,
      content:
          _map__i7$TicketActivityContentEntity_To__i6$TicketActivityContentResponse_Nullable(
              model.content),
      additionalRequest:
          _map__i13$TicketAdditionalRequestEntity_To__i12$TicketAdditionalRequestResponse_Nullable(
              model.additionalRequest),
      onHoldRequest:
          _map__i31$TicketOnHoldRequestEntity_To__i32$TicketOnHoldRequestResponse_Nullable(
              model.onHoldRequest),
      reopenRequest:
          _map__i33$TicketReopenRequestEntity_To__i34$TicketReopenRequestResponse_Nullable(
              model.reopenRequest),
      reason: model.reason,
    );
  }

  _i10.TicketShiftDetailResponse
      _map__i11$TicketShiftDetailEntity_To__i10$TicketShiftDetailResponse(
          _i11.TicketShiftDetailEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketShiftDetailEntity → TicketShiftDetailResponse failed because TicketShiftDetailEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketShiftDetailEntity, TicketShiftDetailResponse> to handle null values during mapping.');
    }
    return _i10.TicketShiftDetailResponse(
      id: model.id,
      workspaceId: model.workspaceId,
      name: model.name,
      description: model.description,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i12.TicketAdditionalRequestResponse
      _map__i13$TicketAdditionalRequestEntity_To__i12$TicketAdditionalRequestResponse(
          _i13.TicketAdditionalRequestEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketAdditionalRequestEntity → TicketAdditionalRequestResponse failed because TicketAdditionalRequestEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketAdditionalRequestEntity, TicketAdditionalRequestResponse> to handle null values during mapping.');
    }
    return _i12.TicketAdditionalRequestResponse(
      workspaceId: model.workspaceId,
      ticketId: model.ticketId,
      nodeId: model.nodeId,
      id: model.id,
      contentNeedAdd: model.contentNeedAdd,
      responseToRequester: model.responseToRequester,
      status: model.status,
      requesterId: model.requesterId,
      targetId: model.targetId,
      attachedFiles: model.attachedFiles
          ?.map<_i69.UploadFileResponseModelV2>((value) =>
              _map__i67$GPAttachmentFileEntity_To__i69$UploadFileResponseModelV2(
                  value))
          .toList(),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i32.TicketOnHoldRequestResponse
      _map__i31$TicketOnHoldRequestEntity_To__i32$TicketOnHoldRequestResponse(
          _i31.TicketOnHoldRequestEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketOnHoldRequestEntity → TicketOnHoldRequestResponse failed because TicketOnHoldRequestEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketOnHoldRequestEntity, TicketOnHoldRequestResponse> to handle null values during mapping.');
    }
    return _i32.TicketOnHoldRequestResponse(
      id: model.id,
      nodeId: model.nodeId,
      ticketId: model.ticketId,
      workspaceId: model.workspaceId,
      requesterId: model.requesterId,
      reason: model.reason,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i34.TicketReopenRequestResponse
      _map__i33$TicketReopenRequestEntity_To__i34$TicketReopenRequestResponse(
          _i33.TicketReopenRequestEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketReopenRequestEntity → TicketReopenRequestResponse failed because TicketReopenRequestEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketReopenRequestEntity, TicketReopenRequestResponse> to handle null values during mapping.');
    }
    return _i34.TicketReopenRequestResponse(reason: model.reason);
  }

  _i16.TicketSLAAggResponse
      _map__i17$TicketSLAAggEntity_To__i16$TicketSLAAggResponse(
          _i17.TicketSLAAggEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLAAggEntity → TicketSLAAggResponse failed because TicketSLAAggEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLAAggEntity, TicketSLAAggResponse> to handle null values during mapping.');
    }
    return _i16.TicketSLAAggResponse(
      shiftAgg:
          _map__i17$TicketShiftAggEntity_To__i16$TicketShiftAggResponse_Nullable(
              model.shiftAgg),
      sla: _map__i15$TicketSLAEntity_To__i14$TicketSLAResponse_Nullable(
          model.sla),
    );
  }

  _i16.TicketShiftAggResponse
      _map__i17$TicketShiftAggEntity_To__i16$TicketShiftAggResponse(
          _i17.TicketShiftAggEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketShiftAggEntity → TicketShiftAggResponse failed because TicketShiftAggEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketShiftAggEntity, TicketShiftAggResponse> to handle null values during mapping.');
    }
    return _i16.TicketShiftAggResponse(
      dateOffs: model.dateOffs,
      weeklySettings: model.weeklySettings,
      shift:
          _map__i11$TicketShiftDetailEntity_To__i10$TicketShiftDetailResponse_Nullable(
              model.shift),
    );
  }

  _i14.TicketSLAResponse _map__i15$TicketSLAEntity_To__i14$TicketSLAResponse(
      _i15.TicketSLAEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLAEntity → TicketSLAResponse failed because TicketSLAEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLAEntity, TicketSLAResponse> to handle null values during mapping.');
    }
    return _i14.TicketSLAResponse(
      id: model.id,
      shiftId: model.shiftId,
      workspaceId: model.workspaceId,
      name: model.name,
      status: model.status,
      slaPriorityLevels: model.slaPriorityLevels
          ?.map<_i14.TicketSLAPriorityLevelsResponse>((value) =>
              _map__i15$TicketSLAPriorityLevelsEntity_To__i14$TicketSLAPriorityLevelsResponse(
                  value))
          .toList(),
      slaNotifications: model.slaNotifications
          ?.map<_i14.TicketSLANotificationResponse>((value) =>
              _map__i15$TicketSLANotificationEntity_To__i14$TicketSLANotificationResponse(
                  value))
          .toList(),
      isDefault: model.isDefault,
      isDeleted: model.isDeleted,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i14.TicketSLAPriorityLevelsResponse
      _map__i15$TicketSLAPriorityLevelsEntity_To__i14$TicketSLAPriorityLevelsResponse(
          _i15.TicketSLAPriorityLevelsEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLAPriorityLevelsEntity → TicketSLAPriorityLevelsResponse failed because TicketSLAPriorityLevelsEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLAPriorityLevelsEntity, TicketSLAPriorityLevelsResponse> to handle null values during mapping.');
    }
    return _i14.TicketSLAPriorityLevelsResponse(
      type: model.type,
      firstResponse:
          _map__i15$TicketSLAPriorityLevelFirstEntity_To__i14$TicketSLAPriorityLevelFirstResponse_Nullable(
              model.firstResponse),
      resolve:
          _map__i15$TicketSLAPriorityLevelFirstEntity_To__i14$TicketSLAPriorityLevelFirstResponse_Nullable(
              model.resolve),
    );
  }

  _i14.TicketSLAPriorityLevelFirstResponse
      _map__i15$TicketSLAPriorityLevelFirstEntity_To__i14$TicketSLAPriorityLevelFirstResponse(
          _i15.TicketSLAPriorityLevelFirstEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLAPriorityLevelFirstEntity → TicketSLAPriorityLevelFirstResponse failed because TicketSLAPriorityLevelFirstEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLAPriorityLevelFirstEntity, TicketSLAPriorityLevelFirstResponse> to handle null values during mapping.');
    }
    return _i14.TicketSLAPriorityLevelFirstResponse(
      type: model.type,
      value: model.value,
    );
  }

  _i14.TicketSLANotificationResponse
      _map__i15$TicketSLANotificationEntity_To__i14$TicketSLANotificationResponse(
          _i15.TicketSLANotificationEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLANotificationEntity → TicketSLANotificationResponse failed because TicketSLANotificationEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLANotificationEntity, TicketSLANotificationResponse> to handle null values during mapping.');
    }
    return _i14.TicketSLANotificationResponse(
      duration:
          _map__i15$TicketSLANotificationDurationEntity_To__i14$TicketSLANotificationDurationResponse(
              model.duration),
      type: model.type,
    );
  }

  _i14.TicketSLANotificationDurationResponse
      _map__i15$TicketSLANotificationDurationEntity_To__i14$TicketSLANotificationDurationResponse(
          _i15.TicketSLANotificationDurationEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSLANotificationDurationEntity → TicketSLANotificationDurationResponse failed because TicketSLANotificationDurationEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSLANotificationDurationEntity, TicketSLANotificationDurationResponse> to handle null values during mapping.');
    }
    return _i14.TicketSLANotificationDurationResponse(
      type: model.type,
      value: model.value,
    );
  }

  _i18.TickeUserRoleResponse
      _map__i19$TickeUserRoleEntity_To__i18$TickeUserRoleResponse(
          _i19.TickeUserRoleEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TickeUserRoleEntity → TickeUserRoleResponse failed because TickeUserRoleEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TickeUserRoleEntity, TickeUserRoleResponse> to handle null values during mapping.');
    }
    return _i18.TickeUserRoleResponse(
      userWhoCanCancelTicket: model.userWhoCanCancelTicket,
      userWhoCanDeleteTicket: model.userWhoCanDeleteTicket,
      userWhoCanAcceptDeclineOnHoldRequest:
          model.userWhoCanAcceptDeclineOnHoldRequest,
      requester: model.requester,
      sameDepartmentWithRequester: model.sameDepartmentWithRequester,
      userWhoIsAdminWorkflow: model.userWhoIsAdminWorkflow,
      userRoleInNode: model.userRoleInNode,
    );
  }

  _i16.TicketFlowChartResponse
      _map__i17$TicketFlowChartEntity_To__i16$TicketFlowChartResponse(
          _i17.TicketFlowChartEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketFlowChartEntity → TicketFlowChartResponse failed because TicketFlowChartEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketFlowChartEntity, TicketFlowChartResponse> to handle null values during mapping.');
    }
    return _i16.TicketFlowChartResponse(
      workspaceId: model.workspaceId,
      ticketStatus: model.ticketStatus,
      nodes: model.nodes
          .map<_i16.TicketFlowchartNodeResponse>((value) =>
              _map__i17$TicketFlowChartNodeEntity_To__i16$TicketFlowchartNodeResponse(
                  value))
          .toList(),
      edges: model.edges
          .map<_i16.TicketFlowchartEdgeResponse>((value) =>
              _map__i17$TicketFlowchartEdgeEntity_To__i16$TicketFlowchartEdgeResponse(
                  value))
          .toList(),
      ticketId: model.ticketId,
      ticketStartAt: model.ticketStartAt,
      ticketActualProcessingDurationSec:
          model.ticketActualProcessingDurationSec,
      ticketActualFirstResponseAfterSec:
          model.ticketActualFirstResponseAfterSec,
      ticketAbsoluteSecondsUntilDeadline:
          model.ticketAbsoluteSecondsUntilDeadline,
      ticketAbsoluteSecondsUntilResponseDeadline:
          model.ticketAbsoluteSecondsUntilResponseDeadline,
      ticketDeadlineTs: model.ticketDeadlineTs,
      ticketResponseDeadlineTs: model.ticketResponseDeadlineTs,
      ticketFirstResponseAt: model.ticketFirstResponseAt,
      slaAgg:
          _map__i17$TicketSLAAggEntity_To__i16$TicketSLAAggResponse_Nullable(
              model.slaAgg),
    );
  }

  _i16.TicketFlowchartNodeResponse
      _map__i17$TicketFlowChartNodeEntity_To__i16$TicketFlowchartNodeResponse(
          _i17.TicketFlowChartNodeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketFlowChartNodeEntity → TicketFlowchartNodeResponse failed because TicketFlowChartNodeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketFlowChartNodeEntity, TicketFlowchartNodeResponse> to handle null values during mapping.');
    }
    return _i16.TicketFlowchartNodeResponse(
      workspaceId: model.workspaceId,
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      id: model.id,
      ticketId: model.ticketId,
      nodeId: model.nodeId,
      assigneeId: model.assigneeId,
      type: model.type,
      updatedBy: model.updatedBy,
      actualProcessingDurationSec: model.actualProcessingDurationSec,
      actualFirstResponseAfterSec: model.actualFirstResponseAfterSec,
      absoluteSecondsUntilDeadline: model.absoluteSecondsUntilDeadline,
      absoluteSecondsUntilResponseDeadline:
          model.absoluteSecondsUntilResponseDeadline,
      responseDeadlineTs: model.responseDeadlineTs,
      startedAt: model.startedAt,
      processedAt: model.processedAt,
      responseAt: model.responseAt,
      additionalRequestAt: model.additionalRequestAt,
      onHoldRequestAt: model.onHoldRequestAt,
      additionalRequestIn: model.additionalRequestIn,
      deadlineTs: model.deadlineTs,
      dateOffMap: model.dateOffMap,
      sla: _map__i15$TicketSLAEntity_To__i14$TicketSLAResponse_Nullable(
          model.sla),
      admins: model.admins
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      supporters: model.supporters
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      defaultFollowers: model.defaultFollowers
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      assignees: model.assignees
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      assignee:
          _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse_Nullable(
              model.assignee),
      option:
          _map__i21$TicketNodeOptionEntity_To__i20$TicketNodeOptionResponse_Nullable(
              model.option),
      noFirstResponseYet: model.noFirstResponseYet,
    );
  }

  _i20.TicketNodeOptionResponse
      _map__i21$TicketNodeOptionEntity_To__i20$TicketNodeOptionResponse(
          _i21.TicketNodeOptionEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketNodeOptionEntity → TicketNodeOptionResponse failed because TicketNodeOptionEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketNodeOptionEntity, TicketNodeOptionResponse> to handle null values during mapping.');
    }
    return _i20.TicketNodeOptionResponse(
      name: model.name,
      assigneeType: model.assigneeType,
      workflowFormFieldPermissions: model.workflowFormFieldPermissions
          ?.map<_i20.WorkflowFormFieldPermissionsResponse>((value) =>
              _map__i21$WorkflowFormFieldPermissionEntity_To__i20$WorkflowFormFieldPermissionsResponse(
                  value))
          .toList(),
    );
  }

  _i16.TicketFlowchartEdgeResponse
      _map__i17$TicketFlowchartEdgeEntity_To__i16$TicketFlowchartEdgeResponse(
          _i17.TicketFlowchartEdgeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketFlowchartEdgeEntity → TicketFlowchartEdgeResponse failed because TicketFlowchartEdgeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketFlowchartEdgeEntity, TicketFlowchartEdgeResponse> to handle null values during mapping.');
    }
    return _i16.TicketFlowchartEdgeResponse(
      workspaceId: model.workspaceId,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      ticketId: model.ticketId,
      currentNodeId: model.currentNodeId,
      nextNodeId: model.nextNodeId,
    );
  }

  _i20.TicketNodeResponse _map__i21$TicketNodeEntity_To__i20$TicketNodeResponse(
      _i21.TicketNodeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketNodeEntity → TicketNodeResponse failed because TicketNodeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketNodeEntity, TicketNodeResponse> to handle null values during mapping.');
    }
    return _i20.TicketNodeResponse(
      id: model.id,
      workspaceId: model.workspaceId,
      ticketId: model.ticketId,
      nodeId: model.nodeId,
      assigneeId: model.assigneeId,
      status: model.status,
      type: model.type,
      updatedBy: model.updatedBy,
      actualProcessingDurationSec: model.actualProcessingDurationSec,
      absoluteSecondsUntilDeadline: model.absoluteSecondsUntilDeadline,
      actualFirstResponseAfterSec: model.actualFirstResponseAfterSec,
      absoluteSecondsUntilResponseDeadline:
          model.absoluteSecondsUntilResponseDeadline,
      responseDeadlineTs: model.responseDeadlineTs,
      startedAt: model.startedAt,
      processedAt: model.processedAt,
      responseAt: model.responseAt,
      additionalRequestAt: model.additionalRequestAt,
      onHoldRequestAt: model.onHoldRequestAt,
      additionalRequestIn: model.additionalRequestIn,
      onHoldRequestIn: model.onHoldRequestIn,
      deadlineTS: model.deadlineTS,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      dateOffMap: model.dateOffMap,
      ticketTags: model.ticketTags
          ?.map<_i20.TicketNodeTagResponse>((value) =>
              _map__i21$TicketNodeTagEntity_To__i20$TicketNodeTagResponse(
                  value))
          .toList(),
      admins: model.admins
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      supporters: model.supporters
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      defaultFollowers: model.defaultFollowers
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      assignees: model.assignees
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      assignee:
          _map__i53$AssigneeEntity_To__i51$Assignee_Nullable(model.assignee),
      sla: model.sla,
      assigneeGroup: model.assigneeGroup
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      tasks: model.tasks,
      option: _map__i21$TicketNodeOptionEntity_To__i20$TicketNodeOptionResponse(
          model.option),
      noFirstResponseYet: model.noFirstResponseYet,
    );
  }

  _i22.TicketAssigneeResponse
      _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
          _i23.TicketAssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketAssigneeEntity → TicketAssigneeResponse failed because TicketAssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketAssigneeEntity, TicketAssigneeResponse> to handle null values during mapping.');
    }
    return _i22.TicketAssigneeResponse(
      id: model.id,
      name: model.name,
      workspaceId: model.workspaceId,
      info: _map__i53$AssigneeEntity_To__i51$Assignee_Nullable(model.info),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      type: model.type,
    );
  }

  _i24.WorkflowAdvanceSettingAssigneeResponse
      _map__i25$WorkflowAdvanceSettingAssigneeEntity_To__i24$WorkflowAdvanceSettingAssigneeResponse(
          _i25.WorkflowAdvanceSettingAssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingAssigneeEntity → WorkflowAdvanceSettingAssigneeResponse failed because WorkflowAdvanceSettingAssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingAssigneeEntity, WorkflowAdvanceSettingAssigneeResponse> to handle null values during mapping.');
    }
    return _i24.WorkflowAdvanceSettingAssigneeResponse(
      id: model.id,
      workspaceId: model.workspaceId,
      members: model.members
          ?.map<_i22.TicketAssigneeResponse>((value) =>
              _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse(
                  value))
          .toList(),
      name: model.name,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      type: model.type,
    );
  }

  _i20.TicketNodeTagResponse
      _map__i21$TicketNodeTagEntity_To__i20$TicketNodeTagResponse(
          _i21.TicketNodeTagEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketNodeTagEntity → TicketNodeTagResponse failed because TicketNodeTagEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketNodeTagEntity, TicketNodeTagResponse> to handle null values during mapping.');
    }
    return _i20.TicketNodeTagResponse(
      tagId: model.tagId,
      ticketId: model.ticketId,
      nodeId: model.nodeId,
      tag: _map__i44$WorkflowTagEntity_To__i43$WorkflowTagResponse(model.tag),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i36.WorkFlowWrapperEntity
      _map__i35$WorkFlowWrapperResponse_To__i36$WorkFlowWrapperEntity(
          _i35.WorkFlowWrapperResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowWrapperResponse → WorkFlowWrapperEntity failed because WorkFlowWrapperResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowWrapperResponse, WorkFlowWrapperEntity> to handle null values during mapping.');
    }
    return _i36.WorkFlowWrapperEntity(
      workflow:
          _map__i35$WorkFlowResponse_To__i36$WorkFlowEntity(model.workflow),
      groupName: model.groupName,
      userInfo:
          _map__i41$WorkFlowUserInfoResponse_To__i42$WorkFlowUserInfoEntity_Nullable(
              model.userInfo),
      form: _map__i37$WorkFlowFormResponse_To__i38$WorkFlowFormEntity_Nullable(
          model.form),
      startNode:
          _map__i39$WorkFlowStartNodeResponse_To__i40$WorkFlowStartNodeEntity_Nullable(
              model.startNode),
    );
  }

  _i36.WorkFlowEntity _map__i35$WorkFlowResponse_To__i36$WorkFlowEntity(
      _i35.WorkFlowResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowResponse → WorkFlowEntity failed because WorkFlowResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowResponse, WorkFlowEntity> to handle null values during mapping.');
    }
    return _i36.WorkFlowEntity(
      workflowGroupId: model.workflowGroupId,
      id: model.id,
      name: model.name,
      isPublished: model.isPublished,
      workspaceId: model.workspaceId,
      createdBy: model.createdBy,
      prefixId: model.prefixId,
      form: _map__i37$WorkFlowFormResponse_To__i38$WorkFlowFormEntity_Nullable(
          model.form),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      description: model.description,
      colorSrc: model.colorSrc,
      iconSrc: model.iconSrc,
      advanceSetting:
          _map__i24$WorkflowAdvanceSettingResponse_To__i25$WorkflowAdvanceSettingEntity_Nullable(
              model.advanceSetting),
      state: model.state,
    );
  }

  _i21.WorkflowFormFieldPermissionEntity
      _map__i20$WorkflowFormFieldPermissionsResponse_To__i21$WorkflowFormFieldPermissionEntity(
          _i20.WorkflowFormFieldPermissionsResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowFormFieldPermissionsResponse → WorkflowFormFieldPermissionEntity failed because WorkflowFormFieldPermissionsResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowFormFieldPermissionsResponse, WorkflowFormFieldPermissionEntity> to handle null values during mapping.');
    }
    return _i21.WorkflowFormFieldPermissionEntity(
      workflowFormFieldId: model.workflowFormFieldId,
      permission: model.permission,
      permissionPath: model.permissionPath,
    );
  }

  _i38.WorkFlowFormEntity
      _map__i37$WorkFlowFormResponse_To__i38$WorkFlowFormEntity(
          _i37.WorkFlowFormResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFormResponse → WorkFlowFormEntity failed because WorkFlowFormResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFormResponse, WorkFlowFormEntity> to handle null values during mapping.');
    }
    return _i38.WorkFlowFormEntity(
      workspaceId: model.workspaceId,
      workflowId: model.workflowId,
      fields: model.fields
          .map<_i38.WorkFlowFormFieldEntity>((value) =>
              _map__i37$WorkFlowFormFieldResponse_To__i38$WorkFlowFormFieldEntity(
                  value))
          .toList(),
    );
  }

  _i38.WorkFlowFormFieldEntity
      _map__i37$WorkFlowFormFieldResponse_To__i38$WorkFlowFormFieldEntity(
          _i37.WorkFlowFormFieldResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFormFieldResponse → WorkFlowFormFieldEntity failed because WorkFlowFormFieldResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFormFieldResponse, WorkFlowFormFieldEntity> to handle null values during mapping.');
    }
    return _i38.WorkFlowFormFieldEntity(
      workflowId: model.workflowId,
      id: model.id,
      type: model.type,
      title: model.title,
      hint: model.hint,
      isRequired: model.isRequired,
      option:
          _map__i37$WorkFlowFormOptionResponse_To__i38$WorkFlowFormOptionEntity(
              model.option),
      workspaceId: model.workspaceId,
      version: model.version,
      parentId: model.parentId,
      clientId: model.clientId,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i38.WorkFlowFieldValuesEntity
      _map__i37$WorkFlowFieldValuesResponse_To__i38$WorkFlowFieldValuesEntity(
          _i37.WorkFlowFieldValuesResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFieldValuesResponse → WorkFlowFieldValuesEntity failed because WorkFlowFieldValuesResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFieldValuesResponse, WorkFlowFieldValuesEntity> to handle null values during mapping.');
    }
    return _i38.WorkFlowFieldValuesEntity(
      workspaceId: model.workspaceId,
      fieldId: model.fieldId,
      ticketId: model.ticketId,
      unitId: model.unitId,
      info: _map__i37$WorkFlowFormFieldResponse_To__i38$WorkFlowFormFieldEntity(
          model.info),
      value: model.value,
    );
  }

  _i38.WorkFlowFormOptionEntity
      _map__i37$WorkFlowFormOptionResponse_To__i38$WorkFlowFormOptionEntity(
          _i37.WorkFlowFormOptionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFormOptionResponse → WorkFlowFormOptionEntity failed because WorkFlowFormOptionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFormOptionResponse, WorkFlowFormOptionEntity> to handle null values during mapping.');
    }
    return _i38.WorkFlowFormOptionEntity(
      isThousandSeparator: model.isThousandSeparator,
      keepDecimalPlaces: model.keepDecimalPlaces,
      secondTitle: model.secondTitle,
      thirdTitle: model.thirdTitle,
      unit: model.unit,
      timeFormat: model.timeFormat,
      formula: model.formula,
      content: model.content,
      index: model.index,
      minValue: model.minValue,
      maxValue: model.maxValue,
      formulaArgs: model.formulaArgs,
      currencyPool: model.currencyPool,
      selectionPool: model.selectionPool,
      selections: model.selections
          ?.map<_i38.WorkFlowFormSelectionEntity>((value) =>
              _map__i37$WorkFlowFormSelectionResponse_To__i38$WorkFlowFormSelectionEntity(
                  value))
          .toList(),
      columns: model.columns
          ?.map<_i38.WorkFlowFormColumnEntity>((value) =>
              _map__i37$WorkFlowFormColumnResponse_To__i38$WorkFlowFormColumnEntity(
                  value))
          .toList(),
    );
  }

  _i38.WorkFlowFormSelectionEntity
      _map__i37$WorkFlowFormSelectionResponse_To__i38$WorkFlowFormSelectionEntity(
          _i37.WorkFlowFormSelectionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFormSelectionResponse → WorkFlowFormSelectionEntity failed because WorkFlowFormSelectionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFormSelectionResponse, WorkFlowFormSelectionEntity> to handle null values during mapping.');
    }
    return _i38.WorkFlowFormSelectionEntity(
      value: model.value,
      fields: model.fields
          ?.map<_i38.WorkFlowFormFieldEntity>((value) =>
              _map__i37$WorkFlowFormFieldResponse_To__i38$WorkFlowFormFieldEntity(
                  value))
          .toList(),
    );
  }

  _i38.WorkFlowFormColumnEntity
      _map__i37$WorkFlowFormColumnResponse_To__i38$WorkFlowFormColumnEntity(
          _i37.WorkFlowFormColumnResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFormColumnResponse → WorkFlowFormColumnEntity failed because WorkFlowFormColumnResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFormColumnResponse, WorkFlowFormColumnEntity> to handle null values during mapping.');
    }
    return _i38.WorkFlowFormColumnEntity(
        field:
            _map__i37$WorkFlowFormFieldResponse_To__i38$WorkFlowFormFieldEntity(
                model.field));
  }

  _i40.WorkFlowStartNodeEntity
      _map__i39$WorkFlowStartNodeResponse_To__i40$WorkFlowStartNodeEntity(
          _i39.WorkFlowStartNodeResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowStartNodeResponse → WorkFlowStartNodeEntity failed because WorkFlowStartNodeResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowStartNodeResponse, WorkFlowStartNodeEntity> to handle null values during mapping.');
    }
    return _i40.WorkFlowStartNodeEntity(
      workspaceId: model.workspaceId,
      workflowId: model.workflowId,
      id: model.id,
      slaId: model.slaId,
      admins: model.admins
          .map<_i5.TicketCreatorEntity>((value) =>
              _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity(value))
          .toList(),
      supporters: model.supporters
          .map<_i5.TicketCreatorEntity>((value) =>
              _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity(value))
          .toList(),
      assignees: model.assignees
          .map<_i5.TicketCreatorEntity>((value) =>
              _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity(value))
          .toList(),
      defaultFollowers: model.defaultFollowers
          .map<_i5.TicketCreatorEntity>((value) =>
              _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity(value))
          .toList(),
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      option:
          _map__i20$TicketNodeOptionResponse_To__i21$TicketNodeOptionEntity_Nullable(
              model.option),
    );
  }

  _i42.WorkFlowUserInfoEntity
      _map__i41$WorkFlowUserInfoResponse_To__i42$WorkFlowUserInfoEntity(
          _i41.WorkFlowUserInfoResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowUserInfoResponse → WorkFlowUserInfoEntity failed because WorkFlowUserInfoResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowUserInfoResponse, WorkFlowUserInfoEntity> to handle null values during mapping.');
    }
    return _i42.WorkFlowUserInfoEntity(
      id: model.id,
      lang: model.lang,
      displayName: model.displayName,
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
      email: model.email,
      phoneNumber: model.phoneNumber,
      title: model.title,
      department: model.department,
      companyName: model.companyName,
      listDepartments: model.listDepartments
          ?.map<_i42.WorkFlowDeparmentEntity>((value) =>
              _map__i41$WorkFlowDeparmentResponse_To__i42$WorkFlowDeparmentEntity(
                  value))
          .toList(),
      loginType: model.loginType,
      region: model.region,
      regionId: model.regionId,
    );
  }

  _i42.WorkFlowDeparmentEntity
      _map__i41$WorkFlowDeparmentResponse_To__i42$WorkFlowDeparmentEntity(
          _i41.WorkFlowDeparmentResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowDeparmentResponse → WorkFlowDeparmentEntity failed because WorkFlowDeparmentResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowDeparmentResponse, WorkFlowDeparmentEntity> to handle null values during mapping.');
    }
    return _i42.WorkFlowDeparmentEntity(
      treeId: model.treeId,
      treeName: model.treeName,
      departmentId: model.departmentId,
      department: model.department,
      roleId: model.roleId,
      title: model.title,
      departments: model.departments,
      departmentIds: model.departmentIds,
    );
  }

  _i25.WorkflowAdvanceSettingEntity
      _map__i24$WorkflowAdvanceSettingResponse_To__i25$WorkflowAdvanceSettingEntity(
          _i24.WorkflowAdvanceSettingResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingResponse → WorkflowAdvanceSettingEntity failed because WorkflowAdvanceSettingResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingResponse, WorkflowAdvanceSettingEntity> to handle null values during mapping.');
    }
    return _i25.WorkflowAdvanceSettingEntity(
      workflowId: model.workflowId,
      workspaceId: model.workspaceId,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createAt: model.createAt,
      updatedAt: model.updatedAt,
      version: model.version,
      isPublished: model.isPublished,
      allowCancel:
          _map__i24$WorkflowAdvanceSettingAllowActionResponse_To__i25$WorkflowAdvanceSettingAllowActionEntity_Nullable(
              model.allowCancel),
      allowDelete:
          _map__i24$WorkflowAdvanceSettingAllowActionResponse_To__i25$WorkflowAdvanceSettingAllowActionEntity_Nullable(
              model.allowDelete),
      approveBeforeOnHold:
          _map__i24$WorkflowAdvanceSettingAllowActionResponse_To__i25$WorkflowAdvanceSettingAllowActionEntity_Nullable(
              model.approveBeforeOnHold),
      adminOption:
          _map__i24$WorkflowAdvanceSettingAdminOptionResponse_To__i25$WorkflowAdvanceSettingAdminOptionEntity_Nullable(
              model.adminOption),
      rateOption:
          _map__i24$WorkflowAdvanceSettingRateOptionResponse_To__i25$WorkflowAdvanceSettingRateOptionEntity_Nullable(
              model.rateOption),
      allowEditingAfterResolving: model.allowEditingAfterResolving,
      dontAllowEditAfterResolveBehindNode:
          model.dontAllowEditAfterResolveBehindNode,
    );
  }

  _i25.WorkflowAdvanceSettingAllowActionEntity
      _map__i24$WorkflowAdvanceSettingAllowActionResponse_To__i25$WorkflowAdvanceSettingAllowActionEntity(
          _i24.WorkflowAdvanceSettingAllowActionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingAllowActionResponse → WorkflowAdvanceSettingAllowActionEntity failed because WorkflowAdvanceSettingAllowActionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingAllowActionResponse, WorkflowAdvanceSettingAllowActionEntity> to handle null values during mapping.');
    }
    return _i25.WorkflowAdvanceSettingAllowActionEntity(
      isAllowed: model.isAllowed,
      option:
          _map__i24$WorkflowAdvanceSettingAllowOptionResponse_To__i25$WorkflowAdvanceSettingAllowOptionEntity_Nullable(
              model.option),
      participantApplied:
          _map__i24$WorkflowAdvanceSettingAdminOptionResponse_To__i25$WorkflowAdvanceSettingAdminOptionEntity_Nullable(
              model.participantApplied),
    );
  }

  _i25.WorkflowAdvanceSettingAdminOptionEntity
      _map__i24$WorkflowAdvanceSettingAdminOptionResponse_To__i25$WorkflowAdvanceSettingAdminOptionEntity(
          _i24.WorkflowAdvanceSettingAdminOptionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingAdminOptionResponse → WorkflowAdvanceSettingAdminOptionEntity failed because WorkflowAdvanceSettingAdminOptionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingAdminOptionResponse, WorkflowAdvanceSettingAdminOptionEntity> to handle null values during mapping.');
    }
    return _i25.WorkflowAdvanceSettingAdminOptionEntity(
      adminWorkflow: model.adminWorkflow,
      adminCurrentStep: model.adminCurrentStep,
      assigneeStep: model.assigneeStep,
      requester: model.requester,
      ref: model.ref,
      participants: model.participants
          ?.map<_i25.WorkflowAdvanceSettingAssigneeEntity>((value) =>
              _map__i24$WorkflowAdvanceSettingAssigneeResponse_To__i25$WorkflowAdvanceSettingAssigneeEntity(
                  value))
          .toList(),
    );
  }

  _i25.WorkflowAdvanceSettingAllowOptionEntity
      _map__i24$WorkflowAdvanceSettingAllowOptionResponse_To__i25$WorkflowAdvanceSettingAllowOptionEntity(
          _i24.WorkflowAdvanceSettingAllowOptionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingAllowOptionResponse → WorkflowAdvanceSettingAllowOptionEntity failed because WorkflowAdvanceSettingAllowOptionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingAllowOptionResponse, WorkflowAdvanceSettingAllowOptionEntity> to handle null values during mapping.');
    }
    return _i25.WorkflowAdvanceSettingAllowOptionEntity(
      whenOpen: model.whenOpen,
      whenProcessing: model.whenProcessing,
      whenDoneOrClosed: model.whenDoneOrClosed,
      whenCanceled: model.whenCanceled,
    );
  }

  _i25.WorkflowAdvanceSettingRateOptionEntity
      _map__i24$WorkflowAdvanceSettingRateOptionResponse_To__i25$WorkflowAdvanceSettingRateOptionEntity(
          _i24.WorkflowAdvanceSettingRateOptionResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingRateOptionResponse → WorkflowAdvanceSettingRateOptionEntity failed because WorkflowAdvanceSettingRateOptionResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingRateOptionResponse, WorkflowAdvanceSettingRateOptionEntity> to handle null values during mapping.');
    }
    return _i25.WorkflowAdvanceSettingRateOptionEntity(
      autoCloseTime: model.autoCloseTime,
      autoCloseType: model.autoCloseType,
      isAllowed: model.isAllowed,
    );
  }

  _i44.WorkflowTagEntity
      _map__i43$WorkflowTagResponse_To__i44$WorkflowTagEntity(
          _i43.WorkflowTagResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowTagResponse → WorkflowTagEntity failed because WorkflowTagResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowTagResponse, WorkflowTagEntity> to handle null values during mapping.');
    }
    return _i44.WorkflowTagEntity(
      workspaceId: model.workspaceId,
      id: model.id,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      version: model.version,
      appliedWorkflowType: model.appliedWorkflowType,
      isPublished: model.isPublished,
      name: model.name,
      hashName: model.hashName,
      description: model.description,
      colorSrc: model.colorSrc,
      iconSrc: model.iconSrc,
    );
  }

  _i35.WorkFlowWrapperResponse
      _map__i36$WorkFlowWrapperEntity_To__i35$WorkFlowWrapperResponse(
          _i36.WorkFlowWrapperEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowWrapperEntity → WorkFlowWrapperResponse failed because WorkFlowWrapperEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowWrapperEntity, WorkFlowWrapperResponse> to handle null values during mapping.');
    }
    return _i35.WorkFlowWrapperResponse(
      workflow:
          _map__i36$WorkFlowEntity_To__i35$WorkFlowResponse(model.workflow),
      groupName: model.groupName,
      userInfo:
          _map__i42$WorkFlowUserInfoEntity_To__i41$WorkFlowUserInfoResponse_Nullable(
              model.userInfo),
      form: _map__i38$WorkFlowFormEntity_To__i37$WorkFlowFormResponse_Nullable(
          model.form),
      startNode:
          _map__i40$WorkFlowStartNodeEntity_To__i39$WorkFlowStartNodeResponse_Nullable(
              model.startNode),
    );
  }

  _i35.WorkFlowResponse _map__i36$WorkFlowEntity_To__i35$WorkFlowResponse(
      _i36.WorkFlowEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowEntity → WorkFlowResponse failed because WorkFlowEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowEntity, WorkFlowResponse> to handle null values during mapping.');
    }
    return _i35.WorkFlowResponse(
      workflowGroupId: model.workflowGroupId,
      id: model.id,
      name: model.name,
      isPublished: model.isPublished,
      workspaceId: model.workspaceId,
      createdBy: model.createdBy,
      prefixId: model.prefixId,
      form: _map__i38$WorkFlowFormEntity_To__i37$WorkFlowFormResponse_Nullable(
          model.form),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      description: model.description,
      colorSrc: model.colorSrc,
      iconSrc: model.iconSrc,
      advanceSetting:
          _map__i25$WorkflowAdvanceSettingEntity_To__i24$WorkflowAdvanceSettingResponse_Nullable(
              model.advanceSetting),
      state: model.state,
    );
  }

  _i20.WorkflowFormFieldPermissionsResponse
      _map__i21$WorkflowFormFieldPermissionEntity_To__i20$WorkflowFormFieldPermissionsResponse(
          _i21.WorkflowFormFieldPermissionEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowFormFieldPermissionEntity → WorkflowFormFieldPermissionsResponse failed because WorkflowFormFieldPermissionEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowFormFieldPermissionEntity, WorkflowFormFieldPermissionsResponse> to handle null values during mapping.');
    }
    return _i20.WorkflowFormFieldPermissionsResponse(
      workflowFormFieldId: model.workflowFormFieldId,
      permission: model.permission,
      permissionPath: model.permissionPath,
    );
  }

  _i37.WorkFlowFormResponse
      _map__i38$WorkFlowFormEntity_To__i37$WorkFlowFormResponse(
          _i38.WorkFlowFormEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFormEntity → WorkFlowFormResponse failed because WorkFlowFormEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFormEntity, WorkFlowFormResponse> to handle null values during mapping.');
    }
    return _i37.WorkFlowFormResponse(
      workspaceId: model.workspaceId,
      workflowId: model.workflowId,
      fields: model.fields
          .map<_i37.WorkFlowFormFieldResponse>((value) =>
              _map__i38$WorkFlowFormFieldEntity_To__i37$WorkFlowFormFieldResponse(
                  value))
          .toList(),
    );
  }

  _i37.WorkFlowFormFieldResponse
      _map__i38$WorkFlowFormFieldEntity_To__i37$WorkFlowFormFieldResponse(
          _i38.WorkFlowFormFieldEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFormFieldEntity → WorkFlowFormFieldResponse failed because WorkFlowFormFieldEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFormFieldEntity, WorkFlowFormFieldResponse> to handle null values during mapping.');
    }
    return _i37.WorkFlowFormFieldResponse(
      workflowId: model.workflowId,
      id: model.id,
      type: model.type,
      title: model.title,
      hint: model.hint,
      isRequired: model.isRequired,
      option:
          _map__i38$WorkFlowFormOptionEntity_To__i37$WorkFlowFormOptionResponse(
              model.option),
      workspaceId: model.workspaceId,
      version: model.version,
      parentId: model.parentId,
      clientId: model.clientId,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i37.WorkFlowFieldValuesResponse
      _map__i38$WorkFlowFieldValuesEntity_To__i37$WorkFlowFieldValuesResponse(
          _i38.WorkFlowFieldValuesEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFieldValuesEntity → WorkFlowFieldValuesResponse failed because WorkFlowFieldValuesEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFieldValuesEntity, WorkFlowFieldValuesResponse> to handle null values during mapping.');
    }
    return _i37.WorkFlowFieldValuesResponse(
      workspaceId: model.workspaceId,
      fieldId: model.fieldId,
      ticketId: model.ticketId,
      unitId: model.unitId,
      info: _map__i38$WorkFlowFormFieldEntity_To__i37$WorkFlowFormFieldResponse(
          model.info),
      value: model.value,
    );
  }

  _i37.WorkFlowFormOptionResponse
      _map__i38$WorkFlowFormOptionEntity_To__i37$WorkFlowFormOptionResponse(
          _i38.WorkFlowFormOptionEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFormOptionEntity → WorkFlowFormOptionResponse failed because WorkFlowFormOptionEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFormOptionEntity, WorkFlowFormOptionResponse> to handle null values during mapping.');
    }
    return _i37.WorkFlowFormOptionResponse(
      isThousandSeparator: model.isThousandSeparator,
      keepDecimalPlaces: model.keepDecimalPlaces,
      secondTitle: model.secondTitle,
      thirdTitle: model.thirdTitle,
      unit: model.unit,
      timeFormat: model.timeFormat,
      formula: model.formula,
      content: model.content,
      index: model.index,
      minValue: model.minValue,
      maxValue: model.maxValue,
      formulaArgs: model.formulaArgs,
      currencyPool: model.currencyPool,
      selectionPool: model.selectionPool,
      selections: model.selections
          ?.map<_i37.WorkFlowFormSelectionResponse>((value) =>
              _map__i38$WorkFlowFormSelectionEntity_To__i37$WorkFlowFormSelectionResponse(
                  value))
          .toList(),
      columns: model.columns
          ?.map<_i37.WorkFlowFormColumnResponse>((value) =>
              _map__i38$WorkFlowFormColumnEntity_To__i37$WorkFlowFormColumnResponse(
                  value))
          .toList(),
    );
  }

  _i37.WorkFlowFormSelectionResponse
      _map__i38$WorkFlowFormSelectionEntity_To__i37$WorkFlowFormSelectionResponse(
          _i38.WorkFlowFormSelectionEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFormSelectionEntity → WorkFlowFormSelectionResponse failed because WorkFlowFormSelectionEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFormSelectionEntity, WorkFlowFormSelectionResponse> to handle null values during mapping.');
    }
    return _i37.WorkFlowFormSelectionResponse(
      value: model.value,
      fields: model.fields
          ?.map<_i37.WorkFlowFormFieldResponse>((value) =>
              _map__i38$WorkFlowFormFieldEntity_To__i37$WorkFlowFormFieldResponse(
                  value))
          .toList(),
    );
  }

  _i37.WorkFlowFormColumnResponse
      _map__i38$WorkFlowFormColumnEntity_To__i37$WorkFlowFormColumnResponse(
          _i38.WorkFlowFormColumnEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowFormColumnEntity → WorkFlowFormColumnResponse failed because WorkFlowFormColumnEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowFormColumnEntity, WorkFlowFormColumnResponse> to handle null values during mapping.');
    }
    return _i37.WorkFlowFormColumnResponse(
        field:
            _map__i38$WorkFlowFormFieldEntity_To__i37$WorkFlowFormFieldResponse(
                model.field));
  }

  _i39.WorkFlowStartNodeResponse
      _map__i40$WorkFlowStartNodeEntity_To__i39$WorkFlowStartNodeResponse(
          _i40.WorkFlowStartNodeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowStartNodeEntity → WorkFlowStartNodeResponse failed because WorkFlowStartNodeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowStartNodeEntity, WorkFlowStartNodeResponse> to handle null values during mapping.');
    }
    return _i39.WorkFlowStartNodeResponse(
      workspaceId: model.workspaceId,
      workflowId: model.workflowId,
      id: model.id,
      slaId: model.slaId,
      admins: model.admins
          .map<_i4.TicketCreatorResponse>((value) =>
              _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse(value))
          .toList(),
      supporters: model.supporters
          .map<_i4.TicketCreatorResponse>((value) =>
              _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse(value))
          .toList(),
      assignees: model.assignees
          .map<_i4.TicketCreatorResponse>((value) =>
              _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse(value))
          .toList(),
      defaultFollowers: model.defaultFollowers
          .map<_i4.TicketCreatorResponse>((value) =>
              _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse(value))
          .toList(),
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      option:
          _map__i21$TicketNodeOptionEntity_To__i20$TicketNodeOptionResponse_Nullable(
              model.option),
    );
  }

  _i41.WorkFlowUserInfoResponse
      _map__i42$WorkFlowUserInfoEntity_To__i41$WorkFlowUserInfoResponse(
          _i42.WorkFlowUserInfoEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowUserInfoEntity → WorkFlowUserInfoResponse failed because WorkFlowUserInfoEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowUserInfoEntity, WorkFlowUserInfoResponse> to handle null values during mapping.');
    }
    return _i41.WorkFlowUserInfoResponse(
      id: model.id,
      lang: model.lang,
      displayName: model.displayName,
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
      email: model.email,
      phoneNumber: model.phoneNumber,
      title: model.title,
      department: model.department,
      companyName: model.companyName,
      listDepartments: model.listDepartments
          ?.map<_i41.WorkFlowDeparmentResponse>((value) =>
              _map__i42$WorkFlowDeparmentEntity_To__i41$WorkFlowDeparmentResponse(
                  value))
          .toList(),
      loginType: model.loginType,
      region: model.region,
      regionId: model.regionId,
    );
  }

  _i41.WorkFlowDeparmentResponse
      _map__i42$WorkFlowDeparmentEntity_To__i41$WorkFlowDeparmentResponse(
          _i42.WorkFlowDeparmentEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowDeparmentEntity → WorkFlowDeparmentResponse failed because WorkFlowDeparmentEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowDeparmentEntity, WorkFlowDeparmentResponse> to handle null values during mapping.');
    }
    return _i41.WorkFlowDeparmentResponse(
      treeId: model.treeId,
      treeName: model.treeName,
      departmentId: model.departmentId,
      department: model.department,
      roleId: model.roleId,
      title: model.title,
      departments: model.departments,
      departmentIds: model.departmentIds,
    );
  }

  _i24.WorkflowAdvanceSettingResponse
      _map__i25$WorkflowAdvanceSettingEntity_To__i24$WorkflowAdvanceSettingResponse(
          _i25.WorkflowAdvanceSettingEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingEntity → WorkflowAdvanceSettingResponse failed because WorkflowAdvanceSettingEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingEntity, WorkflowAdvanceSettingResponse> to handle null values during mapping.');
    }
    return _i24.WorkflowAdvanceSettingResponse(
      workflowId: model.workflowId,
      workspaceId: model.workspaceId,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createAt: model.createAt,
      updatedAt: model.updatedAt,
      version: model.version,
      isPublished: model.isPublished,
      allowCancel:
          _map__i25$WorkflowAdvanceSettingAllowActionEntity_To__i24$WorkflowAdvanceSettingAllowActionResponse_Nullable(
              model.allowCancel),
      allowDelete:
          _map__i25$WorkflowAdvanceSettingAllowActionEntity_To__i24$WorkflowAdvanceSettingAllowActionResponse_Nullable(
              model.allowDelete),
      approveBeforeOnHold:
          _map__i25$WorkflowAdvanceSettingAllowActionEntity_To__i24$WorkflowAdvanceSettingAllowActionResponse_Nullable(
              model.approveBeforeOnHold),
      adminOption:
          _map__i25$WorkflowAdvanceSettingAdminOptionEntity_To__i24$WorkflowAdvanceSettingAdminOptionResponse_Nullable(
              model.adminOption),
      rateOption:
          _map__i25$WorkflowAdvanceSettingRateOptionEntity_To__i24$WorkflowAdvanceSettingRateOptionResponse_Nullable(
              model.rateOption),
      allowEditingAfterResolving: model.allowEditingAfterResolving,
      dontAllowEditAfterResolveBehindNode:
          model.dontAllowEditAfterResolveBehindNode,
    );
  }

  _i24.WorkflowAdvanceSettingAllowActionResponse
      _map__i25$WorkflowAdvanceSettingAllowActionEntity_To__i24$WorkflowAdvanceSettingAllowActionResponse(
          _i25.WorkflowAdvanceSettingAllowActionEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingAllowActionEntity → WorkflowAdvanceSettingAllowActionResponse failed because WorkflowAdvanceSettingAllowActionEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingAllowActionEntity, WorkflowAdvanceSettingAllowActionResponse> to handle null values during mapping.');
    }
    return _i24.WorkflowAdvanceSettingAllowActionResponse(
      isAllowed: model.isAllowed,
      option:
          _map__i25$WorkflowAdvanceSettingAllowOptionEntity_To__i24$WorkflowAdvanceSettingAllowOptionResponse_Nullable(
              model.option),
      participantApplied:
          _map__i25$WorkflowAdvanceSettingAdminOptionEntity_To__i24$WorkflowAdvanceSettingAdminOptionResponse_Nullable(
              model.participantApplied),
    );
  }

  _i24.WorkflowAdvanceSettingAdminOptionResponse
      _map__i25$WorkflowAdvanceSettingAdminOptionEntity_To__i24$WorkflowAdvanceSettingAdminOptionResponse(
          _i25.WorkflowAdvanceSettingAdminOptionEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingAdminOptionEntity → WorkflowAdvanceSettingAdminOptionResponse failed because WorkflowAdvanceSettingAdminOptionEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingAdminOptionEntity, WorkflowAdvanceSettingAdminOptionResponse> to handle null values during mapping.');
    }
    return _i24.WorkflowAdvanceSettingAdminOptionResponse(
      adminWorkflow: model.adminWorkflow,
      adminCurrentStep: model.adminCurrentStep,
      assigneeStep: model.assigneeStep,
      requester: model.requester,
      ref: model.ref,
      participants: model.participants
          ?.map<_i24.WorkflowAdvanceSettingAssigneeResponse>((value) =>
              _map__i25$WorkflowAdvanceSettingAssigneeEntity_To__i24$WorkflowAdvanceSettingAssigneeResponse(
                  value))
          .toList(),
    );
  }

  _i24.WorkflowAdvanceSettingAllowOptionResponse
      _map__i25$WorkflowAdvanceSettingAllowOptionEntity_To__i24$WorkflowAdvanceSettingAllowOptionResponse(
          _i25.WorkflowAdvanceSettingAllowOptionEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingAllowOptionEntity → WorkflowAdvanceSettingAllowOptionResponse failed because WorkflowAdvanceSettingAllowOptionEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingAllowOptionEntity, WorkflowAdvanceSettingAllowOptionResponse> to handle null values during mapping.');
    }
    return _i24.WorkflowAdvanceSettingAllowOptionResponse(
      whenOpen: model.whenOpen,
      whenProcessing: model.whenProcessing,
      whenDoneOrClosed: model.whenDoneOrClosed,
      whenCanceled: model.whenCanceled,
    );
  }

  _i24.WorkflowAdvanceSettingRateOptionResponse
      _map__i25$WorkflowAdvanceSettingRateOptionEntity_To__i24$WorkflowAdvanceSettingRateOptionResponse(
          _i25.WorkflowAdvanceSettingRateOptionEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowAdvanceSettingRateOptionEntity → WorkflowAdvanceSettingRateOptionResponse failed because WorkflowAdvanceSettingRateOptionEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowAdvanceSettingRateOptionEntity, WorkflowAdvanceSettingRateOptionResponse> to handle null values during mapping.');
    }
    return _i24.WorkflowAdvanceSettingRateOptionResponse(
      autoCloseTime: model.autoCloseTime,
      autoCloseType: model.autoCloseType,
      isAllowed: model.isAllowed,
    );
  }

  _i31.TicketOnHoldRequestEntity
      _map__i32$TicketOnHoldRequestResponse_To__i31$TicketOnHoldRequestEntity(
          _i32.TicketOnHoldRequestResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketOnHoldRequestResponse → TicketOnHoldRequestEntity failed because TicketOnHoldRequestResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketOnHoldRequestResponse, TicketOnHoldRequestEntity> to handle null values during mapping.');
    }
    return _i31.TicketOnHoldRequestEntity(
      id: model.id,
      nodeId: model.nodeId,
      ticketId: model.ticketId,
      workspaceId: model.workspaceId,
      requesterId: model.requesterId,
      reason: model.reason,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i33.TicketReopenRequestEntity
      _map__i34$TicketReopenRequestResponse_To__i33$TicketReopenRequestEntity(
          _i34.TicketReopenRequestResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketReopenRequestResponse → TicketReopenRequestEntity failed because TicketReopenRequestResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketReopenRequestResponse, TicketReopenRequestEntity> to handle null values during mapping.');
    }
    return _i33.TicketReopenRequestEntity(reason: model.reason);
  }

  _i46.TicketSpamRequestEntity
      _map__i45$TicketSpamRequestResponse_To__i46$TicketSpamRequestEntity(
          _i45.TicketSpamRequestResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSpamRequestResponse → TicketSpamRequestEntity failed because TicketSpamRequestResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSpamRequestResponse, TicketSpamRequestEntity> to handle null values during mapping.');
    }
    return _i46.TicketSpamRequestEntity(
      createdAt: model.createdAt,
      id: model.id,
      nodeId: model.nodeId,
      reason: model.reason,
      requesterId: model.requesterId,
      ticketId: model.ticketId,
      workspaceId: model.workspaceId,
    );
  }

  _i45.TicketSpamRequestResponse
      _map__i46$TicketSpamRequestEntity_To__i45$TicketSpamRequestResponse(
          _i46.TicketSpamRequestEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketSpamRequestEntity → TicketSpamRequestResponse failed because TicketSpamRequestEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketSpamRequestEntity, TicketSpamRequestResponse> to handle null values during mapping.');
    }
    return _i45.TicketSpamRequestResponse(
      createdAt: model.createdAt,
      id: model.id,
      nodeId: model.nodeId,
      reason: model.reason,
      requesterId: model.requesterId,
      ticketId: model.ticketId,
      workspaceId: model.workspaceId,
    );
  }

  _i43.WorkflowTagResponse
      _map__i44$WorkflowTagEntity_To__i43$WorkflowTagResponse(
          _i44.WorkflowTagEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkflowTagEntity → WorkflowTagResponse failed because WorkflowTagEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkflowTagEntity, WorkflowTagResponse> to handle null values during mapping.');
    }
    return _i43.WorkflowTagResponse(
      workspaceId: model.workspaceId,
      id: model.id,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      version: model.version,
      appliedWorkflowType: model.appliedWorkflowType,
      isPublished: model.isPublished,
      name: model.name,
      hashName: model.hashName,
      description: model.description,
      colorSrc: model.colorSrc,
      iconSrc: model.iconSrc,
    );
  }

  _i48.TicketIsTicketAssigneeEntity
      _map__i47$TicketIsTicketAssigneeResponse_To__i48$TicketIsTicketAssigneeEntity(
          _i47.TicketIsTicketAssigneeResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketIsTicketAssigneeResponse → TicketIsTicketAssigneeEntity failed because TicketIsTicketAssigneeResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketIsTicketAssigneeResponse, TicketIsTicketAssigneeEntity> to handle null values during mapping.');
    }
    return _i48.TicketIsTicketAssigneeEntity(isAssignee: model.isAssignee);
  }

  _i47.TicketIsTicketAssigneeResponse
      _map__i48$TicketIsTicketAssigneeEntity_To__i47$TicketIsTicketAssigneeResponse(
          _i48.TicketIsTicketAssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping TicketIsTicketAssigneeEntity → TicketIsTicketAssigneeResponse failed because TicketIsTicketAssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<TicketIsTicketAssigneeEntity, TicketIsTicketAssigneeResponse> to handle null values during mapping.');
    }
    return _i47.TicketIsTicketAssigneeResponse(isAssignee: model.isAssignee);
  }

  _i50.WorkFlowGroupWrapperEntity
      _map__i49$WorkFlowGroupWrapperResponse_To__i50$WorkFlowGroupWrapperEntity(
          _i49.WorkFlowGroupWrapperResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowGroupWrapperResponse → WorkFlowGroupWrapperEntity failed because WorkFlowGroupWrapperResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowGroupWrapperResponse, WorkFlowGroupWrapperEntity> to handle null values during mapping.');
    }
    return _i50.WorkFlowGroupWrapperEntity(
      workflowGroup:
          _map__i49$WorkFlowGroupResponse_To__i50$WorkFlowGroupEntity(
              model.workflowGroup),
      children: model.children
          .map<_i50.WorkFlowGroupWrapperEntity>((value) =>
              _map__i49$WorkFlowGroupWrapperResponse_To__i50$WorkFlowGroupWrapperEntity(
                  value))
          .toList(),
    );
  }

  _i50.WorkFlowGroupEntity
      _map__i49$WorkFlowGroupResponse_To__i50$WorkFlowGroupEntity(
          _i49.WorkFlowGroupResponse? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkFlowGroupResponse → WorkFlowGroupEntity failed because WorkFlowGroupResponse was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkFlowGroupResponse, WorkFlowGroupEntity> to handle null values during mapping.');
    }
    return _i50.WorkFlowGroupEntity(
      id: model.id,
      parentId: model.parentId,
      workflowCount: model.workflowCount,
      hLevel: model.hLevel,
      version: model.version,
      name: model.name,
      workspaceId: model.workspaceId,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      leftBower: model.leftBower,
      rightBower: model.rightBower,
    );
  }

  _i52.GPUserEntity _map__i51$Assignee_To__i52$GPUserEntity(
      _i51.Assignee? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Assignee → GPUserEntity failed because Assignee was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Assignee, GPUserEntity> to handle null values during mapping.');
    }
    return _i52.GPUserEntity(
      id: _i72.AssigneeEntityMapper.mapIntIdToString(model),
      name: model.displayName,
      avatar: model.avatar,
      displayName: model.displayName,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i52.GPUserEntity _map__i53$AssigneeEntity_To__i52$GPUserEntity(
      _i53.AssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AssigneeEntity → GPUserEntity failed because AssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AssigneeEntity, GPUserEntity> to handle null values during mapping.');
    }
    return _i52.GPUserEntity(
      id: _i72.AssigneeEntityMapper.mapAssigneeEntityId(model),
      avatar: model.avatar,
      displayName: model.displayName,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i53.AssigneeEntity _map__i52$GPUserEntity_To__i53$AssigneeEntity(
      _i52.GPUserEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GPUserEntity → AssigneeEntity failed because GPUserEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GPUserEntity, AssigneeEntity> to handle null values during mapping.');
    }
    return _i53.AssigneeEntity(
      id: _i72.AssigneeEntityMapper.mapGPUserEntityId(model),
      displayName: _i72.AssigneeEntityMapper.mapGPUserEntityDisplayName(model),
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i53.AssigneeEntity _map__i51$Assignee_To__i53$AssigneeEntity(
      _i51.Assignee? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Assignee → AssigneeEntity failed because Assignee was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Assignee, AssigneeEntity> to handle null values during mapping.');
    }
    return _i53.AssigneeEntity(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i51$Info_To__i55$InfoEntity_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i54.WorkEntity _map__i51$Work_To__i54$WorkEntity(_i51.Work? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Work → WorkEntity failed because Work was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Work, WorkEntity> to handle null values during mapping.');
    }
    return _i54.WorkEntity(
      company: model.company,
      department: model.department,
      title: model.title,
      departmentId: model.departmentId,
      departments: model.departments,
      departmentIds: model.departmentIds,
      roleId: model.roleId,
      privacy: model.privacy,
    );
  }

  _i55.InfoEntity _map__i51$Info_To__i55$InfoEntity(_i51.Info? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Info → InfoEntity failed because Info was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Info, InfoEntity> to handle null values during mapping.');
    }
    return _i55.InfoEntity(
        work: model.work
            ?.map<_i54.WorkEntity>(
                (value) => _map__i51$Work_To__i54$WorkEntity(value))
            .toList());
  }

  _i57.ConversationEntity _map__i56$Conversation_To__i57$ConversationEntity(
      _i56.Conversation? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Conversation → ConversationEntity failed because Conversation was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Conversation, ConversationEntity> to handle null values during mapping.');
    }
    return _i57.ConversationEntity(
      id: model.id,
      name: model.name,
      role: model.role,
      avatar: model.avatar,
      folder: model.folder,
      type: model.type,
      groupLevel: model.groupLevel,
      messageCount: model.messageCount,
      memberCount: model.memberCount,
    );
  }

  _i59.OrganizationDepartmentEntity
      _map__i58$OrganizationDepartment_To__i59$OrganizationDepartmentEntity(
          _i58.OrganizationDepartment? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationDepartment → OrganizationDepartmentEntity failed because OrganizationDepartment was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationDepartment, OrganizationDepartmentEntity> to handle null values during mapping.');
    }
    return _i59.OrganizationDepartmentEntity(
      id: model.id,
      name: model.name,
      children: model.children
          .map<_i59.OrganizationDepartmentEntity>((value) =>
              _map__i58$OrganizationDepartment_To__i59$OrganizationDepartmentEntity(
                  value))
          .toList(),
      groupId: model.groupId,
      treeId: model.treeId,
      threadId: model.threadId,
      isPrimary: model.isPrimary,
    );
  }

  _i61.OrganizationRoleEntity
      _map__i60$OrganizationRole_To__i61$OrganizationRoleEntity(
          _i60.OrganizationRole? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationRole → OrganizationRoleEntity failed because OrganizationRole was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationRole, OrganizationRoleEntity> to handle null values during mapping.');
    }
    return _i61.OrganizationRoleEntity(
      name: model.name,
      workspaceId: model.workspaceId,
      id: model.id,
    );
  }

  _i63.ChatBotEntity _map__i62$ChatBotModel_To__i63$ChatBotEntity(
      _i62.ChatBotModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ChatBotModel → ChatBotEntity failed because ChatBotModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ChatBotModel, ChatBotEntity> to handle null values during mapping.');
    }
    return _i63.ChatBotEntity(
      id: model.id,
      name: model.name,
      avatar: model.avatar,
      description: model.description,
      workspaceId: model.workspaceId,
      creatorId: model.creatorId,
      createdAt: model.createdAt,
      botCreatorType: model.botCreatorType,
    );
  }

  _i65.SelectMemberEntity
      _map__i64$SelectInviteesOptions_To__i65$SelectMemberEntity(
          _i64.SelectInviteesOptions? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping SelectInviteesOptions → SelectMemberEntity failed because SelectInviteesOptions was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<SelectInviteesOptions, SelectMemberEntity> to handle null values during mapping.');
    }
    return _i65.SelectMemberEntity(
      assigneeEntities: model.selectedMembers
          ?.map<_i53.AssigneeEntity>(
              (value) => _map__i51$Assignee_To__i53$AssigneeEntity(value))
          .toList(),
      conversationEntities: model.selectedThreads
          ?.map<_i57.ConversationEntity>((value) =>
              _map__i56$Conversation_To__i57$ConversationEntity(value))
          .toList(),
      departmentEntities: model.selectedDepartments
          ?.map<_i59.OrganizationDepartmentEntity>((value) =>
              _map__i58$OrganizationDepartment_To__i59$OrganizationDepartmentEntity(
                  value))
          .toList(),
      roleEntities: model.selectedRoles
          ?.map<_i61.OrganizationRoleEntity>((value) =>
              _map__i60$OrganizationRole_To__i61$OrganizationRoleEntity(value))
          .toList(),
      chatBotEntities: model.selectedBots
          ?.map<_i63.ChatBotEntity>(
              (value) => _map__i62$ChatBotModel_To__i63$ChatBotEntity(value))
          .toList(),
    );
  }

  _i51.Assignee _map__i53$AssigneeEntity_To__i51$Assignee(
      _i53.AssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AssigneeEntity → Assignee failed because AssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AssigneeEntity, Assignee> to handle null values during mapping.');
    }
    return _i51.Assignee(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i55$InfoEntity_To__i51$Info_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i51.Work _map__i54$WorkEntity_To__i51$Work(_i54.WorkEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkEntity → Work failed because WorkEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkEntity, Work> to handle null values during mapping.');
    }
    return _i51.Work(
      company: model.company,
      department: model.department,
      title: model.title,
      departmentId: model.departmentId,
      departments: model.departments,
      departmentIds: model.departmentIds,
      roleId: model.roleId,
      privacy: model.privacy,
    );
  }

  _i51.Info _map__i55$InfoEntity_To__i51$Info(_i55.InfoEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping InfoEntity → Info failed because InfoEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<InfoEntity, Info> to handle null values during mapping.');
    }
    return _i51.Info(
        work: model.work
            ?.map<_i51.Work>(
                (value) => _map__i54$WorkEntity_To__i51$Work(value))
            .toList());
  }

  _i56.Conversation _map__i57$ConversationEntity_To__i56$Conversation(
      _i57.ConversationEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ConversationEntity → Conversation failed because ConversationEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ConversationEntity, Conversation> to handle null values during mapping.');
    }
    return _i56.Conversation(
      name: model.name,
      role: model.role,
      avatar: model.avatar,
      folder: model.folder,
      type: model.type,
      id: model.id,
      groupLevel: model.groupLevel,
      memberCount: model.memberCount,
    )..messageCount = model.messageCount;
  }

  _i60.OrganizationRole
      _map__i61$OrganizationRoleEntity_To__i60$OrganizationRole(
          _i61.OrganizationRoleEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationRoleEntity → OrganizationRole failed because OrganizationRoleEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationRoleEntity, OrganizationRole> to handle null values during mapping.');
    }
    return _i60.OrganizationRole(
      name: model.name,
      workspaceId: model.workspaceId,
      id: model.id,
    );
  }

  _i58.OrganizationDepartment
      _map__i59$OrganizationDepartmentEntity_To__i58$OrganizationDepartment(
          _i59.OrganizationDepartmentEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationDepartmentEntity → OrganizationDepartment failed because OrganizationDepartmentEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationDepartmentEntity, OrganizationDepartment> to handle null values during mapping.');
    }
    return _i58.OrganizationDepartment(
      id: model.id,
      name: model.name,
      children: model.children
          .map<_i58.OrganizationDepartment>((value) =>
              _map__i59$OrganizationDepartmentEntity_To__i58$OrganizationDepartment(
                  value))
          .toList(),
      isPrimary: model.isPrimary,
      threadId: model.threadId,
      treeId: model.treeId,
    )..groupId = model.groupId;
  }

  _i62.ChatBotModel _map__i63$ChatBotEntity_To__i62$ChatBotModel(
      _i63.ChatBotEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ChatBotEntity → ChatBotModel failed because ChatBotEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ChatBotEntity, ChatBotModel> to handle null values during mapping.');
    }
    return _i62.ChatBotModel(
      name: _i72.AssigneeEntityMapper.mapChatBotEntityName(model),
      id: _i72.AssigneeEntityMapper.mapChatBotEntityId(model),
      avatar: model.avatar,
      description: model.description,
      workspaceId: model.workspaceId,
      creatorId: model.creatorId,
      createdAt: model.createdAt,
      botCreatorType: model.botCreatorType,
    );
  }

  _i67.GpAttachmentFileUrlEntity
      _map__i66$UploadFileURLResponseModel_To__i67$GpAttachmentFileUrlEntity(
          _i66.UploadFileURLResponseModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping UploadFileURLResponseModel → GpAttachmentFileUrlEntity failed because UploadFileURLResponseModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<UploadFileURLResponseModel, GpAttachmentFileUrlEntity> to handle null values during mapping.');
    }
    return _i67.GpAttachmentFileUrlEntity(
      src: model.src,
      store: model.store,
    );
  }

  _i67.GPAttachmentFileEntity
      _map__i68$UploadFileResponseModel_To__i67$GPAttachmentFileEntity(
          _i68.UploadFileResponseModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping UploadFileResponseModel → GPAttachmentFileEntity failed because UploadFileResponseModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<UploadFileResponseModel, GPAttachmentFileEntity> to handle null values during mapping.');
    }
    return _i67.GPAttachmentFileEntity(
      uploadType: _i73.AttachmentEntityMapper.mapTypeFile(model),
      fileType: _i73.AttachmentEntityMapper.mapFileType(model),
      id: _i73.AttachmentEntityMapper.mapId(model),
      src: model.src,
      url:
          _map__i66$UploadFileURLResponseModel_To__i67$GpAttachmentFileUrlEntity_Nullable(
              model.url),
      thumbUrl:
          _map__i66$UploadFileURLResponseModel_To__i67$GpAttachmentFileUrlEntity_Nullable(
              model.thumbUrl),
      userId: _i73.AttachmentEntityMapper.mapUserId(model),
      source: model.source,
      quality: model.quality,
      srcThumbPattern: model.srcThumbPattern,
      name: _i73.AttachmentEntityMapper.mapFileName(model),
      fileLink: model.fileLink,
      type: null,
    )..size = model.size;
  }

  _i67.GPAttachmentFileEntity
      _map__i69$UploadFileResponseModelV2_To__i67$GPAttachmentFileEntity(
          _i69.UploadFileResponseModelV2? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping UploadFileResponseModelV2 → GPAttachmentFileEntity failed because UploadFileResponseModelV2 was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<UploadFileResponseModelV2, GPAttachmentFileEntity> to handle null values during mapping.');
    }
    return _i67.GPAttachmentFileEntity(
      fileType: _i73.AttachmentEntityMapper.mapFileType(model),
      id: model.id,
      src: model.src,
      url: _i73.AttachmentEntityMapper.mapUrlV2(model),
      thumbUrl: _i73.AttachmentEntityMapper.mapUrlV2(model),
      userId: model.userId,
      source: model.source,
      quality: model.quality,
      name: model.name,
      fileLink: model.fileLink,
      type: null,
    )..size = model.size;
  }

  _i67.GPAttachmentFileEntity
      _map__i70$UploadImageResponseModel_To__i67$GPAttachmentFileEntity(
          _i70.UploadImageResponseModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping UploadImageResponseModel → GPAttachmentFileEntity failed because UploadImageResponseModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<UploadImageResponseModel, GPAttachmentFileEntity> to handle null values during mapping.');
    }
    return _i67.GPAttachmentFileEntity(
      type: _i73.AttachmentEntityMapper.mapType(model),
      uploadType: _i73.AttachmentEntityMapper.mapType(model),
      fileType: _i73.AttachmentEntityMapper.mapFileType(model),
      id: _i73.AttachmentEntityMapper.mapId(model),
      src: model.src,
      url:
          _map__i66$UploadFileURLResponseModel_To__i67$GpAttachmentFileUrlEntity_Nullable(
              model.url),
      userId: _i73.AttachmentEntityMapper.mapUserId(model),
      source: model.source,
      quality: model.quality,
      category: model.category,
      width: model.width,
      height: model.height,
      fileLink: model.fileLink,
    )
      ..size = model.size
      ..fileName = model.fileName;
  }

  _i66.UploadFileURLResponseModel
      _map__i67$GpAttachmentFileUrlEntity_To__i66$UploadFileURLResponseModel(
          _i67.GpAttachmentFileUrlEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GpAttachmentFileUrlEntity → UploadFileURLResponseModel failed because GpAttachmentFileUrlEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GpAttachmentFileUrlEntity, UploadFileURLResponseModel> to handle null values during mapping.');
    }
    return _i66.UploadFileURLResponseModel(
      store: model.store,
      src: model.src,
    );
  }

  _i68.UploadFileResponseModel
      _map__i67$GPAttachmentFileEntity_To__i68$UploadFileResponseModel(
          _i67.GPAttachmentFileEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GPAttachmentFileEntity → UploadFileResponseModel failed because GPAttachmentFileEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GPAttachmentFileEntity, UploadFileResponseModel> to handle null values during mapping.');
    }
    return _i68.UploadFileResponseModel(
      id: _i73.AttachmentEntityMapper.mapToId(model),
      name: model.name,
      userId: _i73.AttachmentEntityMapper.mapToUserId(model),
      size: model.size,
      fileType: _i73.AttachmentEntityMapper.mapFileTypeEntity(model),
      url:
          _map__i67$GpAttachmentFileUrlEntity_To__i66$UploadFileURLResponseModel_Nullable(
              model.url),
      thumbUrl:
          _map__i67$GpAttachmentFileUrlEntity_To__i66$UploadFileURLResponseModel_Nullable(
              model.thumbUrl),
      fileLink: model.fileLink,
      quality: model.quality,
      source: model.source,
      uploadType: _i73.AttachmentEntityMapper.mapUploadTypeEntity(model),
    )
      ..src = model.src
      ..srcThumbPattern = model.srcThumbPattern;
  }

  _i69.UploadFileResponseModelV2
      _map__i67$GPAttachmentFileEntity_To__i69$UploadFileResponseModelV2(
          _i67.GPAttachmentFileEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GPAttachmentFileEntity → UploadFileResponseModelV2 failed because GPAttachmentFileEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GPAttachmentFileEntity, UploadFileResponseModelV2> to handle null values during mapping.');
    }
    return _i69.UploadFileResponseModelV2(
      id: model.id,
      name: model.name,
      userId: model.userId,
      size: model.size,
      fileType: _i73.AttachmentEntityMapper.mapFileTypeEntity(model),
      url: _i73.AttachmentEntityMapper.mapToUrlV2(model),
      thumbUrl: _i73.AttachmentEntityMapper.mapToUrlV2(model),
      fileLink: model.fileLink,
      quality: model.quality,
      source: model.source,
    )..src = model.src;
  }

  _i5.TicketCreatorEntity?
      _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity_Nullable(
          _i4.TicketCreatorResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i5.TicketCreatorEntity(
      id: model.id,
      displayName: model.displayName,
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
      email: model.email,
      phoneNumber: model.phoneNumber,
      title: model.title,
      department: model.department,
      companyName: model.companyName,
      emloyeeCode: model.emloyeeCode,
      identifierCode: model.identifierCode,
      loginType: model.loginType,
      region: model.region,
      regionId: model.regionId,
    );
  }

  _i7.TicketActivityContentEntity?
      _map__i6$TicketActivityContentResponse_To__i7$TicketActivityContentEntity_Nullable(
          _i6.TicketActivityContentResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i7.TicketActivityContentEntity(
      en: _map__i6$TicketActivityContentItemResponse_To__i7$TicketActivityContentItemEntity(
          model.en),
      vi: _map__i6$TicketActivityContentItemResponse_To__i7$TicketActivityContentItemEntity(
          model.vi),
    );
  }

  _i9.TicketActivityEntity?
      _map__i8$TicketActivityResponse_To__i9$TicketActivityEntity_Nullable(
          _i8.TicketActivityResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i9.TicketActivityEntity(
      id: model.id,
      workspaceId: model.workspaceId,
      ticketId: model.ticketId,
      workflowId: model.workflowId,
      actorId: model.actorId,
      actorType: model.actorType,
      type: model.type,
      targetId: model.targetId,
      objectId: model.objectId,
      actor: _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity_Nullable(
          model.actor),
      createdAt: model.createdAt,
      content:
          _map__i6$TicketActivityContentResponse_To__i7$TicketActivityContentEntity_Nullable(
              model.content),
      additionalRequest:
          _map__i12$TicketAdditionalRequestResponse_To__i13$TicketAdditionalRequestEntity_Nullable(
              model.additionalRequest),
      onHoldRequest:
          _map__i32$TicketOnHoldRequestResponse_To__i31$TicketOnHoldRequestEntity_Nullable(
              model.onHoldRequest),
      reopenRequest:
          _map__i34$TicketReopenRequestResponse_To__i33$TicketReopenRequestEntity_Nullable(
              model.reopenRequest),
      reason: model.reason,
    );
  }

  _i11.TicketShiftDetailEntity?
      _map__i10$TicketShiftDetailResponse_To__i11$TicketShiftDetailEntity_Nullable(
          _i10.TicketShiftDetailResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i11.TicketShiftDetailEntity(
      id: model.id,
      workspaceId: model.workspaceId,
      name: model.name,
      description: model.description,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i13.TicketAdditionalRequestEntity?
      _map__i12$TicketAdditionalRequestResponse_To__i13$TicketAdditionalRequestEntity_Nullable(
          _i12.TicketAdditionalRequestResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i13.TicketAdditionalRequestEntity(
      workspaceId: model.workspaceId,
      ticketId: model.ticketId,
      nodeId: model.nodeId,
      id: model.id,
      contentNeedAdd: model.contentNeedAdd,
      responseToRequester: model.responseToRequester,
      targetId: model.targetId,
      requesterId: model.requesterId,
      attachedFiles: model.attachedFiles
          ?.map<_i67.GPAttachmentFileEntity>((value) =>
              _map__i69$UploadFileResponseModelV2_To__i67$GPAttachmentFileEntity(
                  value))
          .toList(),
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i15.TicketSLAEntity?
      _map__i14$TicketSLAResponse_To__i15$TicketSLAEntity_Nullable(
          _i14.TicketSLAResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i15.TicketSLAEntity(
      id: model.id,
      shiftId: model.shiftId,
      workspaceId: model.workspaceId,
      name: model.name,
      status: model.status,
      slaPriorityLevels: model.slaPriorityLevels
          ?.map<_i15.TicketSLAPriorityLevelsEntity>((value) =>
              _map__i14$TicketSLAPriorityLevelsResponse_To__i15$TicketSLAPriorityLevelsEntity(
                  value))
          .toList(),
      slaNotifications: model.slaNotifications
          ?.map<_i15.TicketSLANotificationEntity>((value) =>
              _map__i14$TicketSLANotificationResponse_To__i15$TicketSLANotificationEntity(
                  value))
          .toList(),
      isDefault: model.isDefault,
      isDeleted: model.isDeleted,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i17.TicketSLAAggEntity?
      _map__i16$TicketSLAAggResponse_To__i17$TicketSLAAggEntity_Nullable(
          _i16.TicketSLAAggResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i17.TicketSLAAggEntity(
      shiftAgg:
          _map__i16$TicketShiftAggResponse_To__i17$TicketShiftAggEntity_Nullable(
              model.shiftAgg),
      sla: _map__i14$TicketSLAResponse_To__i15$TicketSLAEntity_Nullable(
          model.sla),
    );
  }

  _i17.TicketShiftAggEntity?
      _map__i16$TicketShiftAggResponse_To__i17$TicketShiftAggEntity_Nullable(
          _i16.TicketShiftAggResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i17.TicketShiftAggEntity(
      dateOffs: model.dateOffs,
      weeklySettings: model.weeklySettings,
      shift:
          _map__i10$TicketShiftDetailResponse_To__i11$TicketShiftDetailEntity_Nullable(
              model.shift),
    );
  }

  _i15.TicketSLAPriorityLevelFirstEntity?
      _map__i14$TicketSLAPriorityLevelFirstResponse_To__i15$TicketSLAPriorityLevelFirstEntity_Nullable(
          _i14.TicketSLAPriorityLevelFirstResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i15.TicketSLAPriorityLevelFirstEntity(
      type: model.type,
      value: model.value,
    );
  }

  _i19.TickeUserRoleEntity?
      _map__i18$TickeUserRoleResponse_To__i19$TickeUserRoleEntity_Nullable(
          _i18.TickeUserRoleResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i19.TickeUserRoleEntity(
      userWhoCanCancelTicket: model.userWhoCanCancelTicket,
      userWhoCanDeleteTicket: model.userWhoCanDeleteTicket,
      userWhoCanAcceptDeclineOnHoldRequest:
          model.userWhoCanAcceptDeclineOnHoldRequest,
      requester: model.requester,
      sameDepartmentWithRequester: model.sameDepartmentWithRequester,
      userWhoIsAdminWorkflow: model.userWhoIsAdminWorkflow,
      userRoleInNode: model.userRoleInNode,
    );
  }

  _i21.TicketNodeOptionEntity?
      _map__i20$TicketNodeOptionResponse_To__i21$TicketNodeOptionEntity_Nullable(
          _i20.TicketNodeOptionResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i21.TicketNodeOptionEntity(
      name: model.name,
      assigneeType: model.assigneeType,
      workflowFormFieldPermissions: model.workflowFormFieldPermissions
          ?.map<_i21.WorkflowFormFieldPermissionEntity>((value) =>
              _map__i20$WorkflowFormFieldPermissionsResponse_To__i21$WorkflowFormFieldPermissionEntity(
                  value))
          .toList(),
    );
  }

  _i23.TicketAssigneeEntity?
      _map__i22$TicketAssigneeResponse_To__i23$TicketAssigneeEntity_Nullable(
          _i22.TicketAssigneeResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i23.TicketAssigneeEntity(
      id: model.id,
      name: model.name,
      workspaceId: model.workspaceId,
      info: _map__i51$Assignee_To__i53$AssigneeEntity_Nullable(model.info),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      type: model.type,
    );
  }

  _i28.CommentAs? _map__i26$CommentAsResponse_To__i28$CommentAs_Nullable(
      _i26.CommentAsResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i28.CommentAs(
      authorType: model.authorType,
      authorId: model.authorId,
    );
  }

  _i4.TicketCreatorResponse?
      _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse_Nullable(
          _i5.TicketCreatorEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i4.TicketCreatorResponse(
      id: model.id,
      displayName: model.displayName,
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
      email: model.email,
      phoneNumber: model.phoneNumber,
      title: model.title,
      department: model.department,
      companyName: model.companyName,
      emloyeeCode: model.emloyeeCode,
      identifierCode: model.identifierCode,
      loginType: model.loginType,
      region: model.region,
      regionId: model.regionId,
    );
  }

  _i6.TicketActivityContentResponse?
      _map__i7$TicketActivityContentEntity_To__i6$TicketActivityContentResponse_Nullable(
          _i7.TicketActivityContentEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i6.TicketActivityContentResponse(
      en: _map__i7$TicketActivityContentItemEntity_To__i6$TicketActivityContentItemResponse_Nullable(
          model.en),
      vi: _map__i7$TicketActivityContentItemEntity_To__i6$TicketActivityContentItemResponse_Nullable(
          model.vi),
    );
  }

  _i6.TicketActivityContentItemResponse?
      _map__i7$TicketActivityContentItemEntity_To__i6$TicketActivityContentItemResponse_Nullable(
          _i7.TicketActivityContentItemEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i6.TicketActivityContentItemResponse(
      markdownText: model.markdownText,
      text: model.text,
      highlights: model.highlights
          ?.map<_i6.TicketHighlightResponse>((value) =>
              _map__i7$TicketHighlightEntity_To__i6$TicketHighlightResponse(
                  value))
          .toList(),
    );
  }

  _i8.TicketActivityResponse?
      _map__i9$TicketActivityEntity_To__i8$TicketActivityResponse_Nullable(
          _i9.TicketActivityEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i8.TicketActivityResponse(
      workspaceId: model.workspaceId,
      id: model.id,
      ticketId: model.ticketId,
      workflowId: model.workflowId,
      actorId: model.actorId,
      actorType: model.actorType,
      type: model.type,
      targetId: model.targetId,
      objectId: model.objectId,
      actor: _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse_Nullable(
          model.actor),
      createdAt: model.createdAt,
      content:
          _map__i7$TicketActivityContentEntity_To__i6$TicketActivityContentResponse_Nullable(
              model.content),
      additionalRequest:
          _map__i13$TicketAdditionalRequestEntity_To__i12$TicketAdditionalRequestResponse_Nullable(
              model.additionalRequest),
      onHoldRequest:
          _map__i31$TicketOnHoldRequestEntity_To__i32$TicketOnHoldRequestResponse_Nullable(
              model.onHoldRequest),
      reopenRequest:
          _map__i33$TicketReopenRequestEntity_To__i34$TicketReopenRequestResponse_Nullable(
              model.reopenRequest),
      reason: model.reason,
    );
  }

  _i10.TicketShiftDetailResponse?
      _map__i11$TicketShiftDetailEntity_To__i10$TicketShiftDetailResponse_Nullable(
          _i11.TicketShiftDetailEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i10.TicketShiftDetailResponse(
      id: model.id,
      workspaceId: model.workspaceId,
      name: model.name,
      description: model.description,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i12.TicketAdditionalRequestResponse?
      _map__i13$TicketAdditionalRequestEntity_To__i12$TicketAdditionalRequestResponse_Nullable(
          _i13.TicketAdditionalRequestEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i12.TicketAdditionalRequestResponse(
      workspaceId: model.workspaceId,
      ticketId: model.ticketId,
      nodeId: model.nodeId,
      id: model.id,
      contentNeedAdd: model.contentNeedAdd,
      responseToRequester: model.responseToRequester,
      status: model.status,
      requesterId: model.requesterId,
      targetId: model.targetId,
      attachedFiles: model.attachedFiles
          ?.map<_i69.UploadFileResponseModelV2>((value) =>
              _map__i67$GPAttachmentFileEntity_To__i69$UploadFileResponseModelV2(
                  value))
          .toList(),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i32.TicketOnHoldRequestResponse?
      _map__i31$TicketOnHoldRequestEntity_To__i32$TicketOnHoldRequestResponse_Nullable(
          _i31.TicketOnHoldRequestEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i32.TicketOnHoldRequestResponse(
      id: model.id,
      nodeId: model.nodeId,
      ticketId: model.ticketId,
      workspaceId: model.workspaceId,
      requesterId: model.requesterId,
      reason: model.reason,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i34.TicketReopenRequestResponse?
      _map__i33$TicketReopenRequestEntity_To__i34$TicketReopenRequestResponse_Nullable(
          _i33.TicketReopenRequestEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i34.TicketReopenRequestResponse(reason: model.reason);
  }

  _i16.TicketSLAAggResponse?
      _map__i17$TicketSLAAggEntity_To__i16$TicketSLAAggResponse_Nullable(
          _i17.TicketSLAAggEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i16.TicketSLAAggResponse(
      shiftAgg:
          _map__i17$TicketShiftAggEntity_To__i16$TicketShiftAggResponse_Nullable(
              model.shiftAgg),
      sla: _map__i15$TicketSLAEntity_To__i14$TicketSLAResponse_Nullable(
          model.sla),
    );
  }

  _i16.TicketShiftAggResponse?
      _map__i17$TicketShiftAggEntity_To__i16$TicketShiftAggResponse_Nullable(
          _i17.TicketShiftAggEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i16.TicketShiftAggResponse(
      dateOffs: model.dateOffs,
      weeklySettings: model.weeklySettings,
      shift:
          _map__i11$TicketShiftDetailEntity_To__i10$TicketShiftDetailResponse_Nullable(
              model.shift),
    );
  }

  _i14.TicketSLAResponse?
      _map__i15$TicketSLAEntity_To__i14$TicketSLAResponse_Nullable(
          _i15.TicketSLAEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i14.TicketSLAResponse(
      id: model.id,
      shiftId: model.shiftId,
      workspaceId: model.workspaceId,
      name: model.name,
      status: model.status,
      slaPriorityLevels: model.slaPriorityLevels
          ?.map<_i14.TicketSLAPriorityLevelsResponse>((value) =>
              _map__i15$TicketSLAPriorityLevelsEntity_To__i14$TicketSLAPriorityLevelsResponse(
                  value))
          .toList(),
      slaNotifications: model.slaNotifications
          ?.map<_i14.TicketSLANotificationResponse>((value) =>
              _map__i15$TicketSLANotificationEntity_To__i14$TicketSLANotificationResponse(
                  value))
          .toList(),
      isDefault: model.isDefault,
      isDeleted: model.isDeleted,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i14.TicketSLAPriorityLevelFirstResponse?
      _map__i15$TicketSLAPriorityLevelFirstEntity_To__i14$TicketSLAPriorityLevelFirstResponse_Nullable(
          _i15.TicketSLAPriorityLevelFirstEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i14.TicketSLAPriorityLevelFirstResponse(
      type: model.type,
      value: model.value,
    );
  }

  _i18.TickeUserRoleResponse?
      _map__i19$TickeUserRoleEntity_To__i18$TickeUserRoleResponse_Nullable(
          _i19.TickeUserRoleEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i18.TickeUserRoleResponse(
      userWhoCanCancelTicket: model.userWhoCanCancelTicket,
      userWhoCanDeleteTicket: model.userWhoCanDeleteTicket,
      userWhoCanAcceptDeclineOnHoldRequest:
          model.userWhoCanAcceptDeclineOnHoldRequest,
      requester: model.requester,
      sameDepartmentWithRequester: model.sameDepartmentWithRequester,
      userWhoIsAdminWorkflow: model.userWhoIsAdminWorkflow,
      userRoleInNode: model.userRoleInNode,
    );
  }

  _i20.TicketNodeOptionResponse?
      _map__i21$TicketNodeOptionEntity_To__i20$TicketNodeOptionResponse_Nullable(
          _i21.TicketNodeOptionEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i20.TicketNodeOptionResponse(
      name: model.name,
      assigneeType: model.assigneeType,
      workflowFormFieldPermissions: model.workflowFormFieldPermissions
          ?.map<_i20.WorkflowFormFieldPermissionsResponse>((value) =>
              _map__i21$WorkflowFormFieldPermissionEntity_To__i20$WorkflowFormFieldPermissionsResponse(
                  value))
          .toList(),
    );
  }

  _i22.TicketAssigneeResponse?
      _map__i23$TicketAssigneeEntity_To__i22$TicketAssigneeResponse_Nullable(
          _i23.TicketAssigneeEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i22.TicketAssigneeResponse(
      id: model.id,
      name: model.name,
      workspaceId: model.workspaceId,
      info: _map__i53$AssigneeEntity_To__i51$Assignee_Nullable(model.info),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      type: model.type,
    );
  }

  _i36.WorkFlowEntity?
      _map__i35$WorkFlowResponse_To__i36$WorkFlowEntity_Nullable(
          _i35.WorkFlowResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i36.WorkFlowEntity(
      workflowGroupId: model.workflowGroupId,
      id: model.id,
      name: model.name,
      isPublished: model.isPublished,
      workspaceId: model.workspaceId,
      createdBy: model.createdBy,
      prefixId: model.prefixId,
      form: _map__i37$WorkFlowFormResponse_To__i38$WorkFlowFormEntity_Nullable(
          model.form),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      description: model.description,
      colorSrc: model.colorSrc,
      iconSrc: model.iconSrc,
      advanceSetting:
          _map__i24$WorkflowAdvanceSettingResponse_To__i25$WorkflowAdvanceSettingEntity_Nullable(
              model.advanceSetting),
      state: model.state,
    );
  }

  _i38.WorkFlowFormEntity?
      _map__i37$WorkFlowFormResponse_To__i38$WorkFlowFormEntity_Nullable(
          _i37.WorkFlowFormResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i38.WorkFlowFormEntity(
      workspaceId: model.workspaceId,
      workflowId: model.workflowId,
      fields: model.fields
          .map<_i38.WorkFlowFormFieldEntity>((value) =>
              _map__i37$WorkFlowFormFieldResponse_To__i38$WorkFlowFormFieldEntity(
                  value))
          .toList(),
    );
  }

  _i40.WorkFlowStartNodeEntity?
      _map__i39$WorkFlowStartNodeResponse_To__i40$WorkFlowStartNodeEntity_Nullable(
          _i39.WorkFlowStartNodeResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i40.WorkFlowStartNodeEntity(
      workspaceId: model.workspaceId,
      workflowId: model.workflowId,
      id: model.id,
      slaId: model.slaId,
      admins: model.admins
          .map<_i5.TicketCreatorEntity>((value) =>
              _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity(value))
          .toList(),
      supporters: model.supporters
          .map<_i5.TicketCreatorEntity>((value) =>
              _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity(value))
          .toList(),
      assignees: model.assignees
          .map<_i5.TicketCreatorEntity>((value) =>
              _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity(value))
          .toList(),
      defaultFollowers: model.defaultFollowers
          .map<_i5.TicketCreatorEntity>((value) =>
              _map__i4$TicketCreatorResponse_To__i5$TicketCreatorEntity(value))
          .toList(),
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      option:
          _map__i20$TicketNodeOptionResponse_To__i21$TicketNodeOptionEntity_Nullable(
              model.option),
    );
  }

  _i42.WorkFlowUserInfoEntity?
      _map__i41$WorkFlowUserInfoResponse_To__i42$WorkFlowUserInfoEntity_Nullable(
          _i41.WorkFlowUserInfoResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i42.WorkFlowUserInfoEntity(
      id: model.id,
      lang: model.lang,
      displayName: model.displayName,
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
      email: model.email,
      phoneNumber: model.phoneNumber,
      title: model.title,
      department: model.department,
      companyName: model.companyName,
      listDepartments: model.listDepartments
          ?.map<_i42.WorkFlowDeparmentEntity>((value) =>
              _map__i41$WorkFlowDeparmentResponse_To__i42$WorkFlowDeparmentEntity(
                  value))
          .toList(),
      loginType: model.loginType,
      region: model.region,
      regionId: model.regionId,
    );
  }

  _i25.WorkflowAdvanceSettingEntity?
      _map__i24$WorkflowAdvanceSettingResponse_To__i25$WorkflowAdvanceSettingEntity_Nullable(
          _i24.WorkflowAdvanceSettingResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i25.WorkflowAdvanceSettingEntity(
      workflowId: model.workflowId,
      workspaceId: model.workspaceId,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createAt: model.createAt,
      updatedAt: model.updatedAt,
      version: model.version,
      isPublished: model.isPublished,
      allowCancel:
          _map__i24$WorkflowAdvanceSettingAllowActionResponse_To__i25$WorkflowAdvanceSettingAllowActionEntity_Nullable(
              model.allowCancel),
      allowDelete:
          _map__i24$WorkflowAdvanceSettingAllowActionResponse_To__i25$WorkflowAdvanceSettingAllowActionEntity_Nullable(
              model.allowDelete),
      approveBeforeOnHold:
          _map__i24$WorkflowAdvanceSettingAllowActionResponse_To__i25$WorkflowAdvanceSettingAllowActionEntity_Nullable(
              model.approveBeforeOnHold),
      adminOption:
          _map__i24$WorkflowAdvanceSettingAdminOptionResponse_To__i25$WorkflowAdvanceSettingAdminOptionEntity_Nullable(
              model.adminOption),
      rateOption:
          _map__i24$WorkflowAdvanceSettingRateOptionResponse_To__i25$WorkflowAdvanceSettingRateOptionEntity_Nullable(
              model.rateOption),
      allowEditingAfterResolving: model.allowEditingAfterResolving,
      dontAllowEditAfterResolveBehindNode:
          model.dontAllowEditAfterResolveBehindNode,
    );
  }

  _i25.WorkflowAdvanceSettingAllowActionEntity?
      _map__i24$WorkflowAdvanceSettingAllowActionResponse_To__i25$WorkflowAdvanceSettingAllowActionEntity_Nullable(
          _i24.WorkflowAdvanceSettingAllowActionResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i25.WorkflowAdvanceSettingAllowActionEntity(
      isAllowed: model.isAllowed,
      option:
          _map__i24$WorkflowAdvanceSettingAllowOptionResponse_To__i25$WorkflowAdvanceSettingAllowOptionEntity_Nullable(
              model.option),
      participantApplied:
          _map__i24$WorkflowAdvanceSettingAdminOptionResponse_To__i25$WorkflowAdvanceSettingAdminOptionEntity_Nullable(
              model.participantApplied),
    );
  }

  _i25.WorkflowAdvanceSettingAdminOptionEntity?
      _map__i24$WorkflowAdvanceSettingAdminOptionResponse_To__i25$WorkflowAdvanceSettingAdminOptionEntity_Nullable(
          _i24.WorkflowAdvanceSettingAdminOptionResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i25.WorkflowAdvanceSettingAdminOptionEntity(
      adminWorkflow: model.adminWorkflow,
      adminCurrentStep: model.adminCurrentStep,
      assigneeStep: model.assigneeStep,
      requester: model.requester,
      ref: model.ref,
      participants: model.participants
          ?.map<_i25.WorkflowAdvanceSettingAssigneeEntity>((value) =>
              _map__i24$WorkflowAdvanceSettingAssigneeResponse_To__i25$WorkflowAdvanceSettingAssigneeEntity(
                  value))
          .toList(),
    );
  }

  _i25.WorkflowAdvanceSettingAllowOptionEntity?
      _map__i24$WorkflowAdvanceSettingAllowOptionResponse_To__i25$WorkflowAdvanceSettingAllowOptionEntity_Nullable(
          _i24.WorkflowAdvanceSettingAllowOptionResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i25.WorkflowAdvanceSettingAllowOptionEntity(
      whenOpen: model.whenOpen,
      whenProcessing: model.whenProcessing,
      whenDoneOrClosed: model.whenDoneOrClosed,
      whenCanceled: model.whenCanceled,
    );
  }

  _i25.WorkflowAdvanceSettingRateOptionEntity?
      _map__i24$WorkflowAdvanceSettingRateOptionResponse_To__i25$WorkflowAdvanceSettingRateOptionEntity_Nullable(
          _i24.WorkflowAdvanceSettingRateOptionResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i25.WorkflowAdvanceSettingRateOptionEntity(
      autoCloseTime: model.autoCloseTime,
      autoCloseType: model.autoCloseType,
      isAllowed: model.isAllowed,
    );
  }

  _i35.WorkFlowResponse?
      _map__i36$WorkFlowEntity_To__i35$WorkFlowResponse_Nullable(
          _i36.WorkFlowEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i35.WorkFlowResponse(
      workflowGroupId: model.workflowGroupId,
      id: model.id,
      name: model.name,
      isPublished: model.isPublished,
      workspaceId: model.workspaceId,
      createdBy: model.createdBy,
      prefixId: model.prefixId,
      form: _map__i38$WorkFlowFormEntity_To__i37$WorkFlowFormResponse_Nullable(
          model.form),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      description: model.description,
      colorSrc: model.colorSrc,
      iconSrc: model.iconSrc,
      advanceSetting:
          _map__i25$WorkflowAdvanceSettingEntity_To__i24$WorkflowAdvanceSettingResponse_Nullable(
              model.advanceSetting),
      state: model.state,
    );
  }

  _i37.WorkFlowFormResponse?
      _map__i38$WorkFlowFormEntity_To__i37$WorkFlowFormResponse_Nullable(
          _i38.WorkFlowFormEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i37.WorkFlowFormResponse(
      workspaceId: model.workspaceId,
      workflowId: model.workflowId,
      fields: model.fields
          .map<_i37.WorkFlowFormFieldResponse>((value) =>
              _map__i38$WorkFlowFormFieldEntity_To__i37$WorkFlowFormFieldResponse(
                  value))
          .toList(),
    );
  }

  _i39.WorkFlowStartNodeResponse?
      _map__i40$WorkFlowStartNodeEntity_To__i39$WorkFlowStartNodeResponse_Nullable(
          _i40.WorkFlowStartNodeEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i39.WorkFlowStartNodeResponse(
      workspaceId: model.workspaceId,
      workflowId: model.workflowId,
      id: model.id,
      slaId: model.slaId,
      admins: model.admins
          .map<_i4.TicketCreatorResponse>((value) =>
              _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse(value))
          .toList(),
      supporters: model.supporters
          .map<_i4.TicketCreatorResponse>((value) =>
              _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse(value))
          .toList(),
      assignees: model.assignees
          .map<_i4.TicketCreatorResponse>((value) =>
              _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse(value))
          .toList(),
      defaultFollowers: model.defaultFollowers
          .map<_i4.TicketCreatorResponse>((value) =>
              _map__i5$TicketCreatorEntity_To__i4$TicketCreatorResponse(value))
          .toList(),
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      option:
          _map__i21$TicketNodeOptionEntity_To__i20$TicketNodeOptionResponse_Nullable(
              model.option),
    );
  }

  _i41.WorkFlowUserInfoResponse?
      _map__i42$WorkFlowUserInfoEntity_To__i41$WorkFlowUserInfoResponse_Nullable(
          _i42.WorkFlowUserInfoEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i41.WorkFlowUserInfoResponse(
      id: model.id,
      lang: model.lang,
      displayName: model.displayName,
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
      email: model.email,
      phoneNumber: model.phoneNumber,
      title: model.title,
      department: model.department,
      companyName: model.companyName,
      listDepartments: model.listDepartments
          ?.map<_i41.WorkFlowDeparmentResponse>((value) =>
              _map__i42$WorkFlowDeparmentEntity_To__i41$WorkFlowDeparmentResponse(
                  value))
          .toList(),
      loginType: model.loginType,
      region: model.region,
      regionId: model.regionId,
    );
  }

  _i24.WorkflowAdvanceSettingResponse?
      _map__i25$WorkflowAdvanceSettingEntity_To__i24$WorkflowAdvanceSettingResponse_Nullable(
          _i25.WorkflowAdvanceSettingEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i24.WorkflowAdvanceSettingResponse(
      workflowId: model.workflowId,
      workspaceId: model.workspaceId,
      createdBy: model.createdBy,
      updatedBy: model.updatedBy,
      createAt: model.createAt,
      updatedAt: model.updatedAt,
      version: model.version,
      isPublished: model.isPublished,
      allowCancel:
          _map__i25$WorkflowAdvanceSettingAllowActionEntity_To__i24$WorkflowAdvanceSettingAllowActionResponse_Nullable(
              model.allowCancel),
      allowDelete:
          _map__i25$WorkflowAdvanceSettingAllowActionEntity_To__i24$WorkflowAdvanceSettingAllowActionResponse_Nullable(
              model.allowDelete),
      approveBeforeOnHold:
          _map__i25$WorkflowAdvanceSettingAllowActionEntity_To__i24$WorkflowAdvanceSettingAllowActionResponse_Nullable(
              model.approveBeforeOnHold),
      adminOption:
          _map__i25$WorkflowAdvanceSettingAdminOptionEntity_To__i24$WorkflowAdvanceSettingAdminOptionResponse_Nullable(
              model.adminOption),
      rateOption:
          _map__i25$WorkflowAdvanceSettingRateOptionEntity_To__i24$WorkflowAdvanceSettingRateOptionResponse_Nullable(
              model.rateOption),
      allowEditingAfterResolving: model.allowEditingAfterResolving,
      dontAllowEditAfterResolveBehindNode:
          model.dontAllowEditAfterResolveBehindNode,
    );
  }

  _i24.WorkflowAdvanceSettingAllowActionResponse?
      _map__i25$WorkflowAdvanceSettingAllowActionEntity_To__i24$WorkflowAdvanceSettingAllowActionResponse_Nullable(
          _i25.WorkflowAdvanceSettingAllowActionEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i24.WorkflowAdvanceSettingAllowActionResponse(
      isAllowed: model.isAllowed,
      option:
          _map__i25$WorkflowAdvanceSettingAllowOptionEntity_To__i24$WorkflowAdvanceSettingAllowOptionResponse_Nullable(
              model.option),
      participantApplied:
          _map__i25$WorkflowAdvanceSettingAdminOptionEntity_To__i24$WorkflowAdvanceSettingAdminOptionResponse_Nullable(
              model.participantApplied),
    );
  }

  _i24.WorkflowAdvanceSettingAdminOptionResponse?
      _map__i25$WorkflowAdvanceSettingAdminOptionEntity_To__i24$WorkflowAdvanceSettingAdminOptionResponse_Nullable(
          _i25.WorkflowAdvanceSettingAdminOptionEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i24.WorkflowAdvanceSettingAdminOptionResponse(
      adminWorkflow: model.adminWorkflow,
      adminCurrentStep: model.adminCurrentStep,
      assigneeStep: model.assigneeStep,
      requester: model.requester,
      ref: model.ref,
      participants: model.participants
          ?.map<_i24.WorkflowAdvanceSettingAssigneeResponse>((value) =>
              _map__i25$WorkflowAdvanceSettingAssigneeEntity_To__i24$WorkflowAdvanceSettingAssigneeResponse(
                  value))
          .toList(),
    );
  }

  _i24.WorkflowAdvanceSettingAllowOptionResponse?
      _map__i25$WorkflowAdvanceSettingAllowOptionEntity_To__i24$WorkflowAdvanceSettingAllowOptionResponse_Nullable(
          _i25.WorkflowAdvanceSettingAllowOptionEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i24.WorkflowAdvanceSettingAllowOptionResponse(
      whenOpen: model.whenOpen,
      whenProcessing: model.whenProcessing,
      whenDoneOrClosed: model.whenDoneOrClosed,
      whenCanceled: model.whenCanceled,
    );
  }

  _i24.WorkflowAdvanceSettingRateOptionResponse?
      _map__i25$WorkflowAdvanceSettingRateOptionEntity_To__i24$WorkflowAdvanceSettingRateOptionResponse_Nullable(
          _i25.WorkflowAdvanceSettingRateOptionEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i24.WorkflowAdvanceSettingRateOptionResponse(
      autoCloseTime: model.autoCloseTime,
      autoCloseType: model.autoCloseType,
      isAllowed: model.isAllowed,
    );
  }

  _i31.TicketOnHoldRequestEntity?
      _map__i32$TicketOnHoldRequestResponse_To__i31$TicketOnHoldRequestEntity_Nullable(
          _i32.TicketOnHoldRequestResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i31.TicketOnHoldRequestEntity(
      id: model.id,
      nodeId: model.nodeId,
      ticketId: model.ticketId,
      workspaceId: model.workspaceId,
      requesterId: model.requesterId,
      reason: model.reason,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  _i33.TicketReopenRequestEntity?
      _map__i34$TicketReopenRequestResponse_To__i33$TicketReopenRequestEntity_Nullable(
          _i34.TicketReopenRequestResponse? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i33.TicketReopenRequestEntity(reason: model.reason);
  }

  _i53.AssigneeEntity? _map__i51$Assignee_To__i53$AssigneeEntity_Nullable(
      _i51.Assignee? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i53.AssigneeEntity(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i51$Info_To__i55$InfoEntity_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i55.InfoEntity? _map__i51$Info_To__i55$InfoEntity_Nullable(
      _i51.Info? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i55.InfoEntity(
        work: model.work
            ?.map<_i54.WorkEntity>(
                (value) => _map__i51$Work_To__i54$WorkEntity(value))
            .toList());
  }

  _i51.Assignee? _map__i53$AssigneeEntity_To__i51$Assignee_Nullable(
      _i53.AssigneeEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i51.Assignee(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i55$InfoEntity_To__i51$Info_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i51.Info? _map__i55$InfoEntity_To__i51$Info_Nullable(
      _i55.InfoEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i51.Info(
        work: model.work
            ?.map<_i51.Work>(
                (value) => _map__i54$WorkEntity_To__i51$Work(value))
            .toList());
  }

  _i67.GpAttachmentFileUrlEntity?
      _map__i66$UploadFileURLResponseModel_To__i67$GpAttachmentFileUrlEntity_Nullable(
          _i66.UploadFileURLResponseModel? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i67.GpAttachmentFileUrlEntity(
      src: model.src,
      store: model.store,
    );
  }

  _i66.UploadFileURLResponseModel?
      _map__i67$GpAttachmentFileUrlEntity_To__i66$UploadFileURLResponseModel_Nullable(
          _i67.GpAttachmentFileUrlEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i66.UploadFileURLResponseModel(
      store: model.store,
      src: model.src,
    );
  }
}
