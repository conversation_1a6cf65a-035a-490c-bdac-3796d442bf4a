import 'package:gp_core/core.dart';
import 'package:gp_shared/domain/entity/entity.dart';

/// Enum representing the add follower options
enum AddFollowerOption {
  /// Add to current step only
  currentStep,

  /// Add to all steps
  allSteps,
}

extension AddFollowerOptionExt on AddFollowerOption {
  /// Display name for the option
  String get displayName {
    switch (this) {
      case AddFollowerOption.currentStep:
        return LocaleKeys.ticket_details_add_follower_current_step_title.tr;
      case AddFollowerOption.allSteps:
        return LocaleKeys.ticket_details_add_follower_all_steps_title.tr;
    }
  }

  /// Description for the option
  String get description {
    switch (this) {
      case AddFollowerOption.currentStep:
        return LocaleKeys
            .ticket_details_add_follower_current_step_description.tr;
      case AddFollowerOption.allSteps:
        return LocaleKeys.ticket_details_add_follower_all_steps_description.tr;
    }
  }

  /// Icon asset for the option
  String get iconAsset {
    switch (this) {
      case AddFollowerOption.currentStep:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_ADD_SVG;
      case AddFollowerOption.allSteps:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_FOLDER_TREE_SVG;
    }
  }
}
