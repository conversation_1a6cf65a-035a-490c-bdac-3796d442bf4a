// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'ticket_follow_members_params.freezed.dart';
part 'ticket_follow_members_params.g.dart';

@Freezed(
  fromJson: false,
  toJson: true,
  copyWith: true,
)
abstract class TicketFollowMembersParams with _$TicketFollowMembersParams {
  const factory TicketFollowMembersParams({
    @JsonKey(name: 'user_ids') List<String>? userIds,
    @Json<PERSON>ey(name: 'collab_ids') List<String>? collabIds,
    @JsonKey(name: 'department_ids') List<String>? departmentIds,
    @Json<PERSON>ey(name: 'role_ids') List<String>? roleIds,
  }) = _TicketFollowMembersParams;
}
