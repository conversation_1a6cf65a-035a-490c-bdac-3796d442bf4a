// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_edit_comments_request_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketEditCommentsRequestParams {
  @JsonKey(name: 'medias')
  List<Medias>? get medias;
  @JsonKey(name: 'text')
  String? get text;
  @JsonKey(name: 'mentions')
  List<Mentions>? get mentions;

  /// Serializes this TicketEditCommentsRequestParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketEditCommentsRequestParams &&
            const DeepCollectionEquality().equals(other.medias, medias) &&
            (identical(other.text, text) || other.text == text) &&
            const DeepCollectionEquality().equals(other.mentions, mentions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(medias),
      text,
      const DeepCollectionEquality().hash(mentions));

  @override
  String toString() {
    return 'TicketEditCommentsRequestParams(medias: $medias, text: $text, mentions: $mentions)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketEditCommentsRequestParams
    implements TicketEditCommentsRequestParams {
  _TicketEditCommentsRequestParams(
      {@JsonKey(name: 'medias') final List<Medias>? medias = const [],
      @JsonKey(name: 'text') this.text,
      @JsonKey(name: 'mentions') final List<Mentions>? mentions = const []})
      : _medias = medias,
        _mentions = mentions;

  final List<Medias>? _medias;
  @override
  @JsonKey(name: 'medias')
  List<Medias>? get medias {
    final value = _medias;
    if (value == null) return null;
    if (_medias is EqualUnmodifiableListView) return _medias;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'text')
  final String? text;
  final List<Mentions>? _mentions;
  @override
  @JsonKey(name: 'mentions')
  List<Mentions>? get mentions {
    final value = _mentions;
    if (value == null) return null;
    if (_mentions is EqualUnmodifiableListView) return _mentions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TicketEditCommentsRequestParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketEditCommentsRequestParams &&
            const DeepCollectionEquality().equals(other._medias, _medias) &&
            (identical(other.text, text) || other.text == text) &&
            const DeepCollectionEquality().equals(other._mentions, _mentions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_medias),
      text,
      const DeepCollectionEquality().hash(_mentions));

  @override
  String toString() {
    return 'TicketEditCommentsRequestParams(medias: $medias, text: $text, mentions: $mentions)';
  }
}

// dart format on
