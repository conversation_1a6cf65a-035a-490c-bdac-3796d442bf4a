// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_create_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketCreateParams {
  String get title; //
  @Json<PERSON>ey(name: 'priority')
  int get ticketPriority; //
  @JsonKey(name: 'is_private')
  bool get isPrivatePrivacy; //
  @JsonKey(name: 'workflow_id')
  int get workflowId; //
  @JsonKey(name: 'field_values')
  List<dynamic>? get fieldValues;
  @JsonKey(name: 'ref_ticket_ids')
  List<int>? get refTicketIds;

  /// Serializes this TicketCreateParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketCreateParams &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.ticketPriority, ticketPriority) ||
                other.ticketPriority == ticketPriority) &&
            (identical(other.isPrivatePrivacy, isPrivatePrivacy) ||
                other.isPrivatePrivacy == isPrivatePrivacy) &&
            (identical(other.workflowId, workflowId) ||
                other.workflowId == workflowId) &&
            const DeepCollectionEquality()
                .equals(other.fieldValues, fieldValues) &&
            const DeepCollectionEquality()
                .equals(other.refTicketIds, refTicketIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      title,
      ticketPriority,
      isPrivatePrivacy,
      workflowId,
      const DeepCollectionEquality().hash(fieldValues),
      const DeepCollectionEquality().hash(refTicketIds));

  @override
  String toString() {
    return 'TicketCreateParams(title: $title, ticketPriority: $ticketPriority, isPrivatePrivacy: $isPrivatePrivacy, workflowId: $workflowId, fieldValues: $fieldValues, refTicketIds: $refTicketIds)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketCreateParams implements TicketCreateParams {
  _TicketCreateParams(
      {required this.title,
      @JsonKey(name: 'priority') required this.ticketPriority,
      @JsonKey(name: 'is_private') required this.isPrivatePrivacy,
      @JsonKey(name: 'workflow_id') required this.workflowId,
      @JsonKey(name: 'field_values') final List<dynamic>? fieldValues,
      @JsonKey(name: 'ref_ticket_ids') final List<int>? refTicketIds})
      : _fieldValues = fieldValues,
        _refTicketIds = refTicketIds;

  @override
  final String title;
//
  @override
  @JsonKey(name: 'priority')
  final int ticketPriority;
//
  @override
  @JsonKey(name: 'is_private')
  final bool isPrivatePrivacy;
//
  @override
  @JsonKey(name: 'workflow_id')
  final int workflowId;
//
  final List<dynamic>? _fieldValues;
//
  @override
  @JsonKey(name: 'field_values')
  List<dynamic>? get fieldValues {
    final value = _fieldValues;
    if (value == null) return null;
    if (_fieldValues is EqualUnmodifiableListView) return _fieldValues;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _refTicketIds;
  @override
  @JsonKey(name: 'ref_ticket_ids')
  List<int>? get refTicketIds {
    final value = _refTicketIds;
    if (value == null) return null;
    if (_refTicketIds is EqualUnmodifiableListView) return _refTicketIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TicketCreateParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketCreateParams &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.ticketPriority, ticketPriority) ||
                other.ticketPriority == ticketPriority) &&
            (identical(other.isPrivatePrivacy, isPrivatePrivacy) ||
                other.isPrivatePrivacy == isPrivatePrivacy) &&
            (identical(other.workflowId, workflowId) ||
                other.workflowId == workflowId) &&
            const DeepCollectionEquality()
                .equals(other._fieldValues, _fieldValues) &&
            const DeepCollectionEquality()
                .equals(other._refTicketIds, _refTicketIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      title,
      ticketPriority,
      isPrivatePrivacy,
      workflowId,
      const DeepCollectionEquality().hash(_fieldValues),
      const DeepCollectionEquality().hash(_refTicketIds));

  @override
  String toString() {
    return 'TicketCreateParams(title: $title, ticketPriority: $ticketPriority, isPrivatePrivacy: $isPrivatePrivacy, workflowId: $workflowId, fieldValues: $fieldValues, refTicketIds: $refTicketIds)';
  }
}

// dart format on
