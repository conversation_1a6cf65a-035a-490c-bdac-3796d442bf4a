// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoMapprGenerator
// **************************************************************************

// ignore_for_file: type=lint, unnecessary_cast, unused_local_variable

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i1;
import 'package:gp_core/models/assignee.dart' as _i2;
import 'package:gp_core/models/bot/bot_response.dart' as _i13;
import 'package:gp_core/models/conversation.dart' as _i7;
import 'package:gp_core/models/orgchat/organization_department.dart' as _i9;
import 'package:gp_core/models/orgchat/organization_role.dart' as _i11;
import 'package:gp_core/models/orgchat/select_invitees_model.dart' as _i15;

import '../../domain/entity/assignee/assignee.entity.dart' as _i4;
import '../../domain/entity/assignee/assignee_info.entity.dart' as _i6;
import '../../domain/entity/assignee/assignee_work.entity.dart' as _i5;
import '../../domain/entity/chatbot/chatbot.entity.dart' as _i14;
import '../../domain/entity/conversation/conversation.entity.dart' as _i8;
import '../../domain/entity/department/department.entity.dart' as _i10;
import '../../domain/entity/role/role.entity.dart' as _i12;
import '../../domain/entity/select_invite_options.entity.dart' as _i16;
import '../../domain/entity/user/gp_user.entity.dart' as _i3;
import 'assignee_entity_mapper.dart' as _i17;

/// Available mappings:
/// - `Assignee` → `GPUserEntity`.
/// - `AssigneeEntity` → `GPUserEntity`.
/// - `GPUserEntity` → `AssigneeEntity`.
/// - `Assignee` → `AssigneeEntity`.
/// - `Work` → `WorkEntity`.
/// - `Info` → `InfoEntity`.
/// - `Conversation` → `ConversationEntity`.
/// - `OrganizationDepartment` → `OrganizationDepartmentEntity`.
/// - `OrganizationRole` → `OrganizationRoleEntity`.
/// - `ChatBotModel` → `ChatBotEntity`.
/// - `SelectInviteesOptions` → `SelectMemberEntity`.
/// - `AssigneeEntity` → `Assignee`.
/// - `WorkEntity` → `Work`.
/// - `InfoEntity` → `Info`.
/// - `ConversationEntity` → `Conversation`.
/// - `OrganizationRoleEntity` → `OrganizationRole`.
/// - `OrganizationDepartmentEntity` → `OrganizationDepartment`.
/// - `ChatBotEntity` → `ChatBotModel`.
class $AssigneeEntityMapper implements _i1.AutoMapprInterface {
  const $AssigneeEntityMapper();

  Type _typeOf<T>() => T;

  List<_i1.AutoMapprInterface> get _delegates => const [];

  
  
  @override
  bool canConvert<SOURCE, TARGET>({bool recursive = true}) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.Assignee>() ||
            sourceTypeOf == _typeOf<_i2.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i3.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i3.GPUserEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i4.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i3.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i3.GPUserEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i3.GPUserEntity>() ||
            sourceTypeOf == _typeOf<_i3.GPUserEntity?>()) &&
        (targetTypeOf == _typeOf<_i4.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i4.AssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i2.Assignee>() ||
            sourceTypeOf == _typeOf<_i2.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i4.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i4.AssigneeEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i2.Work>() ||
            sourceTypeOf == _typeOf<_i2.Work?>()) &&
        (targetTypeOf == _typeOf<_i5.WorkEntity>() ||
            targetTypeOf == _typeOf<_i5.WorkEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i2.Info>() ||
            sourceTypeOf == _typeOf<_i2.Info?>()) &&
        (targetTypeOf == _typeOf<_i6.InfoEntity>() ||
            targetTypeOf == _typeOf<_i6.InfoEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i7.Conversation>() ||
            sourceTypeOf == _typeOf<_i7.Conversation?>()) &&
        (targetTypeOf == _typeOf<_i8.ConversationEntity>() ||
            targetTypeOf == _typeOf<_i8.ConversationEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i9.OrganizationDepartment>() ||
            sourceTypeOf == _typeOf<_i9.OrganizationDepartment?>()) &&
        (targetTypeOf == _typeOf<_i10.OrganizationDepartmentEntity>() ||
            targetTypeOf == _typeOf<_i10.OrganizationDepartmentEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i11.OrganizationRole>() ||
            sourceTypeOf == _typeOf<_i11.OrganizationRole?>()) &&
        (targetTypeOf == _typeOf<_i12.OrganizationRoleEntity>() ||
            targetTypeOf == _typeOf<_i12.OrganizationRoleEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i13.ChatBotModel>() ||
            sourceTypeOf == _typeOf<_i13.ChatBotModel?>()) &&
        (targetTypeOf == _typeOf<_i14.ChatBotEntity>() ||
            targetTypeOf == _typeOf<_i14.ChatBotEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i15.SelectInviteesOptions>() ||
            sourceTypeOf == _typeOf<_i15.SelectInviteesOptions?>()) &&
        (targetTypeOf == _typeOf<_i16.SelectMemberEntity>() ||
            targetTypeOf == _typeOf<_i16.SelectMemberEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i4.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.Assignee>() ||
            targetTypeOf == _typeOf<_i2.Assignee?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i5.WorkEntity>() ||
            sourceTypeOf == _typeOf<_i5.WorkEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.Work>() ||
            targetTypeOf == _typeOf<_i2.Work?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i6.InfoEntity>() ||
            sourceTypeOf == _typeOf<_i6.InfoEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.Info>() ||
            targetTypeOf == _typeOf<_i2.Info?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i8.ConversationEntity>() ||
            sourceTypeOf == _typeOf<_i8.ConversationEntity?>()) &&
        (targetTypeOf == _typeOf<_i7.Conversation>() ||
            targetTypeOf == _typeOf<_i7.Conversation?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i12.OrganizationRoleEntity>() ||
            sourceTypeOf == _typeOf<_i12.OrganizationRoleEntity?>()) &&
        (targetTypeOf == _typeOf<_i11.OrganizationRole>() ||
            targetTypeOf == _typeOf<_i11.OrganizationRole?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i10.OrganizationDepartmentEntity>() ||
            sourceTypeOf == _typeOf<_i10.OrganizationDepartmentEntity?>()) &&
        (targetTypeOf == _typeOf<_i9.OrganizationDepartment>() ||
            targetTypeOf == _typeOf<_i9.OrganizationDepartment?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i14.ChatBotEntity>() ||
            sourceTypeOf == _typeOf<_i14.ChatBotEntity?>()) &&
        (targetTypeOf == _typeOf<_i13.ChatBotModel>() ||
            targetTypeOf == _typeOf<_i13.ChatBotModel?>())) {
      return true;
    }
    if (recursive) {
      for (final mappr in _delegates) {
        if (mappr.canConvert<SOURCE, TARGET>()) {
          return true;
        }
      }
    }
    return false;
  }

  
  
  @override
  TARGET convert<SOURCE, TARGET>(SOURCE? model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _convert(model)!;
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convert(model)!;
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  TARGET? tryConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _safeConvert(
        model,
        onMappingError: onMappingError,
      );
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvert(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    return null;
  }

  
  
  @override
  Iterable<TARGET> convertIterable<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET>((item) => _convert(item)!);
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertIterable(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Iterable<TARGET?> tryConvertIterable<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET?>(
          (item) => _safeConvert(item, onMappingError: onMappingError));
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertIterable(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  List<TARGET> convertList<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertList(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  List<TARGET?> tryConvertList<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertList(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  Set<TARGET> convertSet<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertSet(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Set<TARGET?> tryConvertSet<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertSet(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  TARGET? _convert<SOURCE, TARGET>(
    SOURCE? model, {
    bool canReturnNull = false,
  }) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.Assignee>() ||
            sourceTypeOf == _typeOf<_i2.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i3.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i3.GPUserEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$Assignee_To__i3$GPUserEntity((model as _i2.Assignee?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i4.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i3.GPUserEntity>() ||
            targetTypeOf == _typeOf<_i3.GPUserEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$AssigneeEntity_To__i3$GPUserEntity(
          (model as _i4.AssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i3.GPUserEntity>() ||
            sourceTypeOf == _typeOf<_i3.GPUserEntity?>()) &&
        (targetTypeOf == _typeOf<_i4.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i4.AssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i3$GPUserEntity_To__i4$AssigneeEntity(
          (model as _i3.GPUserEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i2.Assignee>() ||
            sourceTypeOf == _typeOf<_i2.Assignee?>()) &&
        (targetTypeOf == _typeOf<_i4.AssigneeEntity>() ||
            targetTypeOf == _typeOf<_i4.AssigneeEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$Assignee_To__i4$AssigneeEntity((model as _i2.Assignee?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i2.Work>() ||
            sourceTypeOf == _typeOf<_i2.Work?>()) &&
        (targetTypeOf == _typeOf<_i5.WorkEntity>() ||
            targetTypeOf == _typeOf<_i5.WorkEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$Work_To__i5$WorkEntity((model as _i2.Work?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i2.Info>() ||
            sourceTypeOf == _typeOf<_i2.Info?>()) &&
        (targetTypeOf == _typeOf<_i6.InfoEntity>() ||
            targetTypeOf == _typeOf<_i6.InfoEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$Info_To__i6$InfoEntity((model as _i2.Info?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i7.Conversation>() ||
            sourceTypeOf == _typeOf<_i7.Conversation?>()) &&
        (targetTypeOf == _typeOf<_i8.ConversationEntity>() ||
            targetTypeOf == _typeOf<_i8.ConversationEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i7$Conversation_To__i8$ConversationEntity(
          (model as _i7.Conversation?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i9.OrganizationDepartment>() ||
            sourceTypeOf == _typeOf<_i9.OrganizationDepartment?>()) &&
        (targetTypeOf == _typeOf<_i10.OrganizationDepartmentEntity>() ||
            targetTypeOf == _typeOf<_i10.OrganizationDepartmentEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i9$OrganizationDepartment_To__i10$OrganizationDepartmentEntity(
          (model as _i9.OrganizationDepartment?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i11.OrganizationRole>() ||
            sourceTypeOf == _typeOf<_i11.OrganizationRole?>()) &&
        (targetTypeOf == _typeOf<_i12.OrganizationRoleEntity>() ||
            targetTypeOf == _typeOf<_i12.OrganizationRoleEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i11$OrganizationRole_To__i12$OrganizationRoleEntity(
          (model as _i11.OrganizationRole?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i13.ChatBotModel>() ||
            sourceTypeOf == _typeOf<_i13.ChatBotModel?>()) &&
        (targetTypeOf == _typeOf<_i14.ChatBotEntity>() ||
            targetTypeOf == _typeOf<_i14.ChatBotEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i13$ChatBotModel_To__i14$ChatBotEntity(
          (model as _i13.ChatBotModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i15.SelectInviteesOptions>() ||
            sourceTypeOf == _typeOf<_i15.SelectInviteesOptions?>()) &&
        (targetTypeOf == _typeOf<_i16.SelectMemberEntity>() ||
            targetTypeOf == _typeOf<_i16.SelectMemberEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i15$SelectInviteesOptions_To__i16$SelectMemberEntity(
          (model as _i15.SelectInviteesOptions?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.AssigneeEntity>() ||
            sourceTypeOf == _typeOf<_i4.AssigneeEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.Assignee>() ||
            targetTypeOf == _typeOf<_i2.Assignee?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$AssigneeEntity_To__i2$Assignee(
          (model as _i4.AssigneeEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i5.WorkEntity>() ||
            sourceTypeOf == _typeOf<_i5.WorkEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.Work>() ||
            targetTypeOf == _typeOf<_i2.Work?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i5$WorkEntity_To__i2$Work((model as _i5.WorkEntity?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i6.InfoEntity>() ||
            sourceTypeOf == _typeOf<_i6.InfoEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.Info>() ||
            targetTypeOf == _typeOf<_i2.Info?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i6$InfoEntity_To__i2$Info((model as _i6.InfoEntity?))
          as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i8.ConversationEntity>() ||
            sourceTypeOf == _typeOf<_i8.ConversationEntity?>()) &&
        (targetTypeOf == _typeOf<_i7.Conversation>() ||
            targetTypeOf == _typeOf<_i7.Conversation?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i8$ConversationEntity_To__i7$Conversation(
          (model as _i8.ConversationEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i12.OrganizationRoleEntity>() ||
            sourceTypeOf == _typeOf<_i12.OrganizationRoleEntity?>()) &&
        (targetTypeOf == _typeOf<_i11.OrganizationRole>() ||
            targetTypeOf == _typeOf<_i11.OrganizationRole?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i12$OrganizationRoleEntity_To__i11$OrganizationRole(
          (model as _i12.OrganizationRoleEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i10.OrganizationDepartmentEntity>() ||
            sourceTypeOf == _typeOf<_i10.OrganizationDepartmentEntity?>()) &&
        (targetTypeOf == _typeOf<_i9.OrganizationDepartment>() ||
            targetTypeOf == _typeOf<_i9.OrganizationDepartment?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i10$OrganizationDepartmentEntity_To__i9$OrganizationDepartment(
          (model as _i10.OrganizationDepartmentEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i14.ChatBotEntity>() ||
            sourceTypeOf == _typeOf<_i14.ChatBotEntity?>()) &&
        (targetTypeOf == _typeOf<_i13.ChatBotModel>() ||
            targetTypeOf == _typeOf<_i13.ChatBotModel?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i14$ChatBotEntity_To__i13$ChatBotModel(
          (model as _i14.ChatBotEntity?)) as TARGET);
    }
    throw Exception('No ${model.runtimeType} -> $targetTypeOf mapping.');
  }

  TARGET? _safeConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (!useSafeMapping<SOURCE, TARGET>()) {
      return _convert(
        model,
        canReturnNull: true,
      );
    }
    try {
      return _convert(
        model,
        canReturnNull: true,
      );
    } catch (e, s) {
      onMappingError?.call(e, s, model);
      return null;
    }
  }

  
  
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return false;
  }

  _i3.GPUserEntity _map__i2$Assignee_To__i3$GPUserEntity(_i2.Assignee? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Assignee → GPUserEntity failed because Assignee was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Assignee, GPUserEntity> to handle null values during mapping.');
    }
    return _i3.GPUserEntity(
      id: _i17.AssigneeEntityMapper.mapIntIdToString(model),
      name: model.displayName,
      avatar: model.avatar,
      displayName: model.displayName,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i3.GPUserEntity _map__i4$AssigneeEntity_To__i3$GPUserEntity(
      _i4.AssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AssigneeEntity → GPUserEntity failed because AssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AssigneeEntity, GPUserEntity> to handle null values during mapping.');
    }
    return _i3.GPUserEntity(
      id: _i17.AssigneeEntityMapper.mapAssigneeEntityId(model),
      avatar: model.avatar,
      displayName: model.displayName,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i4.AssigneeEntity _map__i3$GPUserEntity_To__i4$AssigneeEntity(
      _i3.GPUserEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GPUserEntity → AssigneeEntity failed because GPUserEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GPUserEntity, AssigneeEntity> to handle null values during mapping.');
    }
    return _i4.AssigneeEntity(
      id: _i17.AssigneeEntityMapper.mapGPUserEntityId(model),
      displayName: _i17.AssigneeEntityMapper.mapGPUserEntityDisplayName(model),
      avatar: model.avatar,
      avatarThumbPattern: model.avatarThumbPattern,
    );
  }

  _i4.AssigneeEntity _map__i2$Assignee_To__i4$AssigneeEntity(
      _i2.Assignee? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Assignee → AssigneeEntity failed because Assignee was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Assignee, AssigneeEntity> to handle null values during mapping.');
    }
    return _i4.AssigneeEntity(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i2$Info_To__i6$InfoEntity_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i5.WorkEntity _map__i2$Work_To__i5$WorkEntity(_i2.Work? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Work → WorkEntity failed because Work was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Work, WorkEntity> to handle null values during mapping.');
    }
    return _i5.WorkEntity(
      company: model.company,
      department: model.department,
      title: model.title,
      departmentId: model.departmentId,
      departments: model.departments,
      departmentIds: model.departmentIds,
      roleId: model.roleId,
      privacy: model.privacy,
    );
  }

  _i6.InfoEntity _map__i2$Info_To__i6$InfoEntity(_i2.Info? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Info → InfoEntity failed because Info was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Info, InfoEntity> to handle null values during mapping.');
    }
    return _i6.InfoEntity(
        work: model.work
            ?.map<_i5.WorkEntity>(
                (value) => _map__i2$Work_To__i5$WorkEntity(value))
            .toList());
  }

  _i8.ConversationEntity _map__i7$Conversation_To__i8$ConversationEntity(
      _i7.Conversation? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping Conversation → ConversationEntity failed because Conversation was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<Conversation, ConversationEntity> to handle null values during mapping.');
    }
    return _i8.ConversationEntity(
      id: model.id,
      name: model.name,
      role: model.role,
      avatar: model.avatar,
      folder: model.folder,
      type: model.type,
      groupLevel: model.groupLevel,
      messageCount: model.messageCount,
      memberCount: model.memberCount,
    );
  }

  _i10.OrganizationDepartmentEntity
      _map__i9$OrganizationDepartment_To__i10$OrganizationDepartmentEntity(
          _i9.OrganizationDepartment? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationDepartment → OrganizationDepartmentEntity failed because OrganizationDepartment was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationDepartment, OrganizationDepartmentEntity> to handle null values during mapping.');
    }
    return _i10.OrganizationDepartmentEntity(
      id: model.id,
      name: model.name,
      children: model.children
          .map<_i10.OrganizationDepartmentEntity>((value) =>
              _map__i9$OrganizationDepartment_To__i10$OrganizationDepartmentEntity(
                  value))
          .toList(),
      groupId: model.groupId,
      treeId: model.treeId,
      threadId: model.threadId,
      isPrimary: model.isPrimary,
    );
  }

  _i12.OrganizationRoleEntity
      _map__i11$OrganizationRole_To__i12$OrganizationRoleEntity(
          _i11.OrganizationRole? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationRole → OrganizationRoleEntity failed because OrganizationRole was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationRole, OrganizationRoleEntity> to handle null values during mapping.');
    }
    return _i12.OrganizationRoleEntity(
      name: model.name,
      workspaceId: model.workspaceId,
      id: model.id,
    );
  }

  _i14.ChatBotEntity _map__i13$ChatBotModel_To__i14$ChatBotEntity(
      _i13.ChatBotModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ChatBotModel → ChatBotEntity failed because ChatBotModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ChatBotModel, ChatBotEntity> to handle null values during mapping.');
    }
    return _i14.ChatBotEntity(
      id: model.id,
      name: model.name,
      avatar: model.avatar,
      description: model.description,
      workspaceId: model.workspaceId,
      creatorId: model.creatorId,
      createdAt: model.createdAt,
      botCreatorType: model.botCreatorType,
    );
  }

  _i16.SelectMemberEntity
      _map__i15$SelectInviteesOptions_To__i16$SelectMemberEntity(
          _i15.SelectInviteesOptions? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping SelectInviteesOptions → SelectMemberEntity failed because SelectInviteesOptions was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<SelectInviteesOptions, SelectMemberEntity> to handle null values during mapping.');
    }
    return _i16.SelectMemberEntity(
      assigneeEntities: model.selectedMembers
          ?.map<_i4.AssigneeEntity>(
              (value) => _map__i2$Assignee_To__i4$AssigneeEntity(value))
          .toList(),
      conversationEntities: model.selectedThreads
          ?.map<_i8.ConversationEntity>(
              (value) => _map__i7$Conversation_To__i8$ConversationEntity(value))
          .toList(),
      departmentEntities: model.selectedDepartments
          ?.map<_i10.OrganizationDepartmentEntity>((value) =>
              _map__i9$OrganizationDepartment_To__i10$OrganizationDepartmentEntity(
                  value))
          .toList(),
      roleEntities: model.selectedRoles
          ?.map<_i12.OrganizationRoleEntity>((value) =>
              _map__i11$OrganizationRole_To__i12$OrganizationRoleEntity(value))
          .toList(),
      chatBotEntities: model.selectedBots
          ?.map<_i14.ChatBotEntity>(
              (value) => _map__i13$ChatBotModel_To__i14$ChatBotEntity(value))
          .toList(),
    );
  }

  _i2.Assignee _map__i4$AssigneeEntity_To__i2$Assignee(
      _i4.AssigneeEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping AssigneeEntity → Assignee failed because AssigneeEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<AssigneeEntity, Assignee> to handle null values during mapping.');
    }
    return _i2.Assignee(
      id: model.id,
      displayName: model.displayName,
      lang: model.lang,
      fullName: model.fullName,
      cover: model.cover,
      avatar: model.avatar,
      email: model.email,
      linkProfile: model.linkProfile,
      info: _map__i6$InfoEntity_To__i2$Info_Nullable(model.info),
      workspaceAccount: model.workspaceAccount,
      workspaceId: model.workspaceId,
      phoneNumber: model.phoneNumber,
      avatarThumbPattern: model.avatarThumbPattern,
      coverThumbPattern: model.coverThumbPattern,
      userDepartment: model.userDepartment,
      userRole: model.userRole,
    );
  }

  _i2.Work _map__i5$WorkEntity_To__i2$Work(_i5.WorkEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping WorkEntity → Work failed because WorkEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<WorkEntity, Work> to handle null values during mapping.');
    }
    return _i2.Work(
      company: model.company,
      department: model.department,
      title: model.title,
      departmentId: model.departmentId,
      departments: model.departments,
      departmentIds: model.departmentIds,
      roleId: model.roleId,
      privacy: model.privacy,
    );
  }

  _i2.Info _map__i6$InfoEntity_To__i2$Info(_i6.InfoEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping InfoEntity → Info failed because InfoEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<InfoEntity, Info> to handle null values during mapping.');
    }
    return _i2.Info(
        work: model.work
            ?.map<_i2.Work>((value) => _map__i5$WorkEntity_To__i2$Work(value))
            .toList());
  }

  _i7.Conversation _map__i8$ConversationEntity_To__i7$Conversation(
      _i8.ConversationEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ConversationEntity → Conversation failed because ConversationEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ConversationEntity, Conversation> to handle null values during mapping.');
    }
    return _i7.Conversation(
      name: model.name,
      role: model.role,
      avatar: model.avatar,
      folder: model.folder,
      type: model.type,
      id: model.id,
      groupLevel: model.groupLevel,
      memberCount: model.memberCount,
    )..messageCount = model.messageCount;
  }

  _i11.OrganizationRole
      _map__i12$OrganizationRoleEntity_To__i11$OrganizationRole(
          _i12.OrganizationRoleEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationRoleEntity → OrganizationRole failed because OrganizationRoleEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationRoleEntity, OrganizationRole> to handle null values during mapping.');
    }
    return _i11.OrganizationRole(
      name: model.name,
      workspaceId: model.workspaceId,
      id: model.id,
    );
  }

  _i9.OrganizationDepartment
      _map__i10$OrganizationDepartmentEntity_To__i9$OrganizationDepartment(
          _i10.OrganizationDepartmentEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping OrganizationDepartmentEntity → OrganizationDepartment failed because OrganizationDepartmentEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<OrganizationDepartmentEntity, OrganizationDepartment> to handle null values during mapping.');
    }
    return _i9.OrganizationDepartment(
      id: model.id,
      name: model.name,
      children: model.children
          .map<_i9.OrganizationDepartment>((value) =>
              _map__i10$OrganizationDepartmentEntity_To__i9$OrganizationDepartment(
                  value))
          .toList(),
      isPrimary: model.isPrimary,
      threadId: model.threadId,
      treeId: model.treeId,
    )..groupId = model.groupId;
  }

  _i13.ChatBotModel _map__i14$ChatBotEntity_To__i13$ChatBotModel(
      _i14.ChatBotEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping ChatBotEntity → ChatBotModel failed because ChatBotEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<ChatBotEntity, ChatBotModel> to handle null values during mapping.');
    }
    return _i13.ChatBotModel(
      name: _i17.AssigneeEntityMapper.mapChatBotEntityName(model),
      id: _i17.AssigneeEntityMapper.mapChatBotEntityId(model),
      avatar: model.avatar,
      description: model.description,
      workspaceId: model.workspaceId,
      creatorId: model.creatorId,
      createdAt: model.createdAt,
      botCreatorType: model.botCreatorType,
    );
  }

  _i6.InfoEntity? _map__i2$Info_To__i6$InfoEntity_Nullable(_i2.Info? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i6.InfoEntity(
        work: model.work
            ?.map<_i5.WorkEntity>(
                (value) => _map__i2$Work_To__i5$WorkEntity(value))
            .toList());
  }

  _i2.Info? _map__i6$InfoEntity_To__i2$Info_Nullable(_i6.InfoEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i2.Info(
        work: model.work
            ?.map<_i2.Work>((value) => _map__i5$WorkEntity_To__i2$Work(value))
            .toList());
  }
}
