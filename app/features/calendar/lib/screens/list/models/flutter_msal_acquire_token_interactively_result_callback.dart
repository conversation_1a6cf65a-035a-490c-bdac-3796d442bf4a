// class FlutterMSALAcquireTokenInteractivelyResultCallback {
//   final String accessToken;
//   final String? username;
//   final DateTime? tokenExpireDate;
//   final String? id;

//   FlutterMSALAcquireTokenInteractivelyResultCallback({
//     required this.accessToken,
//     required this.tokenExpireDate,
//     required this.username,
//     required this.id,
//   });

//   factory FlutterMSALAcquireTokenInteractivelyResultCallback.fromJson(
//       Map json) {
//     return FlutterMSALAcquireTokenInteractivelyResultCallback(
//       accessToken: json['accessToken'],
//       tokenExpireDate: json['tokenExpireDate'] == null
//           ? null
//           : DateTime.fromMillisecondsSinceEpoch(
//               ((json['tokenExpireDate'] as num) * 1000).toInt()),
//       username: json['username'],
//       id: json['id'],
//     );
//   }
// }

// class FlutterMSALLoadCurrentAccountResultCallback {
//   final String? username;
//   FlutterMSALLoadCurrentAccountResultCallback(this.username);
//   factory FlutterMSALLoadCurrentAccountResultCallback.fromJson(Map json) {
//     return FlutterMSALLoadCurrentAccountResultCallback(json['username']);
//   }
// }
