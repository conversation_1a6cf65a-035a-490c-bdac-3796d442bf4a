// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_update_assignee.params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketUpdateAssigneeParams {
  @JsonKey(name: 'assignee_id')
  int get assigneeId;
  @JsonKey(name: 'current_assignee_id')
  int? get currentAssigneeId;

  /// Serializes this TicketUpdateAssigneeParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketUpdateAssigneeParams &&
            (identical(other.assigneeId, assigneeId) ||
                other.assigneeId == assigneeId) &&
            (identical(other.currentAssigneeId, currentAssigneeId) ||
                other.currentAssigneeId == currentAssigneeId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, assigneeId, currentAssigneeId);

  @override
  String toString() {
    return 'TicketUpdateAssigneeParams(assigneeId: $assigneeId, currentAssigneeId: $currentAssigneeId)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketUpdateAssigneeParams implements TicketUpdateAssigneeParams {
  _TicketUpdateAssigneeParams(
      {@JsonKey(name: 'assignee_id') required this.assigneeId,
      @JsonKey(name: 'current_assignee_id') this.currentAssigneeId});

  @override
  @JsonKey(name: 'assignee_id')
  final int assigneeId;
  @override
  @JsonKey(name: 'current_assignee_id')
  final int? currentAssigneeId;

  @override
  Map<String, dynamic> toJson() {
    return _$TicketUpdateAssigneeParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketUpdateAssigneeParams &&
            (identical(other.assigneeId, assigneeId) ||
                other.assigneeId == assigneeId) &&
            (identical(other.currentAssigneeId, currentAssigneeId) ||
                other.currentAssigneeId == currentAssigneeId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, assigneeId, currentAssigneeId);

  @override
  String toString() {
    return 'TicketUpdateAssigneeParams(assigneeId: $assigneeId, currentAssigneeId: $currentAssigneeId)';
  }
}

// dart format on
