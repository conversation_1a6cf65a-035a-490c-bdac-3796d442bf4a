import 'package:gp_core/core.dart';

import '../feature_flag.dart';
import 'jwt_token.dart';

final box = GetStorage();
final Map<ErrorInterceptorHandler, DioException> _tokenHandlers = {};

final List<_QueuedRequest> _queue = [];

final Dio _dio = GPDio.instance.setup();

void onRenewTokenSuccess() {
  _resumeRequests();
}

void onRenewTokenFailure() {
  for (var entry in _tokenHandlers.entries) {
    entry.key.reject(entry.value);
  }

  _tokenHandlers.clear();
}

Future<void> _resumeRequests() async {
  final queued = List<_QueuedRequest>.from(_queue);
  _queue.clear();

  final accessToken = await TokenManager.accessToken();

  logDebug('queued -> ${queued.length}');

  for (var req in queued) {
    try {
      final regOptions = req.options;
      if (!accessToken.startsWith('Bearer ')) {
        regOptions.headers['Authorization'] = 'Bearer ${accessToken.trim()}';
      }

      final response = await _dio.fetch(regOptions);
      req.handler.resolve(response);
    } catch (e) {
      req.handler.reject(DioException(
        error: e,
        requestOptions: req.options,
      ));
    }
  }
}

class _TokenHandler with TokenInterceptorMixin {
  void onError(
    Dio dio,
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    /// 30/06/2025 họp thống nhất lại flow renewToken: </br>
    /// Token, RefreshToken sinh ra unique trên từng device </br>
    /// Nên khi phía Flutter cần renewToken, cần gọi về native để tránh native bị force-logout:
    ///
    /// Flow:
    /// 1. Khi flutter có api trả về 401, flutter pause toàn bộ request lên BE, flutter gọi method channel `renewToken` sang native.
    /// 2. native xử lý renewToken.
    /// 3. native call method `updateTokenInfo` sang flutter. Các phần retry khi renewToken, native sẽ xử lý.
    /// 4. flutter xử lý token mới, tiếp tục xử lý requests.
    /// 5. Timeout từ step 1 cho tới step 3 là 10s, sau 10s flutter sẽ hiển thị lỗi tương ứng.
    final isSpecial401 = await isSpecial401Condition(err);
    if (err.response?.statusCode == 401 || isSpecial401) {
      _queue.add(_QueuedRequest(err.requestOptions, handler));

      // sync
      Deeplink.renewToken();
    } else {
      handler.next(err);
    }
  }
}

/// Renew token from native, via
class TokenInterceptor extends QueuedInterceptorsWrapper {
  TokenInterceptor(this.dio)
      : super(onError: (err, handler) {
          _tokenHandler.onError(dio, err, handler);
        });

  final Dio dio;

  static final _TokenHandler _tokenHandler = _TokenHandler();
}

final class _QueuedRequest {
  final RequestOptions options;
  final ErrorInterceptorHandler handler;

  _QueuedRequest(this.options, this.handler);
}

mixin TokenInterceptorMixin {
  Future<bool> isSpecial401Condition(DioException err) async {
    final requestUrl = Uri.parse(err.requestOptions.uri.toString());
    bool isInWhiteList =
        FeatureFlag.whiteListDownloadAuthDomains.contains(requestUrl.host);

    final bool isFromDownload =
        err.response?.statusCode == 403 && isInWhiteList;

    if (!isFromDownload) return false;

    final exp = await FlutterSessionJwt.getExpirationDateTime();
    return exp?.isBefore(DateTime.now()) ?? false;
  }
}
