// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_additional_requests_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketAdditionalRequestsParams {
  @JsonKey(name: 'content_need_add')
  String get content;

  ///
  @JsonKey(name: 'node_id')
  int get nodeId;

  ///
  @JsonKey(name: 'ticket_id')
  int get ticketId;

  ///
  @JsonKey(name: 'target_id')
  int get targetId;

  /// Create a copy of TicketAdditionalRequestsParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TicketAdditionalRequestsParamsCopyWith<TicketAdditionalRequestsParams>
      get copyWith => _$TicketAdditionalRequestsParamsCopyWithImpl<
              TicketAdditionalRequestsParams>(
          this as TicketAdditionalRequestsParams, _$identity);

  /// Serializes this TicketAdditionalRequestsParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketAdditionalRequestsParams &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.nodeId, nodeId) || other.nodeId == nodeId) &&
            (identical(other.ticketId, ticketId) ||
                other.ticketId == ticketId) &&
            (identical(other.targetId, targetId) ||
                other.targetId == targetId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, content, nodeId, ticketId, targetId);

  @override
  String toString() {
    return 'TicketAdditionalRequestsParams(content: $content, nodeId: $nodeId, ticketId: $ticketId, targetId: $targetId)';
  }
}

/// @nodoc
abstract mixin class $TicketAdditionalRequestsParamsCopyWith<$Res> {
  factory $TicketAdditionalRequestsParamsCopyWith(
          TicketAdditionalRequestsParams value,
          $Res Function(TicketAdditionalRequestsParams) _then) =
      _$TicketAdditionalRequestsParamsCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'content_need_add') String content,
      @JsonKey(name: 'node_id') int nodeId,
      @JsonKey(name: 'ticket_id') int ticketId,
      @JsonKey(name: 'target_id') int targetId});
}

/// @nodoc
class _$TicketAdditionalRequestsParamsCopyWithImpl<$Res>
    implements $TicketAdditionalRequestsParamsCopyWith<$Res> {
  _$TicketAdditionalRequestsParamsCopyWithImpl(this._self, this._then);

  final TicketAdditionalRequestsParams _self;
  final $Res Function(TicketAdditionalRequestsParams) _then;

  /// Create a copy of TicketAdditionalRequestsParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = null,
    Object? nodeId = null,
    Object? ticketId = null,
    Object? targetId = null,
  }) {
    return _then(_self.copyWith(
      content: null == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      nodeId: null == nodeId
          ? _self.nodeId
          : nodeId // ignore: cast_nullable_to_non_nullable
              as int,
      ticketId: null == ticketId
          ? _self.ticketId
          : ticketId // ignore: cast_nullable_to_non_nullable
              as int,
      targetId: null == targetId
          ? _self.targetId
          : targetId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketAdditionalRequestsParams
    implements TicketAdditionalRequestsParams {
  const _TicketAdditionalRequestsParams(
      {@JsonKey(name: 'content_need_add') required this.content,
      @JsonKey(name: 'node_id') required this.nodeId,
      @JsonKey(name: 'ticket_id') required this.ticketId,
      @JsonKey(name: 'target_id') required this.targetId});

  @override
  @JsonKey(name: 'content_need_add')
  final String content;

  ///
  @override
  @JsonKey(name: 'node_id')
  final int nodeId;

  ///
  @override
  @JsonKey(name: 'ticket_id')
  final int ticketId;

  ///
  @override
  @JsonKey(name: 'target_id')
  final int targetId;

  /// Create a copy of TicketAdditionalRequestsParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TicketAdditionalRequestsParamsCopyWith<_TicketAdditionalRequestsParams>
      get copyWith => __$TicketAdditionalRequestsParamsCopyWithImpl<
          _TicketAdditionalRequestsParams>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TicketAdditionalRequestsParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketAdditionalRequestsParams &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.nodeId, nodeId) || other.nodeId == nodeId) &&
            (identical(other.ticketId, ticketId) ||
                other.ticketId == ticketId) &&
            (identical(other.targetId, targetId) ||
                other.targetId == targetId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, content, nodeId, ticketId, targetId);

  @override
  String toString() {
    return 'TicketAdditionalRequestsParams(content: $content, nodeId: $nodeId, ticketId: $ticketId, targetId: $targetId)';
  }
}

/// @nodoc
abstract mixin class _$TicketAdditionalRequestsParamsCopyWith<$Res>
    implements $TicketAdditionalRequestsParamsCopyWith<$Res> {
  factory _$TicketAdditionalRequestsParamsCopyWith(
          _TicketAdditionalRequestsParams value,
          $Res Function(_TicketAdditionalRequestsParams) _then) =
      __$TicketAdditionalRequestsParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'content_need_add') String content,
      @JsonKey(name: 'node_id') int nodeId,
      @JsonKey(name: 'ticket_id') int ticketId,
      @JsonKey(name: 'target_id') int targetId});
}

/// @nodoc
class __$TicketAdditionalRequestsParamsCopyWithImpl<$Res>
    implements _$TicketAdditionalRequestsParamsCopyWith<$Res> {
  __$TicketAdditionalRequestsParamsCopyWithImpl(this._self, this._then);

  final _TicketAdditionalRequestsParams _self;
  final $Res Function(_TicketAdditionalRequestsParams) _then;

  /// Create a copy of TicketAdditionalRequestsParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? content = null,
    Object? nodeId = null,
    Object? ticketId = null,
    Object? targetId = null,
  }) {
    return _then(_TicketAdditionalRequestsParams(
      content: null == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      nodeId: null == nodeId
          ? _self.nodeId
          : nodeId // ignore: cast_nullable_to_non_nullable
              as int,
      ticketId: null == ticketId
          ? _self.ticketId
          : ticketId // ignore: cast_nullable_to_non_nullable
              as int,
      targetId: null == targetId
          ? _self.targetId
          : targetId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
