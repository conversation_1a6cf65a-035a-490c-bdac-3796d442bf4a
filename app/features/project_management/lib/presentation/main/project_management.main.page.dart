/*
 * Created Date: Monday, 14th July 2025, 08:50:11
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 15th July 2025 09:36:55
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../constants/constant.dart';
import 'project_management_controller.dart';

/// one instance for this page
final ProjectManagementController _projectManagementController =
    ProjectManagementController();

class ProjectManagementMainPage extends StatelessWidget {
  const ProjectManagementMainPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: GPColor.bgPrimary,
      child: Safe<PERSON>rea(
        child: WebViewWidget(
            controller: _projectManagementController.webviewController
              ..setJavaScriptMode(JavaScriptMode.unrestricted)
              ..setNavigationDelegate(
                NavigationDelegate(
                  onProgress: (int progress) {},
                  onPageStarted: (String url) {},
                  onPageFinished: (String url) async {
                    await _projectManagementController.injectTokenToWeb();
                  },
                  onHttpError: _projectManagementController.onHttpError,
                  onWebResourceError: (error) {},
                  onNavigationRequest: (NavigationRequest request) {
                    return NavigationDecision.navigate;
                  },
                ),
              )
              ..addJavaScriptChannel(
                GPWebConstants.kWebChannel,
                onMessageReceived:
                    _projectManagementController.onMessageReceived,
              )
              ..setOnConsoleMessage(
                _projectManagementController.onConsoleMessage,
              )
              ..loadRequest(
                  Uri.parse(_projectManagementController.getWebUrl()))),
      ),
    );
  }
}
