// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_response_additional_request.params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketResponseAdditionalRequestParams {
  @JsonKey(name: 'response_to_requester')
  String get responseToRequester;

  ///
  @JsonKey(name: 'attached_files')
  List<UploadFileResponseModelV2> get attachedFiles;

  /// Create a copy of TicketResponseAdditionalRequestParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TicketResponseAdditionalRequestParamsCopyWith<
          TicketResponseAdditionalRequestParams>
      get copyWith => _$TicketResponseAdditionalRequestParamsCopyWithImpl<
              TicketResponseAdditionalRequestParams>(
          this as TicketResponseAdditionalRequestParams, _$identity);

  /// Serializes this TicketResponseAdditionalRequestParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketResponseAdditionalRequestParams &&
            (identical(other.responseToRequester, responseToRequester) ||
                other.responseToRequester == responseToRequester) &&
            const DeepCollectionEquality()
                .equals(other.attachedFiles, attachedFiles));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, responseToRequester,
      const DeepCollectionEquality().hash(attachedFiles));

  @override
  String toString() {
    return 'TicketResponseAdditionalRequestParams(responseToRequester: $responseToRequester, attachedFiles: $attachedFiles)';
  }
}

/// @nodoc
abstract mixin class $TicketResponseAdditionalRequestParamsCopyWith<$Res> {
  factory $TicketResponseAdditionalRequestParamsCopyWith(
          TicketResponseAdditionalRequestParams value,
          $Res Function(TicketResponseAdditionalRequestParams) _then) =
      _$TicketResponseAdditionalRequestParamsCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'response_to_requester') String responseToRequester,
      @JsonKey(name: 'attached_files')
      List<UploadFileResponseModelV2> attachedFiles});
}

/// @nodoc
class _$TicketResponseAdditionalRequestParamsCopyWithImpl<$Res>
    implements $TicketResponseAdditionalRequestParamsCopyWith<$Res> {
  _$TicketResponseAdditionalRequestParamsCopyWithImpl(this._self, this._then);

  final TicketResponseAdditionalRequestParams _self;
  final $Res Function(TicketResponseAdditionalRequestParams) _then;

  /// Create a copy of TicketResponseAdditionalRequestParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? responseToRequester = null,
    Object? attachedFiles = null,
  }) {
    return _then(_self.copyWith(
      responseToRequester: null == responseToRequester
          ? _self.responseToRequester
          : responseToRequester // ignore: cast_nullable_to_non_nullable
              as String,
      attachedFiles: null == attachedFiles
          ? _self.attachedFiles
          : attachedFiles // ignore: cast_nullable_to_non_nullable
              as List<UploadFileResponseModelV2>,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketResponseAdditionalRequestParams
    implements TicketResponseAdditionalRequestParams {
  const _TicketResponseAdditionalRequestParams(
      {@JsonKey(name: 'response_to_requester')
      required this.responseToRequester,
      @JsonKey(name: 'attached_files')
      required final List<UploadFileResponseModelV2> attachedFiles})
      : _attachedFiles = attachedFiles;

  @override
  @JsonKey(name: 'response_to_requester')
  final String responseToRequester;

  ///
  final List<UploadFileResponseModelV2> _attachedFiles;

  ///
  @override
  @JsonKey(name: 'attached_files')
  List<UploadFileResponseModelV2> get attachedFiles {
    if (_attachedFiles is EqualUnmodifiableListView) return _attachedFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attachedFiles);
  }

  /// Create a copy of TicketResponseAdditionalRequestParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TicketResponseAdditionalRequestParamsCopyWith<
          _TicketResponseAdditionalRequestParams>
      get copyWith => __$TicketResponseAdditionalRequestParamsCopyWithImpl<
          _TicketResponseAdditionalRequestParams>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TicketResponseAdditionalRequestParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketResponseAdditionalRequestParams &&
            (identical(other.responseToRequester, responseToRequester) ||
                other.responseToRequester == responseToRequester) &&
            const DeepCollectionEquality()
                .equals(other._attachedFiles, _attachedFiles));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, responseToRequester,
      const DeepCollectionEquality().hash(_attachedFiles));

  @override
  String toString() {
    return 'TicketResponseAdditionalRequestParams(responseToRequester: $responseToRequester, attachedFiles: $attachedFiles)';
  }
}

/// @nodoc
abstract mixin class _$TicketResponseAdditionalRequestParamsCopyWith<$Res>
    implements $TicketResponseAdditionalRequestParamsCopyWith<$Res> {
  factory _$TicketResponseAdditionalRequestParamsCopyWith(
          _TicketResponseAdditionalRequestParams value,
          $Res Function(_TicketResponseAdditionalRequestParams) _then) =
      __$TicketResponseAdditionalRequestParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'response_to_requester') String responseToRequester,
      @JsonKey(name: 'attached_files')
      List<UploadFileResponseModelV2> attachedFiles});
}

/// @nodoc
class __$TicketResponseAdditionalRequestParamsCopyWithImpl<$Res>
    implements _$TicketResponseAdditionalRequestParamsCopyWith<$Res> {
  __$TicketResponseAdditionalRequestParamsCopyWithImpl(this._self, this._then);

  final _TicketResponseAdditionalRequestParams _self;
  final $Res Function(_TicketResponseAdditionalRequestParams) _then;

  /// Create a copy of TicketResponseAdditionalRequestParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? responseToRequester = null,
    Object? attachedFiles = null,
  }) {
    return _then(_TicketResponseAdditionalRequestParams(
      responseToRequester: null == responseToRequester
          ? _self.responseToRequester
          : responseToRequester // ignore: cast_nullable_to_non_nullable
              as String,
      attachedFiles: null == attachedFiles
          ? _self._attachedFiles
          : attachedFiles // ignore: cast_nullable_to_non_nullable
              as List<UploadFileResponseModelV2>,
    ));
  }
}

// dart format on
