// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_tag.params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketLabelParams {
  @JsonKey(name: 'tag_ids')
  List<int> get tagIds;

  /// Serializes this TicketLabelParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketLabelParams &&
            const DeepCollectionEquality().equals(other.tagIds, tagIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(tagIds));

  @override
  String toString() {
    return 'TicketLabelParams(tagIds: $tagIds)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketLabelParams implements TicketLabelParams {
  _TicketLabelParams(
      {@JsonKey(name: 'tag_ids') required final List<int> tagIds})
      : _tagIds = tagIds;

  final List<int> _tagIds;
  @override
  @JsonKey(name: 'tag_ids')
  List<int> get tagIds {
    if (_tagIds is EqualUnmodifiableListView) return _tagIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tagIds);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TicketLabelParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketLabelParams &&
            const DeepCollectionEquality().equals(other._tagIds, _tagIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_tagIds));

  @override
  String toString() {
    return 'TicketLabelParams(tagIds: $tagIds)';
  }
}

// dart format on
