// ignore_for_file: public_member_api_docs

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../../data/data.dart';
import '../../repository/ticket_repo.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketFollowMembersUseCase extends GPBaseFutureUseCase<
    TicketFollowMembersInput, TicketFollowMembersOutput> {
  const TicketFollowMembersUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<TicketFollowMembersOutput> buildUseCase(
    TicketFollowMembersInput input,
  ) async {
    final params = TicketFollowMembersParams(
      userIds: input.userIds,
      collabIds: input.collabIds,
      departmentIds: input.departmentIds,
      roleIds: input.roleIds,
    );

    final response = await _ticketRepository.getFollowMembers(
      ticketId: input.ticketId,
      params: params,
    );

    if (response.data != null && response.data?.isNotEmpty == true) {
      return TicketFollowMembersOutput(response.data ?? []);
    }

    return const TicketFollowMembersOutput([]);
  }
}

class TicketFollowMembersInput extends GPBaseInput {
  const TicketFollowMembersInput({
    required this.ticketId,
    this.userIds,
    this.collabIds,
    this.departmentIds,
    this.roleIds,
  });

  final String ticketId;
  final List<String>? userIds;
  final List<String>? collabIds;
  final List<String>? departmentIds;
  final List<String>? roleIds;
}

class TicketFollowMembersOutput extends GPBaseOutput {
  const TicketFollowMembersOutput(this.result);

  final List<Assignee> result;
}
