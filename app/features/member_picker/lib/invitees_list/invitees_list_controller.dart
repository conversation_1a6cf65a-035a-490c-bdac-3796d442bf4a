import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_member_picker/invitees_list/widget/limit_dialog.dart';
import 'package:gp_feat_member_picker/picker_mixin.dart';
import 'package:gp_feat_member_picker/selected_list/select_invitees_controller.dart';

import '../version/tooltip_mixin.dart';
import 'version/1.0.2.dart';
import 'version/1.0.4.dart';
import 'version/1.0.6.dart';

export 'version/1.0.2.dart';
export 'version/1.0.4.dart';
export 'version/1.0.6.dart';

class InviteesListController extends ValueNotifier<List<Assignee>>
    with OrgChatConfigTab, UserMeMixin, SelectedMemberMixin, TooltipMixin
    implements ArgumentInitialMixin {
  final _service = AssigneeApi();

  String nextLink = '';

  // final displayItems = BehaviorSubject<List<Assignee>>.seeded([]);
  final items = BehaviorSubject<List<Assignee>>.seeded([]);
  final selectedItems = BehaviorSubject<List<Assignee>>.seeded([]);
  final selectedMemberIds = BehaviorSubject<Set<int>>.seeded({});
  final List<Assignee> fullSelectedMembers = [];

  /// filter những member có id này sẽ k xuất hiện để select/unselect
  List<int>? filterOutMemberIds;

  final searchTextEditingController = TextEditingController();

  // var refreshController;
  final RefreshController refreshController = RefreshController();

  Stream<bool> get hasSelectedItem =>
      selectedItems.map((event) => event.isNotEmpty);
  final hasShadow = BehaviorSubject<bool>.seeded(false);
  final showBottomButton = BehaviorSubject<bool>.seeded(true);

  String _keyword = '';
  var canLoadMore = true;
  final isLoading = BehaviorSubject.seeded(false);
  final RxString searchText = ''.obs;

  final scrollController = ScrollController();
  bool? _onlyCurrentWorkspace;

  SelectInviteesOptions? svo;

  bool get ignoreMe => svo?.ignoreMe ?? true;

  int? get limitUsers => svo?.limitUsers;

  String get groupId => svo?.groupId ?? '';

  String get threadId => svo?.threadId ?? '';

  bool get reachLimitUsers {
    if (limitUsers != null && selectedItems.value.length >= limitUsers!) {
      return true;
    }
    return false;
  }

  /// Kiểm tra có đạt giới hạn chọn cho tab member không
  bool get reachTabSelectionLimit {
    final tabLimit = SelectInviteeTabs.member.getMaxSelectionLimit(svo!);
    if (tabLimit != null && selectedItems.value.length >= tabLimit) {
      return true;
    }
    return false;
  }

  void setOnlyCurrentWorkspace() {
    _onlyCurrentWorkspace = true;
  }

  void setOnPickModeListener(Function onPickOneCallback) {
    this.onPickOneCallback = onPickOneCallback;
  }

  InviteesListController(List<Assignee> value) : super(value) {
    selectedItems.value = value;
  }

  void initState() {
    initUserMe();

    getListItems(reset: true);
    selectedItems.listen((event) {
      value = event;
    });

    searchTextEditingController.addListener(() {
      final text = searchTextEditingController.text;
      searchText.value = text;
    });

    // listen to update invitees list
    debounce(searchText, _updateDisplayRolesWithSearchKeyword,
        time: 300.milliseconds);
  }

  Future<bool> getListItems({reset = false}) async {
    // check loading
    if (isLoading.value && !reset) return false;
    isLoading.add(true);

    // reset values if needed
    if (reset) {
      nextLink = '';
      canLoadMore = true;
      refreshController.footerMode?.value = LoadStatus.canLoading;
    }

    if (!ignoreMe) {
      swapUserMe();
    }

    try {
      ListAPIResponse<Assignee> response;
      final inputMembers = svo?.inputMembers();

      if (svo != null && svo?.hasInputMembers == true) {
        response = inputMembers!;
      } else {
        response = await _service.getAssignees(
          onlyCurrentWorkspace: _onlyCurrentWorkspace ?? _keyword.isEmpty,
          isSearching: _keyword.isNotEmpty,
          q: _keyword,
          nextLink: nextLink,
          groupId: groupId,
          threadId: threadId,
          limit: 30,
        );
      }

      isLoading.add(false);
      if (reset) {
        items.value = [];
      }

      if (response.data?.isNotEmpty == true && !ignoreMe) {
        removeDuplicateUserme(response.data ?? []);

        addUserMe(keyword: _keyword);
      }

      // exclude me user
      var responseData = response.data;

      updateAssigneeByAddedByAdmin(responseData ?? []);

      handleIgnoreMe(responseData ?? []);

      //
      final newItems = response.data ?? [];

      items.value.addAll(newItems);

      filterMembers();

      // assign next link
      nextLink = response.links?.next ?? "";

      fillSelectedMembers(newItems);

      // fill selected from selected list
      handleViewOnly();

      // refresh UI
      sortAddedByAdminList(items.value);

      items.add(items.value.toSet().toList());

      syncSelectedItems(responseData ?? []);

      canLoadMore = response.links?.next?.isNotEmpty ?? false;
      return canLoadMore;
    } catch (error, trace) {
      logDebug(trace);
      isLoading.add(false);
      handleError(error, trace);
      return false;
    }
  }

  void handleError(Object error, StackTrace s) {
    GPCoreTracker().appendError(
      'Flutter.memberPicker.getListItems',
      data: {'error': error, 'stacktrace': s},
    );
    GPCoreTracker().sendLog(
      message: 'Flutter.memberPicker.getListItems',
      trackerType: GPTrackerType.memberPicker,
    );

    isLoading.add(false);
    logDebug("got error ${error.toString()}, $s");
    var message = 'Có lỗi xảy ra, vui lòng thử lại!';
    if (error is AppException) {
      message = error.toString();
    }
    Popup.instance.showSnackBar(message: message, type: SnackbarType.error);
  }

  void setSelectedValue(List<Assignee> value) {
    for (var element in value) {
      _insertToListSelected(0, element);
    }
  }

  void addIfNotExistValue(List<Assignee> value) {
    for (var element in value) {
      final index = selectedItems.value.indexWhere((p1) => element.id == p1.id);
      if (index < 0) {
        _insertToListSelected(0, element);
      }
    }
  }

  void _insertToListSelected(int index, Assignee element) {
    selectedMemberController.insert(element);
    if (!selectedItems.value.contains(element)) {
      selectedItems.value.insert(0, element);
    }

    selectedItems.add(selectedItems.value);
    if (!fullSelectedMembers.contains(element)) {
      fullSelectedMembers.add(element);
    }
  }

  void _removeFromListSelected(Assignee element) {
    final index = selectedItems.value.indexWhere((p1) => element == p1);
    if (index >= 0) {
      selectedMemberController.remove(element);

      selectedItems.value.removeAt(index);
      selectedItems.add(selectedItems.value);
    }
    fullSelectedMembers.remove(element);
  }

  void onAssigneeUpdated(Assignee p1) {
    // Kiểm tra giới hạn chung (limitUsers)
    if (reachLimitUsers && p1.isSelected.value == false) {
      Popup.instance.showBottomSheet(LimitDialog(
          title: LocaleKeys.memberPicker_limit_user_title.tr
              .replaceFirst('%', limitUsers.toString()),
          content: LocaleKeys.memberPicker_limit_user_description.tr));
      return;
    }

    // Kiểm tra giới hạn theo tab
    if (reachTabSelectionLimit && p1.isSelected.value == false) {
      final tabLimit = SelectInviteeTabs.member.getMaxSelectionLimit(svo!);
      Popup.instance.showBottomSheet(LimitDialog(
          title:
              "${LocaleKeys.memberPicker_limit_title.tr} ${SelectInviteeTabs.member.title.toLowerCase()}",
          content:
              "${LocaleKeys.memberPicker_limit_description.tr} $tabLimit ${SelectInviteeTabs.member.title.toLowerCase()}"));
      return;
    }

    p1.isSelected.value = !p1.isSelected.value;
    items.add(items.value);

    // selected section
    if (p1.isSelected.value) {
      _insertToListSelected(0, p1.clone);

      onPickOneCallback?.call();
    } else {
      _removeFromListSelected(p1);
    }
    Get.find<SelectInviteesController>().checkHasAnySelectedItem();

    // selectedItems.add(selectedItems.value);

    hideTooltip();
    hideSelectedTooltip();
  }

  void removeAllSelected() {
    for (var assignee in selectedItems.value) {
      assignee.isSelected.value = false;
      var index = items.value.indexWhere((element) => element == assignee);
      if (index != -1) {
        items.value[index] = assignee;
        items.add(items.value);
      }
    }
    selectedItems.add([]);
    selectedMemberController.fullSelectedMemberIds.clear();
  }

  void onRemoveSelectedAssignee(Assignee assignee) {
    //
    assignee.isSelected.value = false;
    var index = items.value.indexWhere((element) => element == assignee);
    if (index != -1) {
      items.value[index] = assignee;
      items.add(items.value);
    }

    //
    _removeFromListSelected(assignee);
    Get.find<SelectInviteesController>().checkHasAnySelectedItem();
  }

  void _updateDisplayRolesWithSearchKeyword(String? keyword) {
    if (keyword != null) {
      _keyword = keyword;
    }
    canLoadMore = true;
    getListItems(reset: true);
    // final values = items.value
    //     .where((element) =>
    //         element.displayName.toLowerCase().contains(_keyword.toLowerCase()))
    //     .toList();
    // displayItems.add(values);
  }

  Future<bool> onLoadMore() async {
    if (canLoadMore) {
      await getListItems();
      refreshController.loadComplete();
    }
    return true;
  }

  @override
  void initArguments(SelectInviteesOptions arg) {
    svo = arg;
    if (arg.needGetAllSelectedMembers ?? false) {
      selectedMemberController.needGetFullSelectedMembers = true;
    }
  }

  void sortAddedByAdminList(List<Assignee> input) {
    if (svo?.membersAddedByAdminIds?.isNotEmpty == true) {
      input.sort(
        (a, b) =>
            a.isEnabled.value == false && a.isSelected.value == true ? 0 : 1,
      );
    }
  }
}

extension _HandleData on InviteesListController {
  void handleIgnoreMe(List<Assignee> responseData) {
    if (ignoreMe) {
      final userMeId = Constants.userId();
      responseData.removeWhere((element) => element.id.toString() == userMeId);
    }
  }

  void filterMembers() {
    // remove các user không đc hiện
    if (filterOutMemberIds?.isNotEmpty == true) {
      items.value
          .removeWhere((element) => filterOutMemberIds!.contains(element.id));
    }
  }

  void updateAssigneeByAddedByAdmin(List<Assignee> newItems) {
    if (svo?.membersAddedByAdminIds?.isEmpty == true) {
      return;
    }

    svo?.membersAddedByAdminIds?.forEach((element) {
      final Assignee? assignee =
          newItems.firstWhereOrNull((assignee) => assignee.id == element);

      if (assignee != null) {
        addTooltipByAssigneeItem(assignee);
      } else {
        final Assignee? selectedAssignee = selectedItems.value
            .firstWhereOrNull((assignee) => assignee.id == element);
        if (selectedAssignee != null) {
          addTooltipByAssigneeItem(selectedAssignee);
        }
      }

      if (element == userMe.id) {
        userMe.isEnabled.value = false;
      }
    });
  }

  void fillSelectedMembers(List<Assignee> newItems) {
    // fill selected items not removable
    if (selectedMemberIds.value.isNotEmpty) {
      for (var element in items.value) {
        if (selectedMemberIds.value.contains(element.id) == true) {
          element.isSelected.value = true;
          element.isEnabled.value = false;
          // return true;
        }
      }
    }

    if (selectedItems.value.isNotEmpty) {
      for (var element in newItems) {
        if (selectedItems.value.contains(element)) {
          element.isSelected.value = true;
        }
      }
    }
  }

  void handleViewOnly() {
    final bool isViewOnly = svo?.mode == SelectInviteesOptionsMode.viewOnly;

    if (isViewOnly) {
      items.value.clear();
      if (selectedMemberIds.value.isEmpty) {
        selectedItems.value.clear();

        selectedItems.add(selectedItems.value);
      }

      for (var element in selectedItems.value) {
        element.isEnabled.value = false;
        element.isSelected.value = true;
      }

      if (_keyword.isNotEmpty) {
        final s = TiengViet.parse(_keyword).toLowerCase();
        final newSelecteds = selectedItems.value.where((element) =>
            TiengViet.parse(element.displayName).toLowerCase().contains(s) ||
            TiengViet.parse(element.fullName ?? "").toLowerCase().contains(s) ||
            TiengViet.parse(element.email ?? "").toLowerCase().contains(s));

        items.value.addAll(newSelecteds);
      } else {
        items.value.addAll(selectedItems.value);
      }
    }
  }
}
