/*
 * Created Date: Thursday, 13th March 2025, 08:50:26
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 13th March 2025 09:36:50
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/base/models/list_api_response.dart';
import 'package:gp_core_v2/base/base.dart';
import 'package:injectable/injectable.dart';

import 'mapper.dart';

const _kInstanceName = 'kGPPortalMapper';


@Singleton(order: DiConstants.kDomainMapperOrder)
@Named(_kInstanceName)
@AutoMappr(
  [],
  includes: [
    PortalEntityMapper(),
  ],
)
final class GPPortalMapper extends $GPPortalMapper {
  const GPPortalMapper();
}

mixin GPPortalMapperMixin {
  /// convert [SOURCE] to [TARGET]
  TARGET _convertWithCatching<SOURCE, TARGET>(
    TARGET Function<SOURCE, TARGET>(SOURCE source) convertFunc,
    SOURCE source,
  ) {
    try {
      return convertFunc.call<SOURCE, TARGET>(source);
    } catch (e) {
      throw ParseException(ParseExceptionKind.errorMapping, e);
    }
  }

  
  TARGET convert<SOURCE, TARGET>(SOURCE data) {
    return _convertWithCatching(
      <SOURCE, TARGET>(source) {
        return GetIt.I<GPPortalMapper>(instanceName: _kInstanceName)
            .convert<SOURCE, TARGET>(source);
      },
      data,
    );
  }

  /// convert List [SOURCE] to List [TARGET]
  List<TARGET> _convertListWithCatching<SOURCE, TARGET>(
    List<TARGET> Function<SOURCE, TARGET>(List<SOURCE> source) convertFunc,
    List<SOURCE> source,
  ) {
    try {
      return convertFunc.call<SOURCE, TARGET>(source);
    } catch (e) {
      throw ParseException(ParseExceptionKind.errorMapping, e);
    }
  }

  
  List<TARGET> convertList<SOURCE, TARGET>(List<SOURCE> data) {
    return _convertListWithCatching(
      <SOURCE, TARGET>(source) {
        return GetIt.I<GPPortalMapper>(instanceName: _kInstanceName)
            .convertList<SOURCE, TARGET>(source);
      },
      data,
    );
  }

  String? nextLink(Object? data) {
    if (data is Links) {
      return data.next;
    }

    return null;
  }
}
