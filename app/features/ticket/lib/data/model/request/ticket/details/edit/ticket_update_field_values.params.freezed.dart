// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_update_field_values.params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketUpdateFieldValuesParams {
  @JsonKey(name: 'updated_field_values')
  List<TicketUpdateFieldValuesDataParams> get fieldValues;
  @JsonKey(name: 'node_id')
  int get nodeId;

  ///
  @JsonKey(name: 'parallel_with_additional_response')
  bool get parallelWithAdditionalResponse;

  /// Serializes this TicketUpdateFieldValuesParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketUpdateFieldValuesParams &&
            const DeepCollectionEquality()
                .equals(other.fieldValues, fieldValues) &&
            (identical(other.nodeId, nodeId) || other.nodeId == nodeId) &&
            (identical(other.parallelWithAdditionalResponse,
                    parallelWithAdditionalResponse) ||
                other.parallelWithAdditionalResponse ==
                    parallelWithAdditionalResponse));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(fieldValues),
      nodeId,
      parallelWithAdditionalResponse);

  @override
  String toString() {
    return 'TicketUpdateFieldValuesParams(fieldValues: $fieldValues, nodeId: $nodeId, parallelWithAdditionalResponse: $parallelWithAdditionalResponse)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketUpdateFieldValuesParams implements TicketUpdateFieldValuesParams {
  _TicketUpdateFieldValuesParams(
      {@JsonKey(name: 'updated_field_values')
      required final List<TicketUpdateFieldValuesDataParams> fieldValues,
      @JsonKey(name: 'node_id') required this.nodeId,
      @JsonKey(name: 'parallel_with_additional_response')
      this.parallelWithAdditionalResponse = false})
      : _fieldValues = fieldValues;

  final List<TicketUpdateFieldValuesDataParams> _fieldValues;
  @override
  @JsonKey(name: 'updated_field_values')
  List<TicketUpdateFieldValuesDataParams> get fieldValues {
    if (_fieldValues is EqualUnmodifiableListView) return _fieldValues;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_fieldValues);
  }

  @override
  @JsonKey(name: 'node_id')
  final int nodeId;

  ///
  @override
  @JsonKey(name: 'parallel_with_additional_response')
  final bool parallelWithAdditionalResponse;

  @override
  Map<String, dynamic> toJson() {
    return _$TicketUpdateFieldValuesParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketUpdateFieldValuesParams &&
            const DeepCollectionEquality()
                .equals(other._fieldValues, _fieldValues) &&
            (identical(other.nodeId, nodeId) || other.nodeId == nodeId) &&
            (identical(other.parallelWithAdditionalResponse,
                    parallelWithAdditionalResponse) ||
                other.parallelWithAdditionalResponse ==
                    parallelWithAdditionalResponse));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_fieldValues),
      nodeId,
      parallelWithAdditionalResponse);

  @override
  String toString() {
    return 'TicketUpdateFieldValuesParams(fieldValues: $fieldValues, nodeId: $nodeId, parallelWithAdditionalResponse: $parallelWithAdditionalResponse)';
  }
}

/// @nodoc
mixin _$TicketUpdateFieldValuesDataParams {
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'value')
  dynamic get value;

  /// Dùng cho update đơn vị tiền tệ
  @JsonKey(name: 'unit_id', includeIfNull: false)
  int? get unitId;

  /// Serializes this TicketUpdateFieldValuesDataParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketUpdateFieldValuesDataParams &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other.value, value) &&
            (identical(other.unitId, unitId) || other.unitId == unitId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(value), unitId);

  @override
  String toString() {
    return 'TicketUpdateFieldValuesDataParams(id: $id, value: $value, unitId: $unitId)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketUpdateFieldValuesDataParams
    implements TicketUpdateFieldValuesDataParams {
  _TicketUpdateFieldValuesDataParams(
      {@JsonKey(name: 'id') required this.id,
      @JsonKey(name: 'value') this.value,
      @JsonKey(name: 'unit_id', includeIfNull: false) this.unitId});

  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'value')
  final dynamic value;

  /// Dùng cho update đơn vị tiền tệ
  @override
  @JsonKey(name: 'unit_id', includeIfNull: false)
  final int? unitId;

  @override
  Map<String, dynamic> toJson() {
    return _$TicketUpdateFieldValuesDataParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketUpdateFieldValuesDataParams &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other.value, value) &&
            (identical(other.unitId, unitId) || other.unitId == unitId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(value), unitId);

  @override
  String toString() {
    return 'TicketUpdateFieldValuesDataParams(id: $id, value: $value, unitId: $unitId)';
  }
}

// dart format on
