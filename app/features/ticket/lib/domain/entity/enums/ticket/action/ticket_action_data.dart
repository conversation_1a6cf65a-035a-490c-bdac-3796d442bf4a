import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/lib.dart';

part 'ticket_action_data.g.dart';

/*
  easy view: cmd + K + J then cmd K + 5
*/

const _kButtons = 'buttons';
const _kHighlightFirstButton = 'highlightFirstButton';
const _kHighlightSecondaryButton = 'highlightSecondaryButton';

List<Map<String, dynamic>> _data = [
  {
    /// requester
    TicketRole.requester.name: {
      TicketStatus.waitingForHandling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.duplicate.name,
            TicketAction.editField.name,
            TicketAction.cancelTicket.name,
            // TicketAction.addHandler.name,
          ]
        }
      },
      TicketStatus.handling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.provideInfo.name,
            TicketAction.edit.name,
            TicketAction.duplicate.name,
            TicketAction.editField.name,
            TicketAction.cancelTicket.name,
            // TicketAction.addHandler.name,
          ]
        },
        TicketNodeStatus.handling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.provideInfo.name,
            TicketAction.edit.name,
            TicketAction.duplicate.name,
            TicketAction.editField.name,
            TicketAction.cancelTicket.name,
            TicketAction.addHandler.name,
            // TicketAction.changeHandler.name,
          ]
        },
        TicketNodeStatus.waitingForArchive.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.duplicate.name,
            TicketAction.editField.name,
            TicketAction.cancelTicket.name,
            // TicketAction.changeHandler.name,
            TicketAction.approveOnHold.name,
            TicketAction.rejectOnHold.name,
          ],
          // _kHighlightFirstButton: true,
          // _kHighlightSecondaryButton: true,
        },
        TicketNodeStatus.waitingForProvideInfo.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.provideInfo.name,
            TicketAction.edit.name,
            TicketAction.duplicate.name,
            TicketAction.editField.name,
            TicketAction.cancelTicket.name,
            // TicketAction.changeHandler.name,
          ]
        },
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.provideInfo.name,
            TicketAction.edit.name,
            TicketAction.duplicate.name,
            TicketAction.editField.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.provideInfo.name,
            TicketAction.edit.name,
            TicketAction.duplicate.name,
            TicketAction.editField.name,
            TicketAction.cancelTicket.name,
          ]
        }
      },
      TicketStatus.archived.name: {
        TicketNodeStatus.archived.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.edit.name,
            TicketAction.duplicate.name,
            TicketAction.editField.name,
            TicketAction.cancelTicket.name,
            // TicketAction.changeHandler.name,
          ]
        },
      },
      TicketStatus.closed.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.duplicate.name,
            TicketAction.cancelTicket.name,
          ],
          _kHighlightFirstButton: false,
          _kHighlightSecondaryButton: false,
        },
        TicketNodeStatus.rejected.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.duplicate.name,
            TicketAction.cancelTicket.name,
          ],
          _kHighlightFirstButton: false,
          _kHighlightSecondaryButton: false,
        },
        TicketNodeStatus.notApprove.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.duplicate.name,
            TicketAction.cancelTicket.name,
          ],
          _kHighlightFirstButton: false,
          _kHighlightSecondaryButton: false,
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.duplicate.name,
          ],
          _kHighlightFirstButton: false,
          _kHighlightSecondaryButton: false,
        },
      },
      TicketStatus.handled.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.duplicate.name,
            TicketAction.closeTicket.name,
            TicketAction.reopenTicket.name,
            TicketAction.cancelTicket.name,
          ],
          _kHighlightFirstButton: true,
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.duplicate.name,
            TicketAction.closeTicket.name,
            TicketAction.reopenTicket.name,
            TicketAction.cancelTicket.name,
          ],
          _kHighlightFirstButton: true,
        },
      },
      TicketStatus.cancelled.name: {
        TicketNodeStatus.cancelled.name: {
          _kButtons: [
            TicketAction.duplicate.name,
            TicketAction.deleteTicket.name,
          ]
        },
      },
      TicketStatus.spam.name: {
        TicketNodeStatus.spam.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.duplicate.name,
          ]
        },
      },
    },

    /// handler
    TicketRole.handler.name: {
      TicketStatus.waitingForHandling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.addHandler.name,
          ]
        },
      },
      TicketStatus.handling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.addHandler.name,
          ]
        },
        TicketNodeStatus.handling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.editField.name,
            TicketAction.nextStep.name,
            TicketAction.cancelTicket.name,
            TicketAction.changeHandler.name,
            TicketAction.requestToProvideInfo.name,
            TicketAction.moveToOnHold.name,
            TicketAction.addLabel.name,
            TicketAction.spam.name,
          ],
          _kHighlightFirstButton: true,
        },
        TicketNodeStatus.waitingForArchive.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.editField.name,
            TicketAction.cancelTicket.name,
            TicketAction.changeHandler.name,
            TicketAction.continueHandle.name,
            TicketAction.approveOnHold.name,
            TicketAction.rejectOnHold.name,
            TicketAction.addLabel.name,
          ],
          _kHighlightFirstButton: true,
          _kHighlightSecondaryButton: true,
        },
        TicketNodeStatus.waitingForProvideInfo.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.editField.name,
            TicketAction.cancelTicket.name,
            TicketAction.changeHandler.name,
            TicketAction.requestToProvideInfo.name,
            TicketAction.continueHandle.name,
            TicketAction.addLabel.name,
          ],
          _kHighlightFirstButton: true,
        },
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.addLabel.name,
          ]
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.addLabel.name,
          ]
        },
      },
      TicketStatus.archived.name: {
        TicketNodeStatus.archived.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.changeHandler.name,
            TicketAction.continueHandle.name,
            TicketAction.addLabel.name,
          ],
          _kHighlightFirstButton: true,
        },
      },
      TicketStatus.handled.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.addLabel.name,
          ]
        },
      },
      TicketStatus.closed.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.rejected.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.notApprove.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
      },
      TicketStatus.cancelled.name: {
        TicketNodeStatus.cancelled.name: {
          _kButtons: [
            TicketAction.deleteTicket.name,
          ]
        },
      },
      TicketStatus.spam.name: {
        TicketNodeStatus.spam.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.duplicate.name,
          ]
        },
      },
    },

    /// send to group
    TicketRole.sendToGroup.name: {
      TicketStatus.waitingForHandling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.comment.name,
            TicketAction.addHandler.name,
            TicketAction.addFollower.name,
            // TicketAction.unfollow.name,
            TicketAction.cancelTicket.name,
          ]
        }
      },
      TicketStatus.handling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.comment.name,
            TicketAction.addHandler.name,
            TicketAction.addFollower.name,
            // TicketAction.unfollow.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.handling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.changeHandler.name,
            // TicketAction.unfollow.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.waitingForArchive.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.changeHandler.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.archived.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.changeHandler.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.waitingForProvideInfo.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.changeHandler.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
      },
      TicketStatus.archived.name: {
        TicketNodeStatus.archived.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
      },
      TicketStatus.handled.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
      },
      TicketStatus.closed.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
      },
      TicketStatus.cancelled.name: {
        TicketNodeStatus.cancelled.name: {_kButtons: []},
      },
      TicketStatus.spam.name: {
        TicketNodeStatus.spam.name: {_kButtons: []},
      }
    },

    /// stepManager
    TicketRole.stepManager.name: {
      TicketStatus.waitingForHandling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.addHandler.name,
          ]
        },
      },
      TicketStatus.handling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.addHandler.name,
          ]
        },
        TicketNodeStatus.handling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.changeHandler.name,
            TicketAction.requestToProvideInfo.name,
            TicketAction.moveToOnHold.name,
          ]
        },
        TicketNodeStatus.waitingForArchive.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.changeHandler.name,
            TicketAction.requestToProvideInfo.name,
            TicketAction.approveOnHold.name,
            TicketAction.rejectOnHold.name,
          ],
          _kHighlightFirstButton: true,
          _kHighlightSecondaryButton: true,
        },
        TicketNodeStatus.waitingForProvideInfo.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.changeHandler.name,
            TicketAction.requestToProvideInfo.name,
          ]
        },
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
      },
      TicketStatus.archived.name: {
        TicketNodeStatus.archived.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.changeHandler.name,
          ]
        },
      },
      TicketStatus.handled.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
      },
      TicketStatus.closed.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.rejected.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.notApprove.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
          ]
        },
      },
      TicketStatus.cancelled.name: {
        TicketNodeStatus.cancelled.name: {
          _kButtons: [
            TicketAction.deleteTicket.name,
          ]
        },
      },
      TicketStatus.spam.name: {
        TicketNodeStatus.spam.name: {
          _kButtons: [
            // TODO
          ]
        },
      },
    },

    /// follower
    TicketRole.follower.name: {
      TicketStatus.waitingForHandling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.handling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.handling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.waitingForArchive.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.approveOnHold.name,
            TicketAction.rejectOnHold.name,
            // TicketAction.unfollow.name,
          ],
          _kHighlightFirstButton: true,
          _kHighlightSecondaryButton: true,
        },
        TicketNodeStatus.waitingForProvideInfo.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.archived.name: {
        TicketNodeStatus.archived.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.handled.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.closed.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.rejected.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.notApprove.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.cancelled.name: {
        TicketNodeStatus.cancelled.name: {
          _kButtons: [
            TicketAction.deleteTicket.name,
            TicketAction.addFollower.name,
          ]
        },
      },
      TicketStatus.spam.name: {
        TicketNodeStatus.spam.name: {
          _kButtons: [
            // TODO
          ]
        },
      },
    },

    /// createdByDepartment
    TicketRole.createdByDepartment.name: {
      TicketStatus.waitingForHandling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.handling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.handling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.waitingForArchive.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.approveOnHold.name,
            TicketAction.rejectOnHold.name,
            // TicketAction.unfollow.name,
          ],
          _kHighlightFirstButton: true,
          _kHighlightSecondaryButton: true,
        },
        TicketNodeStatus.waitingForProvideInfo.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.archived.name: {
        TicketNodeStatus.archived.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.handled.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.closed.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.rejected.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.notApprove.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.cancelled.name: {
        TicketNodeStatus.cancelled.name: {
          _kButtons: [
            TicketAction.deleteTicket.name,
          ]
        },
      },
      TicketStatus.spam.name: {
        TicketNodeStatus.spam.name: {
          _kButtons: [
            // TODO
          ]
        },
      },
    },

    /// supporter
    TicketRole.supporter.name: {
      TicketStatus.waitingForHandling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.handling.name: {
        TicketNodeStatus.waitingForHandling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.handling.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.waitingForArchive.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            TicketAction.approveOnHold.name,
            TicketAction.rejectOnHold.name,
            // TicketAction.unfollow.name,
          ],
          _kHighlightFirstButton: true,
          _kHighlightSecondaryButton: true,
        },
        TicketNodeStatus.waitingForProvideInfo.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.approved.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.archived.name: {
        TicketNodeStatus.archived.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.handled.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.closed.name: {
        TicketNodeStatus.handled.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.rejected.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
        TicketNodeStatus.notApprove.name: {
          _kButtons: [
            TicketAction.addFollower.name,
            TicketAction.comment.name,
            TicketAction.cancelTicket.name,
            // TicketAction.unfollow.name,
          ]
        },
      },
      TicketStatus.cancelled.name: {
        TicketNodeStatus.cancelled.name: {
          _kButtons: [
            TicketAction.deleteTicket.name,
          ]
        },
      },
      TicketStatus.spam.name: {
        TicketNodeStatus.spam.name: {
          _kButtons: [
            // TODO
          ]
        },
      },
    },
  },
];

final class TicketPermission with NodeGraphMixin {
  static final TicketPermission _instance = TicketPermission._();

  factory TicketPermission() => _instance;

  TicketPermission._();

  TicketActionEntity? getTicketActions({
    required TicketEntity ticketEntity,
    required TicketNodeEntity ticketNodeEntity,
    required TicketFlowChartEntity ticketFlowChartEntity,
    required bool isTicketAssignee,
    required List<Assignee> adminWorkflow,
  }) {
    if (ticketNodeEntity.status == TicketNodeStatus.notTurnYet) return null;

    final List<TicketActionEntity> ticketActions = [];
    final ticketStatus = ticketEntity.status;
    final ticketNodeStatus = ticketNodeEntity.status;
    final ticketNodeType = ticketNodeEntity.type;
    final currentNodeId = ticketNodeEntity.id.toString();
    final userRoleInNode =
        ticketEntity.userRole?.userRoleInNode?[currentNodeId];
    final hasUserRoleInNode = userRoleInNode != null;

    final hasAssignee = ticketNodeEntity.assignee != null;

    // userRole
    final isRequestor = ticketEntity.creator?.id == Constants.userId();
    bool isStepManager = false;
    bool isHandler = false;
    bool isNodeHandler = false;
    bool isHandlerGroup = false;
    bool isSupporter = false;
    bool isFollower = false;

    bool isSameDepartmentWithAssignee = false;
    bool isSameDepartmentWithRequester = false;
    bool canCancel = false;
    bool canDelete = false;
    bool canAcceptDeclineOnHold = false;
    bool isAfterStartNode = false;

    final isNodeWaiting =
        ticketNodeEntity.status == TicketNodeStatus.waitingForHandling;
    final isNodeProcessing =
        ticketNodeEntity.status == TicketNodeStatus.handling;
    final isNodePreOnHold =
        ticketNodeEntity.status == TicketNodeStatus.waitingForArchive;
    final isNodeNeedUpdate =
        ticketNodeEntity.status == TicketNodeStatus.waitingForProvideInfo;
    final isNodeOnHold = ticketNodeEntity.status == TicketNodeStatus.archived;
    final isTicketWaiting =
        ticketEntity.status == TicketStatus.waitingForHandling;
    final isTicketProcessing = ticketEntity.status == TicketStatus.handling;
    final isTicketCancelled = ticketEntity.status == TicketStatus.cancelled;
    final isTicketOnHold = ticketEntity.status == TicketStatus.archived;

    final bool isAdminWorkflow =
        ticketEntity.userRole?.userWhoIsAdminWorkflow ?? false;

    if (hasUserRoleInNode) {
      isStepManager = userRoleInNode['admin'];
      isHandler = userRoleInNode['assignee'];
      isHandlerGroup = userRoleInNode['assignee_group'];
      isSupporter = userRoleInNode['supporter'];
      isFollower = userRoleInNode['follower'];
    }

    isNodeHandler =
        ticketNodeEntity.assignee?.id.toString() == Constants.userId();

    if (isHandler && !isHandlerGroup && !isHandlerGroup) {
      isHandlerGroup = true;
    }

    final canUpsertApprove = ticketNodeEntity.option.assigneeType ==
            TicketNodeAssigneeType.creatorLeader &&
        isRequestor;

    final listStep = mapListStep(
        edges: ticketFlowChartEntity.edges, nodes: ticketFlowChartEntity.nodes);
    final stepIndex =
        listStep.indexWhere((e) => e.nodeIds.contains(currentNodeId));
    if (stepIndex == 1) isAfterStartNode = true;

    final isCanGetBackPreviousStep =
        isAfterStartNode ? false : (isNodeHandler && isNodeProcessing);
    final isCanGetBackEndStep = (isAdminWorkflow || isRequestor) &&
        ticketEntity.status == TicketStatus.handled;

    // advanceSeting
    final isWFManager = isWorkFlowManager(adminWorkflow: adminWorkflow);
    final bool isAllowCancel = isAllow(
      ticketEntity: ticketEntity,
      allowActionSetting: ticketEntity.workflow?.advanceSetting?.allowCancel,
      isFollower: isFollower,
      isHandler: isHandler,
      isRequestor: isRequestor,
      isStepManager: isStepManager,
      isWorkFlowManager: isWFManager,
    );

    final bool isAllowDelete = isAllow(
      ticketEntity: ticketEntity,
      allowActionSetting: ticketEntity.workflow?.advanceSetting?.allowDelete,
      isFollower: isFollower,
      isHandler: isHandler,
      isRequestor: isRequestor,
      isStepManager: isStepManager,
      isWorkFlowManager: isWFManager,
    );

    // có thể approve trước khi bảo lưu
    // final bool canApproveBeforeOnHold = isAllow(
    //   ticketEntity: ticketEntity,
    //   allowActionSetting: ticketEntity.workflow.advanceSetting?.allowDelete,
    //   isFollower: isFollower,
    //   isHandler: isHandler,
    //   isRequestor: isRequestor,
    //   isStepManager: isStepManager,
    //   isWorkFlowManager: isWFManager,
    // );

    // isSameDepartmentWithAssignee
    if (ticketEntity.userRole?.sameDepartmentWithRequester == true) {
      isSameDepartmentWithAssignee = true;
    } else {
      if (hasUserRoleInNode) {
        isSameDepartmentWithAssignee =
            userRoleInNode['same_department_with_assignee'];
      }
    }

    isSameDepartmentWithRequester =
        (ticketEntity.userRole?.sameDepartmentWithRequester ?? false) &&
            !isRequestor;

    final formFieldPermission =
        ticketNodeEntity.option.workflowFormFieldPermissions;
    final canEditAtLeaseOneField = formFieldPermission?.any((element) {
          return element.canEdit;
        }) ??
        false;

    final isAllowEditingAfterResolving =
        (ticketEntity.workflow?.advanceSetting?.isAllowEditingAfterResolving ??
                false) &&
            (ticketEntity.status == TicketStatus.handled ||
                ticketEntity.status == TicketStatus.closed) &&
            isTicketAssignee;

    // canCancel
    if (isAllowCancel) {
      if (ticketEntity.userRole?.userWhoCanCancelTicket == true) {
        canCancel = true;
      } else {
        if (userRoleInNode != null) {
          canCancel = ticketEntity.userRole?.userRoleInNode?[currentNodeId]
              ['user_who_can_cancel_ticket'];
        }
      }
    }

    // canDelete
    if (isAllowDelete) {
      canDelete = true;
    } else if (ticketEntity.userRole?.userWhoCanDeleteTicket == true) {
      canDelete = true;
    } else if (userRoleInNode != null) {
      canDelete = ticketEntity.userRole?.userRoleInNode?[currentNodeId]
          ['user_who_can_delete_ticket'];
    }

    // canApproveArchive / canRejectArchive
    if (ticketEntity.userRole?.userWhoCanAcceptDeclineOnHoldRequest == true) {
      canAcceptDeclineOnHold = true;
    }

    // add buttons
    if (isRequestor) {
      ticketActions.add(_getTicketActions(
        role: TicketRole.requester,
        status: ticketStatus,
        nodeStatus: ticketNodeStatus,
      ));
    }

    if (isStepManager) {
      ticketActions.add(_getTicketActions(
        role: TicketRole.stepManager,
        status: ticketStatus,
        nodeStatus: ticketNodeStatus,
      ));
    }

    if (isNodeHandler) {
      ticketActions.add(_getTicketActions(
        role: TicketRole.handler,
        status: ticketStatus,
        nodeStatus: ticketNodeStatus,
      ));

      if (ticketNodeType == TicketNodeType.approval && isNodeProcessing) {
        ticketActions.addAll(
          [
            const TicketActionEntity(
              actions: [
                TicketAction.approve,
                TicketAction.reject,
              ],
              highlightFirstButton: true,
              highlightSecondaryButton: true,
            ),
          ],
        );

        for (var element in ticketActions) {
          if (element.actions.contains(TicketAction.nextStep)) {
            element.actions.remove(TicketAction.nextStep);
          }
        }
      }
    }

    if (isHandlerGroup) {
      ticketActions.add(_getTicketActions(
        role: TicketRole.sendToGroup,
        status: ticketStatus,
        nodeStatus: ticketNodeStatus,
      ));
    }

    if (isSupporter) {
      ticketActions.add(
        _getTicketActions(
          role: TicketRole.supporter,
          status: ticketStatus,
          nodeStatus: ticketNodeStatus,
        ),
      );
    }

    if (isFollower) {
      ticketActions.add(_getTicketActions(
        role: TicketRole.follower,
        status: ticketStatus,
        nodeStatus: ticketNodeStatus,
      ));
    }

    if (ticketEntity.userRole?.userWhoCanAcceptDeclineOnHoldRequest == true &&
        isNodePreOnHold) {
      ticketActions.addAll(
        [
          const TicketActionEntity(
            actions: [
              TicketAction.approveOnHold,
              TicketAction.rejectOnHold,
              TicketAction.addFollower,
              TicketAction.comment,
              // TicketAction.unfollow,
            ],
            highlightFirstButton: true,
            highlightSecondaryButton: true,
          ),
        ],
      );
    }

    if (!isTicketCancelled) {
      ticketActions.add(const TicketActionEntity(
        actions: [
          TicketAction.addFollower,
        ],
      ));
    }

    // if ((isFollower || isSupporter || isSameDepartmentWithRequester) &&
    //     !isNodeHandler) {
    //   ticketActions.add(const TicketActionEntity(
    //     actions: [
    //       TicketAction.unfollow,
    //     ],
    //   ));
    // }

    // returning result
    final actions = <TicketAction>[];

    if (hasAssignee) {
      if (!(isNodeHandler && isRequestor) && (isNodeHandler || isRequestor)) {
        actions.add(TicketAction.chat);
      }
    }

    if (ticketActions.isNotEmpty) {
      bool highlightFirstButton = false;
      bool highlightSecondaryButton = false;

      for (var element in ticketActions) {
        actions.addAll(element.actions);

        if (!highlightFirstButton) {
          highlightFirstButton = element.highlightFirstButton;
        }

        if (!highlightSecondaryButton) {
          highlightSecondaryButton = element.highlightSecondaryButton;
        }
      }

      final actionSet = actions.toSet();

      // Luôn hiện comment
      actionSet.add(TicketAction.comment);

      if (isRequestor) {
        actionSet.add(TicketAction.duplicate);
      }

      if (isHandler && !isNodeHandler) {
        if (isTicketWaiting && isNodeWaiting) {
          actionSet.add(TicketAction.addHandler);
        }
        actionSet.remove(TicketAction.nextStep);
        highlightFirstButton = false;
        highlightSecondaryButton = false;
      }

      if (isHandler && isTicketProcessing && isNodeProcessing) {
        actionSet.add(TicketAction.changeHandler);
      }

      if (isNodeHandler &&
          (isNodePreOnHold || isNodeOnHold || isNodeNeedUpdate)) {
        actionSet.add(TicketAction.continueHandle);
      }

      if (!isNodeHandler) {
        actionSet.remove(TicketAction.addLabel);
        if (actionSet.contains(TicketAction.continueHandle)) {
          actionSet.remove(TicketAction.continueHandle);
          if (actionSet.hasHightlightButton == false) {
            highlightFirstButton = false;
            highlightSecondaryButton = false;
          }
        }
      }

      if (isNodeHandler) {
        actionSet.add(TicketAction.addLabel);
      }

      // if ((!isFollower && !isSupporter && !isSameDepartmentWithRequester) ||
      //     isNodeHandler) {
      //   actionSet.remove(TicketAction.unfollow);
      // }

      if ((!isNodeHandler && !isStepManager) ||
          (!isNodeProcessing && !isNodeNeedUpdate)) {
        actionSet.remove(TicketAction.requestToProvideInfo);
      }

      if (canUpsertApprove ||
          !isNodeWaiting ||
          ticketNodeEntity.assignee != null ||
          (!isHandler && !isStepManager)) {
        actionSet.remove(TicketAction.addHandler);
      }

      if (isRequestor && ticketNodeEntity.isHeadDepartmentAssignee) {
        if (isNodeWaiting) {
          actionSet.add(TicketAction.addHandler);
        } else if (isNodeProcessing || isNodeOnHold) {
          actionSet.add(TicketAction.changeHandler);
        }
      }

      if (!canUpsertApprove &&
          (isNodeHandler || isStepManager || isHandlerGroup || isHandler) &&
          (isNodeProcessing ||
              isNodePreOnHold ||
              isNodeNeedUpdate ||
              isNodeOnHold)) {
        actionSet.add(TicketAction.changeHandler);
      }

      actionSet.add(TicketAction.editField);

      if (((isRequestor &&
                      (isTicketProcessing ||
                          isTicketOnHold ||
                          isTicketWaiting)) ||
                  (isNodeHandler &&
                      isTicketProcessing &&
                      (isNodeProcessing ||
                          isNodePreOnHold ||
                          isNodeNeedUpdate)) ||
                  isAllowEditingAfterResolving) ==
              false ||
          !canEditAtLeaseOneField) {
        actionSet.remove(TicketAction.editField);
      }

      if (hasAssignee) {
        actionSet.remove(TicketAction.addHandler);
      }

      if (!canCancel) {
        actionSet.remove(TicketAction.cancelTicket);
      }
      if (!canDelete) {
        actionSet.remove(TicketAction.deleteTicket);
      } else {
        actionSet.add(TicketAction.deleteTicket);
      }
      if (!canAcceptDeclineOnHold) {
        actionSet.remove(TicketAction.approveOnHold);
        actionSet.remove(TicketAction.rejectOnHold);
        if (actionSet.hasHightlightButton == false) {
          highlightFirstButton = false;
          highlightSecondaryButton = false;
        }
      }

      if (ticketEntity.showRating) {
        actionSet.remove(TicketAction.closeTicket);
        actionSet.remove(TicketAction.reopenTicket);
        // actionSet.remove(TicketAction.addLabel);
        highlightFirstButton = false;
        highlightSecondaryButton = false;
      }

      if (isCanGetBackPreviousStep) {
        actionSet.add(TicketAction.backStep);
      }

      if (isCanGetBackEndStep) {
        actionSet.add(TicketAction.backEndStep);
      }

      if (actionSet.hasHightlightOnlyOneButton) {
        highlightFirstButton = true;
        highlightSecondaryButton = false;
      }

      return TicketActionEntity(
        actions: actionSet.toList().orderByPriority(),
        highlightFirstButton: highlightFirstButton,
        highlightSecondaryButton: highlightSecondaryButton,
      );
    }

    return null;
  }

  TicketActionEntity _getTicketActions({
    required TicketRole role,
    required TicketStatus status,
    required TicketNodeStatus nodeStatus,
  }) {
    assert(_data.isNotEmpty);

    for (var element in _data) {
      if (element.containsKey(role.name)) {
        final elementData = element[role.name][status.name][nodeStatus.name];

        if (elementData is Map) {
          assert(elementData.containsKey(_kButtons));

          return TicketActionEntity.fromJson(
            Map<String, dynamic>.from(elementData),
          );
        }
      }
    }

    return const TicketActionEntity(actions: []);
  }

  bool isWorkFlowManager({
    required List<Assignee> adminWorkflow,
  }) {
    for (var member in adminWorkflow) {
      if (member.id.toString() == Constants.userId()) {
        return true;
      }
    }

    return false;
  }

  bool isAllow({
    required TicketEntity ticketEntity,
    WorkflowAdvanceSettingAllowActionEntity? allowActionSetting,
    bool? isRequestor,

    /// người quản lý bước
    bool? isStepManager,
    bool? isHandler,
    bool? isFollower,

    /// người quản lý quy trình
    bool? isWorkFlowManager,
  }) {
    if (allowActionSetting == null) return false;
    if (!allowActionSetting.isAllowed) return false;
    if (allowActionSetting.hasParticipantApplied != true) return false;

    // final advanceSetting = ticketEntity.workflow.advanceSetting;
    // if (advanceSetting == null) return false;

    final ticketStatus = ticketEntity.status;

    bool isTicketWaitingForHandling =
        ticketStatus == TicketStatus.waitingForHandling;
    bool isTicketHandling = ticketStatus == TicketStatus.handling;
    bool isTicketHandledOrClosed = ticketStatus == TicketStatus.handled ||
        ticketStatus == TicketStatus.closed;
    bool isTicketCancelled = ticketStatus == TicketStatus.cancelled;
    final participantApplied = allowActionSetting.participantApplied;

    bool isAllowAction = false;
    if (isTicketWaitingForHandling) {
      isAllowAction = allowActionSetting.option?.whenOpen ?? false;
    } else if (isTicketHandling) {
      isAllowAction = allowActionSetting.option?.whenProcessing ?? false;
    } else if (isTicketHandledOrClosed) {
      isAllowAction = allowActionSetting.option?.whenDoneOrClosed ?? false;
    } else if (isTicketCancelled) {
      isAllowAction = allowActionSetting.isAllowed;
    }

    bool? hasPermission;
    if (isRequestor == true && participantApplied?.requester == true) {
      hasPermission ??= participantApplied?.requester;
    }
    if (isHandler == true && participantApplied?.assigneeStep == true) {
      hasPermission ??= participantApplied?.assigneeStep;
    }
    if (isStepManager == true && participantApplied?.adminCurrentStep == true) {
      hasPermission ??= participantApplied?.adminCurrentStep;
    }
    if (isWorkFlowManager == true) {
      hasPermission ??= isWorkFlowManager;
    }

    return isAllowAction && (hasPermission ?? false);
  }
}

extension TicketActionExtension on Set<TicketAction> {
  bool get hasHightlightButton {
    const hightLightActions = [
      TicketAction.approve,
      TicketAction.reject,
      TicketAction.approveOnHold,
      TicketAction.rejectOnHold,
      TicketAction.continueHandle,
      TicketAction.nextStep,
      TicketAction.closeTicket,
    ];

    for (var element in this) {
      if (hightLightActions.contains(element)) {
        return true;
      }
    }

    return false;
  }

  /// Check chỉ có 1 button màu xanh
  bool get hasHightlightOnlyOneButton {
    const hightLightOneButtonActions = [
      TicketAction.continueHandle,
      TicketAction.nextStep,
    ];

    const hightLightTwoButtonActions = [
      TicketAction.approve,
      TicketAction.reject,
      TicketAction.approveOnHold,
      TicketAction.rejectOnHold,
    ];

    bool containOneButton = false;
    bool containTwoButton = false;

    for (var element in this) {
      if (hightLightOneButtonActions.contains(element)) {
        containOneButton = true;
      }

      if (hightLightTwoButtonActions.contains(element)) {
        containTwoButton = true;
      }
    }

    return containOneButton && !containTwoButton;
  }
}

@JsonSerializable()
final class TicketActionEntity {
  @JsonKey(name: _kButtons)
  final List<TicketAction> actions;

  @JsonKey(name: _kHighlightFirstButton)
  final bool highlightFirstButton;

  @JsonKey(name: _kHighlightSecondaryButton)
  final bool highlightSecondaryButton;

  const TicketActionEntity({
    required this.actions,
    this.highlightFirstButton = false,
    this.highlightSecondaryButton = false,
  });

  factory TicketActionEntity.fromJson(Map<String, dynamic> json) =>
      _$TicketActionEntityFromJson(json);

  @override
  String toString() {
    return '''Test TicketActionEntity result:\n
      button: $actions,
      highlightFirstButton: $highlightFirstButton,
      highlightSecondaryButton: $highlightSecondaryButton,
    ''';
  }
}

// void testTicketAction() {
//   final actions = TicketAction.values.orderByPriority();
//   logDebug('TickAction order by priority -> ${actions.printPriority()}');

//   final TicketListResponse ticketListResponse =
//       ApiResponseV2<TicketListResponse>.fromJson(
//     ticketDetailsTest,
//     (p0) => TicketListResponse.fromJson(p0),
//   ).data;

//   final TicketNodeResponse nodeResponse =
//       ApiResponseV2<TicketNodeResponse>.fromJson(
//     nodeDataTest,
//     (p0) => TicketNodeResponse.fromJson(p0),
//   ).data;

//   final TicketFlowChartResponse flowChartResponse =
//       ApiResponseV2<TicketFlowChartResponse>.fromJson(
//     ticketFlowChatTest,
//     (p0) => TicketFlowChartResponse.fromJson(p0),
//   ).data;

// final GPTicketMapper mapper =
//     GetIt.I<GPTicketMapper>(instanceName: 'kGPTicketMapper');

//   final ticketEntity =
//       mapper.convert<TicketListResponse, TicketEntity>(ticketListResponse);
//   final ticketNodeEntity =
//       mapper.convert<TicketNodeResponse, TicketNodeEntity>(nodeResponse);
//   final ticketFlowChartEntity =
//       mapper.convert<TicketFlowChartResponse, TicketFlowChartEntity>(
//           flowChartResponse);

//   final TicketActionEntity? ticketActionEntity =
//     TicketPermission().getTicketActions(
//   ticketEntity: ticketEntity,
//   ticketNodeEntity: ticketNodeEntity,
//   ticketFlowChartEntity: ticketFlowChartEntity,
// );

//   logDebug('ticketActionEntity -> ${ticketActionEntity.toString()}');
// }
