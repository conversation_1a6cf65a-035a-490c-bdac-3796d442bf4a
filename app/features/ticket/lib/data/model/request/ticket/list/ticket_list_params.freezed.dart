// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_list_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketListParams {
  @JsonKey(name: 'tab_ids', to<PERSON><PERSON>: _tabIdsToJson)
  List<dynamic>? get tabIds;

  ///
  @JsonKey(name: 'creator_ids', toJson: _creatorIdsToJson)
  List<int>? get creatorIds;

  ///
  @JsonKey(name: 'ticket_statuses', toJson: _ticketStatusesToJson)
  List<int>? get ticketStatuses;
  @JsonKey(name: 'workflow_ids', toJson: _workflowIdToJson)
  List<int>? get workflowIds;
  @JsonKey(name: 'assignee_ids', toJson: _assigneeIdsToJson)
  List<int>? get assigneeIds;
  @JsonKey(name: 'node_statuses', toJson: _nodeStatusesToJson)
  List<int>? get nodeStatuses;
  @JsonKey(name: 'on_hold_request_statuses', toJson: _onHoldStatusesToJson)
  List<int>? get onHoldStatuses;
  @JsonKey(name: 'priorities', toJson: _prioritiesToJson)
  List<int>? get priorities;
  @JsonKey(name: 'start_time')
  String? get startTime;
  @JsonKey(name: 'end_time')
  String? get endTime;
  @JsonKey(name: 'user_type', toJson: _homeMenuEnumToJson)
  TicketHomeMenuEnums? get homeMenuEnum;
  int get page;
  int get limit;
  String? get sort;
  @JsonKey(name: 'is_overdue')
  bool? get isOverdue;
  @JsonKey(name: 'ticket_title')
  String? get search;
  @JsonKey(name: 'ticket_code')
  String? get searchByCode;

  /// Create a copy of TicketListParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TicketListParamsCopyWith<TicketListParams> get copyWith =>
      _$TicketListParamsCopyWithImpl<TicketListParams>(
          this as TicketListParams, _$identity);

  /// Serializes this TicketListParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketListParams &&
            const DeepCollectionEquality().equals(other.tabIds, tabIds) &&
            const DeepCollectionEquality()
                .equals(other.creatorIds, creatorIds) &&
            const DeepCollectionEquality()
                .equals(other.ticketStatuses, ticketStatuses) &&
            const DeepCollectionEquality()
                .equals(other.workflowIds, workflowIds) &&
            const DeepCollectionEquality()
                .equals(other.assigneeIds, assigneeIds) &&
            const DeepCollectionEquality()
                .equals(other.nodeStatuses, nodeStatuses) &&
            const DeepCollectionEquality()
                .equals(other.onHoldStatuses, onHoldStatuses) &&
            const DeepCollectionEquality()
                .equals(other.priorities, priorities) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.homeMenuEnum, homeMenuEnum) ||
                other.homeMenuEnum == homeMenuEnum) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            (identical(other.isOverdue, isOverdue) ||
                other.isOverdue == isOverdue) &&
            (identical(other.search, search) || other.search == search) &&
            (identical(other.searchByCode, searchByCode) ||
                other.searchByCode == searchByCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(tabIds),
      const DeepCollectionEquality().hash(creatorIds),
      const DeepCollectionEquality().hash(ticketStatuses),
      const DeepCollectionEquality().hash(workflowIds),
      const DeepCollectionEquality().hash(assigneeIds),
      const DeepCollectionEquality().hash(nodeStatuses),
      const DeepCollectionEquality().hash(onHoldStatuses),
      const DeepCollectionEquality().hash(priorities),
      startTime,
      endTime,
      homeMenuEnum,
      page,
      limit,
      sort,
      isOverdue,
      search,
      searchByCode);

  @override
  String toString() {
    return 'TicketListParams(tabIds: $tabIds, creatorIds: $creatorIds, ticketStatuses: $ticketStatuses, workflowIds: $workflowIds, assigneeIds: $assigneeIds, nodeStatuses: $nodeStatuses, onHoldStatuses: $onHoldStatuses, priorities: $priorities, startTime: $startTime, endTime: $endTime, homeMenuEnum: $homeMenuEnum, page: $page, limit: $limit, sort: $sort, isOverdue: $isOverdue, search: $search, searchByCode: $searchByCode)';
  }
}

/// @nodoc
abstract mixin class $TicketListParamsCopyWith<$Res> {
  factory $TicketListParamsCopyWith(
          TicketListParams value, $Res Function(TicketListParams) _then) =
      _$TicketListParamsCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'tab_ids', toJson: _tabIdsToJson) List<dynamic>? tabIds,
      @JsonKey(name: 'creator_ids', toJson: _creatorIdsToJson)
      List<int>? creatorIds,
      @JsonKey(name: 'ticket_statuses', toJson: _ticketStatusesToJson)
      List<int>? ticketStatuses,
      @JsonKey(name: 'workflow_ids', toJson: _workflowIdToJson)
      List<int>? workflowIds,
      @JsonKey(name: 'assignee_ids', toJson: _assigneeIdsToJson)
      List<int>? assigneeIds,
      @JsonKey(name: 'node_statuses', toJson: _nodeStatusesToJson)
      List<int>? nodeStatuses,
      @JsonKey(name: 'on_hold_request_statuses', toJson: _onHoldStatusesToJson)
      List<int>? onHoldStatuses,
      @JsonKey(name: 'priorities', toJson: _prioritiesToJson)
      List<int>? priorities,
      @JsonKey(name: 'start_time') String? startTime,
      @JsonKey(name: 'end_time') String? endTime,
      @JsonKey(name: 'user_type', toJson: _homeMenuEnumToJson)
      TicketHomeMenuEnums? homeMenuEnum,
      int page,
      int limit,
      String? sort,
      @JsonKey(name: 'is_overdue') bool? isOverdue,
      @JsonKey(name: 'ticket_title') String? search,
      @JsonKey(name: 'ticket_code') String? searchByCode});
}

/// @nodoc
class _$TicketListParamsCopyWithImpl<$Res>
    implements $TicketListParamsCopyWith<$Res> {
  _$TicketListParamsCopyWithImpl(this._self, this._then);

  final TicketListParams _self;
  final $Res Function(TicketListParams) _then;

  /// Create a copy of TicketListParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabIds = freezed,
    Object? creatorIds = freezed,
    Object? ticketStatuses = freezed,
    Object? workflowIds = freezed,
    Object? assigneeIds = freezed,
    Object? nodeStatuses = freezed,
    Object? onHoldStatuses = freezed,
    Object? priorities = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? homeMenuEnum = freezed,
    Object? page = null,
    Object? limit = null,
    Object? sort = freezed,
    Object? isOverdue = freezed,
    Object? search = freezed,
    Object? searchByCode = freezed,
  }) {
    return _then(_self.copyWith(
      tabIds: freezed == tabIds
          ? _self.tabIds
          : tabIds // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      creatorIds: freezed == creatorIds
          ? _self.creatorIds
          : creatorIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      ticketStatuses: freezed == ticketStatuses
          ? _self.ticketStatuses
          : ticketStatuses // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      workflowIds: freezed == workflowIds
          ? _self.workflowIds
          : workflowIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      assigneeIds: freezed == assigneeIds
          ? _self.assigneeIds
          : assigneeIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      nodeStatuses: freezed == nodeStatuses
          ? _self.nodeStatuses
          : nodeStatuses // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      onHoldStatuses: freezed == onHoldStatuses
          ? _self.onHoldStatuses
          : onHoldStatuses // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      priorities: freezed == priorities
          ? _self.priorities
          : priorities // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      startTime: freezed == startTime
          ? _self.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _self.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
      homeMenuEnum: freezed == homeMenuEnum
          ? _self.homeMenuEnum
          : homeMenuEnum // ignore: cast_nullable_to_non_nullable
              as TicketHomeMenuEnums?,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      sort: freezed == sort
          ? _self.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as String?,
      isOverdue: freezed == isOverdue
          ? _self.isOverdue
          : isOverdue // ignore: cast_nullable_to_non_nullable
              as bool?,
      search: freezed == search
          ? _self.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
      searchByCode: freezed == searchByCode
          ? _self.searchByCode
          : searchByCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketListParams implements TicketListParams {
  _TicketListParams(
      {@JsonKey(name: 'tab_ids', toJson: _tabIdsToJson)
      final List<dynamic>? tabIds,
      @JsonKey(name: 'creator_ids', toJson: _creatorIdsToJson)
      final List<int>? creatorIds,
      @JsonKey(name: 'ticket_statuses', toJson: _ticketStatusesToJson)
      final List<int>? ticketStatuses,
      @JsonKey(name: 'workflow_ids', toJson: _workflowIdToJson)
      final List<int>? workflowIds,
      @JsonKey(name: 'assignee_ids', toJson: _assigneeIdsToJson)
      final List<int>? assigneeIds,
      @JsonKey(name: 'node_statuses', toJson: _nodeStatusesToJson)
      final List<int>? nodeStatuses,
      @JsonKey(name: 'on_hold_request_statuses', toJson: _onHoldStatusesToJson)
      final List<int>? onHoldStatuses,
      @JsonKey(name: 'priorities', toJson: _prioritiesToJson)
      final List<int>? priorities,
      @JsonKey(name: 'start_time') this.startTime,
      @JsonKey(name: 'end_time') this.endTime,
      @JsonKey(name: 'user_type', toJson: _homeMenuEnumToJson)
      this.homeMenuEnum,
      this.page = 0,
      this.limit = 10,
      this.sort = '5:desc',
      @JsonKey(name: 'is_overdue') this.isOverdue,
      @JsonKey(name: 'ticket_title') this.search,
      @JsonKey(name: 'ticket_code') this.searchByCode})
      : _tabIds = tabIds,
        _creatorIds = creatorIds,
        _ticketStatuses = ticketStatuses,
        _workflowIds = workflowIds,
        _assigneeIds = assigneeIds,
        _nodeStatuses = nodeStatuses,
        _onHoldStatuses = onHoldStatuses,
        _priorities = priorities;

  final List<dynamic>? _tabIds;
  @override
  @JsonKey(name: 'tab_ids', toJson: _tabIdsToJson)
  List<dynamic>? get tabIds {
    final value = _tabIds;
    if (value == null) return null;
    if (_tabIds is EqualUnmodifiableListView) return _tabIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  ///
  final List<int>? _creatorIds;

  ///
  @override
  @JsonKey(name: 'creator_ids', toJson: _creatorIdsToJson)
  List<int>? get creatorIds {
    final value = _creatorIds;
    if (value == null) return null;
    if (_creatorIds is EqualUnmodifiableListView) return _creatorIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  ///
  final List<int>? _ticketStatuses;

  ///
  @override
  @JsonKey(name: 'ticket_statuses', toJson: _ticketStatusesToJson)
  List<int>? get ticketStatuses {
    final value = _ticketStatuses;
    if (value == null) return null;
    if (_ticketStatuses is EqualUnmodifiableListView) return _ticketStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _workflowIds;
  @override
  @JsonKey(name: 'workflow_ids', toJson: _workflowIdToJson)
  List<int>? get workflowIds {
    final value = _workflowIds;
    if (value == null) return null;
    if (_workflowIds is EqualUnmodifiableListView) return _workflowIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _assigneeIds;
  @override
  @JsonKey(name: 'assignee_ids', toJson: _assigneeIdsToJson)
  List<int>? get assigneeIds {
    final value = _assigneeIds;
    if (value == null) return null;
    if (_assigneeIds is EqualUnmodifiableListView) return _assigneeIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _nodeStatuses;
  @override
  @JsonKey(name: 'node_statuses', toJson: _nodeStatusesToJson)
  List<int>? get nodeStatuses {
    final value = _nodeStatuses;
    if (value == null) return null;
    if (_nodeStatuses is EqualUnmodifiableListView) return _nodeStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _onHoldStatuses;
  @override
  @JsonKey(name: 'on_hold_request_statuses', toJson: _onHoldStatusesToJson)
  List<int>? get onHoldStatuses {
    final value = _onHoldStatuses;
    if (value == null) return null;
    if (_onHoldStatuses is EqualUnmodifiableListView) return _onHoldStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _priorities;
  @override
  @JsonKey(name: 'priorities', toJson: _prioritiesToJson)
  List<int>? get priorities {
    final value = _priorities;
    if (value == null) return null;
    if (_priorities is EqualUnmodifiableListView) return _priorities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'start_time')
  final String? startTime;
  @override
  @JsonKey(name: 'end_time')
  final String? endTime;
  @override
  @JsonKey(name: 'user_type', toJson: _homeMenuEnumToJson)
  final TicketHomeMenuEnums? homeMenuEnum;
  @override
  @JsonKey()
  final int page;
  @override
  @JsonKey()
  final int limit;
  @override
  @JsonKey()
  final String? sort;
  @override
  @JsonKey(name: 'is_overdue')
  final bool? isOverdue;
  @override
  @JsonKey(name: 'ticket_title')
  final String? search;
  @override
  @JsonKey(name: 'ticket_code')
  final String? searchByCode;

  /// Create a copy of TicketListParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TicketListParamsCopyWith<_TicketListParams> get copyWith =>
      __$TicketListParamsCopyWithImpl<_TicketListParams>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TicketListParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketListParams &&
            const DeepCollectionEquality().equals(other._tabIds, _tabIds) &&
            const DeepCollectionEquality()
                .equals(other._creatorIds, _creatorIds) &&
            const DeepCollectionEquality()
                .equals(other._ticketStatuses, _ticketStatuses) &&
            const DeepCollectionEquality()
                .equals(other._workflowIds, _workflowIds) &&
            const DeepCollectionEquality()
                .equals(other._assigneeIds, _assigneeIds) &&
            const DeepCollectionEquality()
                .equals(other._nodeStatuses, _nodeStatuses) &&
            const DeepCollectionEquality()
                .equals(other._onHoldStatuses, _onHoldStatuses) &&
            const DeepCollectionEquality()
                .equals(other._priorities, _priorities) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.homeMenuEnum, homeMenuEnum) ||
                other.homeMenuEnum == homeMenuEnum) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            (identical(other.isOverdue, isOverdue) ||
                other.isOverdue == isOverdue) &&
            (identical(other.search, search) || other.search == search) &&
            (identical(other.searchByCode, searchByCode) ||
                other.searchByCode == searchByCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_tabIds),
      const DeepCollectionEquality().hash(_creatorIds),
      const DeepCollectionEquality().hash(_ticketStatuses),
      const DeepCollectionEquality().hash(_workflowIds),
      const DeepCollectionEquality().hash(_assigneeIds),
      const DeepCollectionEquality().hash(_nodeStatuses),
      const DeepCollectionEquality().hash(_onHoldStatuses),
      const DeepCollectionEquality().hash(_priorities),
      startTime,
      endTime,
      homeMenuEnum,
      page,
      limit,
      sort,
      isOverdue,
      search,
      searchByCode);

  @override
  String toString() {
    return 'TicketListParams(tabIds: $tabIds, creatorIds: $creatorIds, ticketStatuses: $ticketStatuses, workflowIds: $workflowIds, assigneeIds: $assigneeIds, nodeStatuses: $nodeStatuses, onHoldStatuses: $onHoldStatuses, priorities: $priorities, startTime: $startTime, endTime: $endTime, homeMenuEnum: $homeMenuEnum, page: $page, limit: $limit, sort: $sort, isOverdue: $isOverdue, search: $search, searchByCode: $searchByCode)';
  }
}

/// @nodoc
abstract mixin class _$TicketListParamsCopyWith<$Res>
    implements $TicketListParamsCopyWith<$Res> {
  factory _$TicketListParamsCopyWith(
          _TicketListParams value, $Res Function(_TicketListParams) _then) =
      __$TicketListParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'tab_ids', toJson: _tabIdsToJson) List<dynamic>? tabIds,
      @JsonKey(name: 'creator_ids', toJson: _creatorIdsToJson)
      List<int>? creatorIds,
      @JsonKey(name: 'ticket_statuses', toJson: _ticketStatusesToJson)
      List<int>? ticketStatuses,
      @JsonKey(name: 'workflow_ids', toJson: _workflowIdToJson)
      List<int>? workflowIds,
      @JsonKey(name: 'assignee_ids', toJson: _assigneeIdsToJson)
      List<int>? assigneeIds,
      @JsonKey(name: 'node_statuses', toJson: _nodeStatusesToJson)
      List<int>? nodeStatuses,
      @JsonKey(name: 'on_hold_request_statuses', toJson: _onHoldStatusesToJson)
      List<int>? onHoldStatuses,
      @JsonKey(name: 'priorities', toJson: _prioritiesToJson)
      List<int>? priorities,
      @JsonKey(name: 'start_time') String? startTime,
      @JsonKey(name: 'end_time') String? endTime,
      @JsonKey(name: 'user_type', toJson: _homeMenuEnumToJson)
      TicketHomeMenuEnums? homeMenuEnum,
      int page,
      int limit,
      String? sort,
      @JsonKey(name: 'is_overdue') bool? isOverdue,
      @JsonKey(name: 'ticket_title') String? search,
      @JsonKey(name: 'ticket_code') String? searchByCode});
}

/// @nodoc
class __$TicketListParamsCopyWithImpl<$Res>
    implements _$TicketListParamsCopyWith<$Res> {
  __$TicketListParamsCopyWithImpl(this._self, this._then);

  final _TicketListParams _self;
  final $Res Function(_TicketListParams) _then;

  /// Create a copy of TicketListParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? tabIds = freezed,
    Object? creatorIds = freezed,
    Object? ticketStatuses = freezed,
    Object? workflowIds = freezed,
    Object? assigneeIds = freezed,
    Object? nodeStatuses = freezed,
    Object? onHoldStatuses = freezed,
    Object? priorities = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? homeMenuEnum = freezed,
    Object? page = null,
    Object? limit = null,
    Object? sort = freezed,
    Object? isOverdue = freezed,
    Object? search = freezed,
    Object? searchByCode = freezed,
  }) {
    return _then(_TicketListParams(
      tabIds: freezed == tabIds
          ? _self._tabIds
          : tabIds // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      creatorIds: freezed == creatorIds
          ? _self._creatorIds
          : creatorIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      ticketStatuses: freezed == ticketStatuses
          ? _self._ticketStatuses
          : ticketStatuses // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      workflowIds: freezed == workflowIds
          ? _self._workflowIds
          : workflowIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      assigneeIds: freezed == assigneeIds
          ? _self._assigneeIds
          : assigneeIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      nodeStatuses: freezed == nodeStatuses
          ? _self._nodeStatuses
          : nodeStatuses // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      onHoldStatuses: freezed == onHoldStatuses
          ? _self._onHoldStatuses
          : onHoldStatuses // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      priorities: freezed == priorities
          ? _self._priorities
          : priorities // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      startTime: freezed == startTime
          ? _self.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _self.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
      homeMenuEnum: freezed == homeMenuEnum
          ? _self.homeMenuEnum
          : homeMenuEnum // ignore: cast_nullable_to_non_nullable
              as TicketHomeMenuEnums?,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      sort: freezed == sort
          ? _self.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as String?,
      isOverdue: freezed == isOverdue
          ? _self.isOverdue
          : isOverdue // ignore: cast_nullable_to_non_nullable
              as bool?,
      search: freezed == search
          ? _self.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
      searchByCode: freezed == searchByCode
          ? _self.searchByCode
          : searchByCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
