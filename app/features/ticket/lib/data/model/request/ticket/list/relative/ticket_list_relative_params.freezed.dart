// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_list_relative_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketListRelativeParams {
  @JsonKey(name: 'ticket_title')
  String? get search; //
  @JsonKey(name: 'user_type')
  int get userType; //
  int get limit; //
  int get page;

  /// Create a copy of TicketListRelativeParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TicketListRelativeParamsCopyWith<TicketListRelativeParams> get copyWith =>
      _$TicketListRelativeParamsCopyWithImpl<TicketListRelativeParams>(
          this as TicketListRelativeParams, _$identity);

  /// Serializes this TicketListRelativeParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketListRelativeParams &&
            (identical(other.search, search) || other.search == search) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.page, page) || other.page == page));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, search, userType, limit, page);

  @override
  String toString() {
    return 'TicketListRelativeParams(search: $search, userType: $userType, limit: $limit, page: $page)';
  }
}

/// @nodoc
abstract mixin class $TicketListRelativeParamsCopyWith<$Res> {
  factory $TicketListRelativeParamsCopyWith(TicketListRelativeParams value,
          $Res Function(TicketListRelativeParams) _then) =
      _$TicketListRelativeParamsCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'ticket_title') String? search,
      @JsonKey(name: 'user_type') int userType,
      int limit,
      int page});
}

/// @nodoc
class _$TicketListRelativeParamsCopyWithImpl<$Res>
    implements $TicketListRelativeParamsCopyWith<$Res> {
  _$TicketListRelativeParamsCopyWithImpl(this._self, this._then);

  final TicketListRelativeParams _self;
  final $Res Function(TicketListRelativeParams) _then;

  /// Create a copy of TicketListRelativeParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? search = freezed,
    Object? userType = null,
    Object? limit = null,
    Object? page = null,
  }) {
    return _then(_self.copyWith(
      search: freezed == search
          ? _self.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
      userType: null == userType
          ? _self.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketListRelativeParams implements TicketListRelativeParams {
  _TicketListRelativeParams(
      {@JsonKey(name: 'ticket_title') this.search,
      @JsonKey(name: 'user_type') this.userType = 9,
      this.limit = 10,
      this.page = 1});

  @override
  @JsonKey(name: 'ticket_title')
  final String? search;
//
  @override
  @JsonKey(name: 'user_type')
  final int userType;
//
  @override
  @JsonKey()
  final int limit;
//
  @override
  @JsonKey()
  final int page;

  /// Create a copy of TicketListRelativeParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TicketListRelativeParamsCopyWith<_TicketListRelativeParams> get copyWith =>
      __$TicketListRelativeParamsCopyWithImpl<_TicketListRelativeParams>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TicketListRelativeParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketListRelativeParams &&
            (identical(other.search, search) || other.search == search) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.page, page) || other.page == page));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, search, userType, limit, page);

  @override
  String toString() {
    return 'TicketListRelativeParams(search: $search, userType: $userType, limit: $limit, page: $page)';
  }
}

/// @nodoc
abstract mixin class _$TicketListRelativeParamsCopyWith<$Res>
    implements $TicketListRelativeParamsCopyWith<$Res> {
  factory _$TicketListRelativeParamsCopyWith(_TicketListRelativeParams value,
          $Res Function(_TicketListRelativeParams) _then) =
      __$TicketListRelativeParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'ticket_title') String? search,
      @JsonKey(name: 'user_type') int userType,
      int limit,
      int page});
}

/// @nodoc
class __$TicketListRelativeParamsCopyWithImpl<$Res>
    implements _$TicketListRelativeParamsCopyWith<$Res> {
  __$TicketListRelativeParamsCopyWithImpl(this._self, this._then);

  final _TicketListRelativeParams _self;
  final $Res Function(_TicketListRelativeParams) _then;

  /// Create a copy of TicketListRelativeParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? search = freezed,
    Object? userType = null,
    Object? limit = null,
    Object? page = null,
  }) {
    return _then(_TicketListRelativeParams(
      search: freezed == search
          ? _self.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
      userType: null == userType
          ? _self.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
