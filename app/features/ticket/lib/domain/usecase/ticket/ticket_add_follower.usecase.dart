/*
 * Created Date: Tuesday, 22nd October 2024, 15:49:20
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 22nd October 2024 15:50:43
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../../data/data.dart';
import '../../domain.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketAddFollowerUseCase extends GPBaseFutureUseCase<
    TicketAddFollowerInput, ApiResponseV2<dynamic>> {
  TicketAddFollowerUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<ApiResponseV2<dynamic>> buildUseCase(
    TicketAddFollowerInput input,
  ) async {
    return _ticketRepository.follower(followerInput: input);
  }
}

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketAddFollowerAllStepUseCase
    extends GPBaseFutureUseCase<TicketAddFollowerInput, GPBaseOutput> {
  TicketAddFollowerAllStepUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<GPBaseOutput> buildUseCase(
    TicketAddFollowerInput input,
  ) async {
    await _ticketRepository.addFollowerAllStep(followerInput: input);
    return const TicketAddFollowerAllStepOutput();
  }
}

class TicketAddFollowerInput extends GPBaseInput {
  const TicketAddFollowerInput({
    required this.ticketId,
    required this.nodeId,
    required this.params,
  });

  final String ticketId;
  final String nodeId;

  final TicketFollowerParams params;
}

class TicketAddFollowerAllStepOutput extends GPBaseOutput {
  const TicketAddFollowerAllStepOutput();
}
