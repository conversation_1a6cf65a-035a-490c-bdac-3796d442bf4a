// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppDefaultState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AppDefaultState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AppDefaultState()';
  }
}

/// @nodoc
class $AppDefaultStateCopyWith<$Res> {
  $AppDefaultStateCopyWith(
      AppDefaultState _, $Res Function(AppDefaultState) __);
}

/// @nodoc

class _AppDefaultState extends AppDefaultState {
  const _AppDefaultState() : super._();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _AppDefaultState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AppDefaultState()';
  }
}

/// @nodoc
class _$AppDefaultStateCopyWith<$Res>
    implements $AppDefaultStateCopyWith<$Res> {
  _$AppDefaultStateCopyWith(
      _AppDefaultState _, $Res Function(_AppDefaultState) __);
}

/// @nodoc
class __$AppDefaultStateCopyWithImpl<$Res>
    implements _$AppDefaultStateCopyWith<$Res> {
  __$AppDefaultStateCopyWithImpl(this._self, this._then);

  final _AppDefaultState _self;
  final $Res Function(_AppDefaultState) _then;
}

// dart format on
