// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_redo_previous.params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketRedoPreviousParams {
  @JsonKey(name: 'reason')
  String get reason;
  @JsonKey(name: 'node_ids')
  List<int> get nodeIds;
  @JsonKey(name: 'ticket_id')
  int get ticketId;
  @JsonKey(name: 'id')
  int get id;

  /// Serializes this TicketRedoPreviousParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketRedoPreviousParams &&
            (identical(other.reason, reason) || other.reason == reason) &&
            const DeepCollectionEquality().equals(other.nodeIds, nodeIds) &&
            (identical(other.ticketId, ticketId) ||
                other.ticketId == ticketId) &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, reason,
      const DeepCollectionEquality().hash(nodeIds), ticketId, id);

  @override
  String toString() {
    return 'TicketRedoPreviousParams(reason: $reason, nodeIds: $nodeIds, ticketId: $ticketId, id: $id)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketRedoPreviousParams implements TicketRedoPreviousParams {
  _TicketRedoPreviousParams(
      {@JsonKey(name: 'reason') required this.reason,
      @JsonKey(name: 'node_ids') required final List<int> nodeIds,
      @JsonKey(name: 'ticket_id') required this.ticketId,
      @JsonKey(name: 'id') required this.id})
      : _nodeIds = nodeIds;

  @override
  @JsonKey(name: 'reason')
  final String reason;
  final List<int> _nodeIds;
  @override
  @JsonKey(name: 'node_ids')
  List<int> get nodeIds {
    if (_nodeIds is EqualUnmodifiableListView) return _nodeIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_nodeIds);
  }

  @override
  @JsonKey(name: 'ticket_id')
  final int ticketId;
  @override
  @JsonKey(name: 'id')
  final int id;

  @override
  Map<String, dynamic> toJson() {
    return _$TicketRedoPreviousParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketRedoPreviousParams &&
            (identical(other.reason, reason) || other.reason == reason) &&
            const DeepCollectionEquality().equals(other._nodeIds, _nodeIds) &&
            (identical(other.ticketId, ticketId) ||
                other.ticketId == ticketId) &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, reason,
      const DeepCollectionEquality().hash(_nodeIds), ticketId, id);

  @override
  String toString() {
    return 'TicketRedoPreviousParams(reason: $reason, nodeIds: $nodeIds, ticketId: $ticketId, id: $id)';
  }
}

// dart format on
