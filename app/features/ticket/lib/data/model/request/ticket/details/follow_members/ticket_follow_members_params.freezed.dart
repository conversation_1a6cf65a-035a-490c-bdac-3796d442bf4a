// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_follow_members_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketFollowMembersParams {
  @JsonKey(name: 'user_ids')
  List<String>? get userIds;
  @JsonKey(name: 'collab_ids')
  List<String>? get collabIds;
  @JsonKey(name: 'department_ids')
  List<String>? get departmentIds;
  @JsonKey(name: 'role_ids')
  List<String>? get roleIds;

  /// Create a copy of TicketFollowMembersParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TicketFollowMembersParamsCopyWith<TicketFollowMembersParams> get copyWith =>
      _$TicketFollowMembersParamsCopyWithImpl<TicketFollowMembersParams>(
          this as TicketFollowMembersParams, _$identity);

  /// Serializes this TicketFollowMembersParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketFollowMembersParams &&
            const DeepCollectionEquality().equals(other.userIds, userIds) &&
            const DeepCollectionEquality().equals(other.collabIds, collabIds) &&
            const DeepCollectionEquality()
                .equals(other.departmentIds, departmentIds) &&
            const DeepCollectionEquality().equals(other.roleIds, roleIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(userIds),
      const DeepCollectionEquality().hash(collabIds),
      const DeepCollectionEquality().hash(departmentIds),
      const DeepCollectionEquality().hash(roleIds));

  @override
  String toString() {
    return 'TicketFollowMembersParams(userIds: $userIds, collabIds: $collabIds, departmentIds: $departmentIds, roleIds: $roleIds)';
  }
}

/// @nodoc
abstract mixin class $TicketFollowMembersParamsCopyWith<$Res> {
  factory $TicketFollowMembersParamsCopyWith(TicketFollowMembersParams value,
          $Res Function(TicketFollowMembersParams) _then) =
      _$TicketFollowMembersParamsCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'user_ids') List<String>? userIds,
      @JsonKey(name: 'collab_ids') List<String>? collabIds,
      @JsonKey(name: 'department_ids') List<String>? departmentIds,
      @JsonKey(name: 'role_ids') List<String>? roleIds});
}

/// @nodoc
class _$TicketFollowMembersParamsCopyWithImpl<$Res>
    implements $TicketFollowMembersParamsCopyWith<$Res> {
  _$TicketFollowMembersParamsCopyWithImpl(this._self, this._then);

  final TicketFollowMembersParams _self;
  final $Res Function(TicketFollowMembersParams) _then;

  /// Create a copy of TicketFollowMembersParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userIds = freezed,
    Object? collabIds = freezed,
    Object? departmentIds = freezed,
    Object? roleIds = freezed,
  }) {
    return _then(_self.copyWith(
      userIds: freezed == userIds
          ? _self.userIds
          : userIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      collabIds: freezed == collabIds
          ? _self.collabIds
          : collabIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      departmentIds: freezed == departmentIds
          ? _self.departmentIds
          : departmentIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      roleIds: freezed == roleIds
          ? _self.roleIds
          : roleIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketFollowMembersParams implements TicketFollowMembersParams {
  const _TicketFollowMembersParams(
      {@JsonKey(name: 'user_ids') final List<String>? userIds,
      @JsonKey(name: 'collab_ids') final List<String>? collabIds,
      @JsonKey(name: 'department_ids') final List<String>? departmentIds,
      @JsonKey(name: 'role_ids') final List<String>? roleIds})
      : _userIds = userIds,
        _collabIds = collabIds,
        _departmentIds = departmentIds,
        _roleIds = roleIds;

  final List<String>? _userIds;
  @override
  @JsonKey(name: 'user_ids')
  List<String>? get userIds {
    final value = _userIds;
    if (value == null) return null;
    if (_userIds is EqualUnmodifiableListView) return _userIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _collabIds;
  @override
  @JsonKey(name: 'collab_ids')
  List<String>? get collabIds {
    final value = _collabIds;
    if (value == null) return null;
    if (_collabIds is EqualUnmodifiableListView) return _collabIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _departmentIds;
  @override
  @JsonKey(name: 'department_ids')
  List<String>? get departmentIds {
    final value = _departmentIds;
    if (value == null) return null;
    if (_departmentIds is EqualUnmodifiableListView) return _departmentIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _roleIds;
  @override
  @JsonKey(name: 'role_ids')
  List<String>? get roleIds {
    final value = _roleIds;
    if (value == null) return null;
    if (_roleIds is EqualUnmodifiableListView) return _roleIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of TicketFollowMembersParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TicketFollowMembersParamsCopyWith<_TicketFollowMembersParams>
      get copyWith =>
          __$TicketFollowMembersParamsCopyWithImpl<_TicketFollowMembersParams>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TicketFollowMembersParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketFollowMembersParams &&
            const DeepCollectionEquality().equals(other._userIds, _userIds) &&
            const DeepCollectionEquality()
                .equals(other._collabIds, _collabIds) &&
            const DeepCollectionEquality()
                .equals(other._departmentIds, _departmentIds) &&
            const DeepCollectionEquality().equals(other._roleIds, _roleIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_userIds),
      const DeepCollectionEquality().hash(_collabIds),
      const DeepCollectionEquality().hash(_departmentIds),
      const DeepCollectionEquality().hash(_roleIds));

  @override
  String toString() {
    return 'TicketFollowMembersParams(userIds: $userIds, collabIds: $collabIds, departmentIds: $departmentIds, roleIds: $roleIds)';
  }
}

/// @nodoc
abstract mixin class _$TicketFollowMembersParamsCopyWith<$Res>
    implements $TicketFollowMembersParamsCopyWith<$Res> {
  factory _$TicketFollowMembersParamsCopyWith(_TicketFollowMembersParams value,
          $Res Function(_TicketFollowMembersParams) _then) =
      __$TicketFollowMembersParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'user_ids') List<String>? userIds,
      @JsonKey(name: 'collab_ids') List<String>? collabIds,
      @JsonKey(name: 'department_ids') List<String>? departmentIds,
      @JsonKey(name: 'role_ids') List<String>? roleIds});
}

/// @nodoc
class __$TicketFollowMembersParamsCopyWithImpl<$Res>
    implements _$TicketFollowMembersParamsCopyWith<$Res> {
  __$TicketFollowMembersParamsCopyWithImpl(this._self, this._then);

  final _TicketFollowMembersParams _self;
  final $Res Function(_TicketFollowMembersParams) _then;

  /// Create a copy of TicketFollowMembersParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userIds = freezed,
    Object? collabIds = freezed,
    Object? departmentIds = freezed,
    Object? roleIds = freezed,
  }) {
    return _then(_TicketFollowMembersParams(
      userIds: freezed == userIds
          ? _self._userIds
          : userIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      collabIds: freezed == collabIds
          ? _self._collabIds
          : collabIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      departmentIds: freezed == departmentIds
          ? _self._departmentIds
          : departmentIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      roleIds: freezed == roleIds
          ? _self._roleIds
          : roleIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

// dart format on
