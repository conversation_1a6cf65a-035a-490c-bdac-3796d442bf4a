import 'package:gp_core/models/assignee.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_feat_ticket/lib.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/action/add_follower_option.dart';

typedef TicketDetailCallback = Function(TicketEntity entity);

final class TicketDetailsTabState extends CoreV2BaseState {
  const TicketDetailsTabState();
}

final class TicketDetailsLoaded extends TicketDetailsTabState {
  const TicketDetailsLoaded({
    required this.entity,
    this.callback,
  });

  final TicketEntity entity;

  /// gọi sau khi get ticket details succes
  final TicketDetailCallback? callback;
}

final class TicketDetailsFlowChartLoaded extends TicketDetailsTabState {
  const TicketDetailsFlowChartLoaded({required this.entity});

  final TicketFlowChartEntity entity;
}

final class TicketDetailsNodeLoaded extends TicketDetailsTabState {
  const TicketDetailsNodeLoaded({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.ticketFlowChartEntity,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketFlowChartEntity ticketFlowChartEntity;
}

final class TicketGetIsTicketAssigneeSuccess extends TicketDetailsTabState {
  const TicketGetIsTicketAssigneeSuccess({
    required this.ticketEntity,
    required this.ticketNodeEntity,
    required this.ticketFlowChartEntity,
    required this.isTicketAssignee,
  });

  final TicketEntity ticketEntity;
  final TicketNodeEntity ticketNodeEntity;
  final TicketFlowChartEntity ticketFlowChartEntity;
  final bool isTicketAssignee;
}

final class TicketAdditionalRequestSuccess extends TicketDetailsTabState {
  const TicketAdditionalRequestSuccess();
}

final class TicketGetActionPermissionsSuccess extends TicketDetailsTabState {
  const TicketGetActionPermissionsSuccess({
    this.ticketActionEntity,
  });

  final TicketActionEntity? ticketActionEntity;
}

final class TicketChangeNodeStatusSuccess extends TicketDetailsTabState {
  const TicketChangeNodeStatusSuccess({
    this.ticketNodeEntity,
    this.nodeStatus,
  });

  final TicketNodeEntity? ticketNodeEntity;
  final TicketNodeStatus? nodeStatus;
}

final class TicketSpamSuccess extends TicketDetailsTabState {
  const TicketSpamSuccess();
}

final class TicketMoveToOnHoldSuccess extends TicketDetailsTabState {
  const TicketMoveToOnHoldSuccess({
    required this.ticketStatus,
  });

  final TicketStatus ticketStatus;
}

final class TicketGetAdditionalRequestsSuccess extends TicketDetailsTabState {
  const TicketGetAdditionalRequestsSuccess({
    required this.ticketAdditionalRequestEntity,
  });

  final List<TicketAdditionalRequestEntity> ticketAdditionalRequestEntity;
}

final class TicketGetOnHoldSuccess extends TicketDetailsTabState {
  const TicketGetOnHoldSuccess({
    required this.ticketOnHoldRequestEntity,
  });

  final TicketOnHoldRequestEntity ticketOnHoldRequestEntity;
}

final class TicketAcceptOnHoldSuccess extends TicketDetailsTabState {
  const TicketAcceptOnHoldSuccess();
}

final class TicketRejectOnHoldSuccess extends TicketDetailsTabState {
  const TicketRejectOnHoldSuccess();
}

final class TicketCancelOnHoldSuccess extends TicketDetailsTabState {
  const TicketCancelOnHoldSuccess();
}

final class TicketUnfollowTicketSuccess extends TicketDetailsTabState {
  const TicketUnfollowTicketSuccess();
}

final class TicketDeleteTicketSuccess extends TicketDetailsTabState {
  const TicketDeleteTicketSuccess();
}

final class TicketConfirmCloseTicketSuccess extends TicketDetailsTabState {
  const TicketConfirmCloseTicketSuccess();
}

final class TicketReopenTicketSuccess extends TicketDetailsTabState {
  const TicketReopenTicketSuccess();
}

final class TicketReviewTicketSuccess extends TicketDetailsTabState {
  const TicketReviewTicketSuccess();
}

final class TicketCancelTicketSuccess extends TicketDetailsTabState {
  const TicketCancelTicketSuccess();
}

final class TicketAddLabelSuccess extends TicketDetailsTabState {
  const TicketAddLabelSuccess();
}

final class TicketBackStepSuccess extends TicketDetailsTabState {
  const TicketBackStepSuccess();
}

final class TicketBackEndStepSuccess extends TicketDetailsTabState {
  const TicketBackEndStepSuccess();
}

final class TicketGetAssigneesSuccess extends TicketDetailsTabState {
  const TicketGetAssigneesSuccess({
    required this.assignees,
  });

  final List<Assignee> assignees;
}

final class TicketGetAdminWorkflowSuccess extends TicketDetailsTabState {
  const TicketGetAdminWorkflowSuccess({
    required this.adminWorkflow,
  });

  final List<Assignee> adminWorkflow;
}

final class TicketAddHandlerSuccess extends TicketDetailsTabState {
  const TicketAddHandlerSuccess();
}

final class TicketChangeHandlerSuccess extends TicketDetailsTabState {
  const TicketChangeHandlerSuccess();
}

final class TicketAddFollowerSuccess extends TicketDetailsTabState {
  const TicketAddFollowerSuccess({
    required this.option,
  });

  final AddFollowerOption option;
}

final class TicketGetFollowMembersSuccess extends TicketDetailsTabState {
  const TicketGetFollowMembersSuccess({
    required this.followMembers,
  });

  final List<Assignee> followMembers;
}
