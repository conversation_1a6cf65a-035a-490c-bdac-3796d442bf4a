# Bash
### Xử lý cả project hiện tại và pub cache
```
./remove_triple_slash_comments.sh
```

### Chỉ xử lý project hiện tại
```
./remove_triple_slash_comments.sh --project
```

### Chỉ xử lý pub cache
```
./remove_triple_slash_comments.sh --cache
```

### Xem trước những file sẽ bị thay đổi (không thực sự thay đổi)
```
./remove_triple_slash_comments.sh --dry-run
```

### Xem hướng dẫn
```
./remove_triple_slash_comments.sh --help
```


# Python
# Xử lý cả project hiện tại và pub cache
```
python3 remove_triple_slash_comments.py
```

# Chỉ xử lý project hiện tại
```
python3 remove_triple_slash_comments.py --project
```

# Chỉ xử lý pub cache
```
python3 remove_triple_slash_comments.py --cache
```

# Xem trước những file sẽ bị thay đổi (không thực sự thay đổi)
```
python3 remove_triple_slash_comments.py --dry-run
```

# Xem hướng dẫn
```
python3 remove_triple_slash_comments.py --help
```