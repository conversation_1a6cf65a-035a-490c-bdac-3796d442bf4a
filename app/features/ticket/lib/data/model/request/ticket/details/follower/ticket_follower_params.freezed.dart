// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_follower_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketFollowerParams {
  @JsonKey(name: 'remove_participants')
  List<TicketParticipantsParams> get removeAssignees;

  ///
  @JsonKey(name: 'add_participants')
  List<TicketParticipantsParams> get addAssignees;

  /// Create a copy of TicketFollowerParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TicketFollowerParamsCopyWith<TicketFollowerParams> get copyWith =>
      _$TicketFollowerParamsCopyWithImpl<TicketFollowerParams>(
          this as TicketFollowerParams, _$identity);

  /// Serializes this TicketFollowerParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketFollowerParams &&
            const DeepCollectionEquality()
                .equals(other.removeAssignees, removeAssignees) &&
            const DeepCollectionEquality()
                .equals(other.addAssignees, addAssignees));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(removeAssignees),
      const DeepCollectionEquality().hash(addAssignees));

  @override
  String toString() {
    return 'TicketFollowerParams(removeAssignees: $removeAssignees, addAssignees: $addAssignees)';
  }
}

/// @nodoc
abstract mixin class $TicketFollowerParamsCopyWith<$Res> {
  factory $TicketFollowerParamsCopyWith(TicketFollowerParams value,
          $Res Function(TicketFollowerParams) _then) =
      _$TicketFollowerParamsCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'remove_participants')
      List<TicketParticipantsParams> removeAssignees,
      @JsonKey(name: 'add_participants')
      List<TicketParticipantsParams> addAssignees});
}

/// @nodoc
class _$TicketFollowerParamsCopyWithImpl<$Res>
    implements $TicketFollowerParamsCopyWith<$Res> {
  _$TicketFollowerParamsCopyWithImpl(this._self, this._then);

  final TicketFollowerParams _self;
  final $Res Function(TicketFollowerParams) _then;

  /// Create a copy of TicketFollowerParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? removeAssignees = null,
    Object? addAssignees = null,
  }) {
    return _then(_self.copyWith(
      removeAssignees: null == removeAssignees
          ? _self.removeAssignees
          : removeAssignees // ignore: cast_nullable_to_non_nullable
              as List<TicketParticipantsParams>,
      addAssignees: null == addAssignees
          ? _self.addAssignees
          : addAssignees // ignore: cast_nullable_to_non_nullable
              as List<TicketParticipantsParams>,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketFollowerParams implements TicketFollowerParams {
  const _TicketFollowerParams(
      {@JsonKey(name: 'remove_participants')
      final List<TicketParticipantsParams> removeAssignees = const [],
      @JsonKey(name: 'add_participants')
      final List<TicketParticipantsParams> addAssignees = const []})
      : _removeAssignees = removeAssignees,
        _addAssignees = addAssignees;

  final List<TicketParticipantsParams> _removeAssignees;
  @override
  @JsonKey(name: 'remove_participants')
  List<TicketParticipantsParams> get removeAssignees {
    if (_removeAssignees is EqualUnmodifiableListView) return _removeAssignees;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_removeAssignees);
  }

  ///
  final List<TicketParticipantsParams> _addAssignees;

  ///
  @override
  @JsonKey(name: 'add_participants')
  List<TicketParticipantsParams> get addAssignees {
    if (_addAssignees is EqualUnmodifiableListView) return _addAssignees;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_addAssignees);
  }

  /// Create a copy of TicketFollowerParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TicketFollowerParamsCopyWith<_TicketFollowerParams> get copyWith =>
      __$TicketFollowerParamsCopyWithImpl<_TicketFollowerParams>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TicketFollowerParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketFollowerParams &&
            const DeepCollectionEquality()
                .equals(other._removeAssignees, _removeAssignees) &&
            const DeepCollectionEquality()
                .equals(other._addAssignees, _addAssignees));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_removeAssignees),
      const DeepCollectionEquality().hash(_addAssignees));

  @override
  String toString() {
    return 'TicketFollowerParams(removeAssignees: $removeAssignees, addAssignees: $addAssignees)';
  }
}

/// @nodoc
abstract mixin class _$TicketFollowerParamsCopyWith<$Res>
    implements $TicketFollowerParamsCopyWith<$Res> {
  factory _$TicketFollowerParamsCopyWith(_TicketFollowerParams value,
          $Res Function(_TicketFollowerParams) _then) =
      __$TicketFollowerParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'remove_participants')
      List<TicketParticipantsParams> removeAssignees,
      @JsonKey(name: 'add_participants')
      List<TicketParticipantsParams> addAssignees});
}

/// @nodoc
class __$TicketFollowerParamsCopyWithImpl<$Res>
    implements _$TicketFollowerParamsCopyWith<$Res> {
  __$TicketFollowerParamsCopyWithImpl(this._self, this._then);

  final _TicketFollowerParams _self;
  final $Res Function(_TicketFollowerParams) _then;

  /// Create a copy of TicketFollowerParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? removeAssignees = null,
    Object? addAssignees = null,
  }) {
    return _then(_TicketFollowerParams(
      removeAssignees: null == removeAssignees
          ? _self._removeAssignees
          : removeAssignees // ignore: cast_nullable_to_non_nullable
              as List<TicketParticipantsParams>,
      addAssignees: null == addAssignees
          ? _self._addAssignees
          : addAssignees // ignore: cast_nullable_to_non_nullable
              as List<TicketParticipantsParams>,
    ));
  }
}

// dart format on
