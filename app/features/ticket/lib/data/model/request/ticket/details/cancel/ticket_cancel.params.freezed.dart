// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_cancel.params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketCancelParams {
  @JsonKey(name: 'ticket_node_id')
  int get ticketNodeId;

  /// Serializes this TicketCancelParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketCancelParams &&
            (identical(other.ticketNodeId, ticketNodeId) ||
                other.ticketNodeId == ticketNodeId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, ticketNodeId);

  @override
  String toString() {
    return 'TicketCancelParams(ticketNodeId: $ticketNodeId)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketCancelParams implements TicketCancelParams {
  _TicketCancelParams(
      {@JsonKey(name: 'ticket_node_id') required this.ticketNodeId});

  @override
  @JsonKey(name: 'ticket_node_id')
  final int ticketNodeId;

  @override
  Map<String, dynamic> toJson() {
    return _$TicketCancelParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketCancelParams &&
            (identical(other.ticketNodeId, ticketNodeId) ||
                other.ticketNodeId == ticketNodeId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, ticketNodeId);

  @override
  String toString() {
    return 'TicketCancelParams(ticketNodeId: $ticketNodeId)';
  }
}

// dart format on
