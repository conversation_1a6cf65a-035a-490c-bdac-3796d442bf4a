/*
 * Created Date: 4/01/2024 14:52:59
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 11th April 2025 18:07:48
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs
// ignore_for_file: use_if_null_to_convert_nulls_to_bools

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:gp_core/models/comment/comment.dart';
import 'package:gp_feat_ticket/domain/entity/workflow/workflow_tag.entity.dart';
import 'package:gp_shared/mapper/entity/assignee_entity_mapper.dart';
import 'package:gp_shared/mapper/entity/attachment_entity_mapper.dart';

import '../../data/data.dart';
import '../../domain/domain.dart';
import 'ticket_entity_mapper.auto_mappr.dart';


@AutoMappr([
  // ---------- TicketList ---------- \\
  MapType<TicketListResponse, TicketEntity>(
    fields: [
      Field(
        'id',
        custom: TicketEntityMapper.mapId,
      ),
    ],
  ),
  MapType<TicketCreatorResponse, TicketCreatorEntity>(),
  MapType<TicketActivityContentResponse, TicketActivityContentEntity>(),
  MapType<TicketActivityContentItemResponse, TicketActivityContentItemEntity>(),
  MapType<TicketHighlightResponse, TicketHighlightEntity>(),
  MapType<RefTicketResponse, RefTicketEntity>(),
  MapType<TicketActivityResponse, TicketActivityEntity>(),
  MapType<TicketShiftDetailResponse, TicketShiftDetailEntity>(),

  MapType<TicketAdditionalRequestResponse, TicketAdditionalRequestEntity>(),
  // SLA
  MapType<TicketSLAResponse, TicketSLAEntity>(),
  MapType<TicketSLAAggResponse, TicketSLAAggEntity>(),
  MapType<TicketShiftAggResponse, TicketShiftAggEntity>(),
  MapType<TicketSLAPriorityLevelsResponse, TicketSLAPriorityLevelsEntity>(),
  MapType<TicketSLAPriorityLevelFirstResponse,
      TicketSLAPriorityLevelFirstEntity>(),
  MapType<TicketSLANotificationResponse, TicketSLANotificationEntity>(),
  MapType<TicketSLANotificationDurationResponse,
      TicketSLANotificationDurationEntity>(),
  MapType<TickeUserRoleResponse, TickeUserRoleEntity>(),
  MapType<TicketFlowChartResponse, TicketFlowChartEntity>(),
  MapType<TicketFlowchartNodeResponse, TicketFlowChartNodeEntity>(),
  MapType<TicketNodeOptionResponse, TicketNodeOptionEntity>(),
  MapType<TicketFlowchartEdgeResponse, TicketFlowchartEdgeEntity>(),
  MapType<TicketNodeResponse, TicketNodeEntity>(),
  MapType<TicketAssigneeResponse, TicketAssigneeEntity>(),
  MapType<WorkflowAdvanceSettingAssigneeResponse,
      WorkflowAdvanceSettingAssigneeEntity>(),
  // Comments
  MapType<TicketCommentResponse, TicketCommentEntity>(fields: [
    Field(
      'comments',
      custom: TicketEntityMapper.mapTicketCommentCommentReplies,
    ),
  ]),
  MapType<CommentAsResponse, CommentAs>(),
  MapType<MentionResponse, Mentions>(),
  MapType<MediasResponse, Medias>(fields: [
    Field('width', whenNull: 0),
    Field('height', whenNull: 0),
  ]),
  MapType<TicketCommentEntity, TicketPostCommentsRequestParams>(),
  MapType<Comment, TicketEditCommentsRequestParams>(),
  MapType<Comment, TicketCommentEntity>(),
  MapType<TicketNodeTagResponse, TicketNodeTagEntity>(),
  // ---------- TicketList -> response ---------- \\
  MapType<TicketEntity, TicketListResponse>(),
  MapType<TicketCreatorEntity, TicketCreatorResponse>(),
  MapType<TicketActivityContentEntity, TicketActivityContentResponse>(),
  MapType<TicketActivityContentItemEntity, TicketActivityContentItemResponse>(),
  MapType<TicketHighlightEntity, TicketHighlightResponse>(),
  MapType<RefTicketEntity, RefTicketResponse>(),
  MapType<TicketActivityEntity, TicketActivityResponse>(),
  MapType<TicketShiftDetailEntity, TicketShiftDetailResponse>(),

  MapType<TicketAdditionalRequestEntity, TicketAdditionalRequestResponse>(),
  MapType<TicketOnHoldRequestEntity, TicketOnHoldRequestResponse>(),
  MapType<TicketReopenRequestEntity, TicketReopenRequestResponse>(),

  // SLA
  MapType<TicketSLAAggEntity, TicketSLAAggResponse>(),
  MapType<TicketShiftAggEntity, TicketShiftAggResponse>(),
  MapType<TicketSLAEntity, TicketSLAResponse>(),
  MapType<TicketSLAPriorityLevelsEntity, TicketSLAPriorityLevelsResponse>(),
  MapType<TicketSLAPriorityLevelFirstEntity,
      TicketSLAPriorityLevelFirstResponse>(),
  MapType<TicketSLANotificationEntity, TicketSLANotificationResponse>(),
  MapType<TicketSLANotificationDurationEntity,
      TicketSLANotificationDurationResponse>(),
  MapType<TickeUserRoleEntity, TickeUserRoleResponse>(),
  MapType<TicketFlowChartEntity, TicketFlowChartResponse>(),
  MapType<TicketFlowChartNodeEntity, TicketFlowchartNodeResponse>(),
  MapType<TicketNodeOptionEntity, TicketNodeOptionResponse>(),
  MapType<TicketFlowchartEdgeEntity, TicketFlowchartEdgeResponse>(),
  MapType<TicketNodeEntity, TicketNodeResponse>(),
  MapType<TicketAssigneeEntity, TicketAssigneeResponse>(),
  MapType<WorkflowAdvanceSettingAssigneeEntity,
      WorkflowAdvanceSettingAssigneeResponse>(),
  MapType<TicketNodeTagEntity, TicketNodeTagResponse>(),

  // ---------- WorkFlow ---------- \\
  /*
      Ticket depends WorkFlow
      WorkFlow depends Ticket
      setup 2 mapper includes nhau không được,
      nên đành tổng hợp lại thành 1
    */
  MapType<WorkFlowWrapperResponse, WorkFlowWrapperEntity>(),
  MapType<WorkFlowResponse, WorkFlowEntity>(),
  MapType<WorkflowFormFieldPermissionsResponse,
      WorkflowFormFieldPermissionEntity>(),
  // form

  MapType<WorkFlowFormResponse, WorkFlowFormEntity>(),
  MapType<WorkFlowFormFieldResponse, WorkFlowFormFieldEntity>(),
  MapType<WorkFlowFieldValuesResponse, WorkFlowFieldValuesEntity>(),
  MapType<WorkFlowFormOptionResponse, WorkFlowFormOptionEntity>(),
  MapType<WorkFlowFormSelectionResponse, WorkFlowFormSelectionEntity>(),
  MapType<WorkFlowFormColumnResponse, WorkFlowFormColumnEntity>(),
  // startNode
  MapType<WorkFlowStartNodeResponse, WorkFlowStartNodeEntity>(),
  // user
  MapType<WorkFlowUserInfoResponse, WorkFlowUserInfoEntity>(),
  MapType<WorkFlowDeparmentResponse, WorkFlowDeparmentEntity>(),
  // advanceSetting
  MapType<WorkflowAdvanceSettingResponse, WorkflowAdvanceSettingEntity>(),
  MapType<WorkflowAdvanceSettingAllowActionResponse,
      WorkflowAdvanceSettingAllowActionEntity>(),
  MapType<WorkflowAdvanceSettingAdminOptionResponse,
      WorkflowAdvanceSettingAdminOptionEntity>(),
  MapType<WorkflowAdvanceSettingAllowOptionResponse,
      WorkflowAdvanceSettingAllowOptionEntity>(),
  MapType<WorkflowAdvanceSettingRateOptionResponse,
      WorkflowAdvanceSettingRateOptionEntity>(),
  MapType<WorkflowTagResponse, WorkflowTagEntity>(),
  // ---------- WorkFlow entity -> response ---------- \\
  MapType<WorkFlowWrapperEntity, WorkFlowWrapperResponse>(),
  MapType<WorkFlowEntity, WorkFlowResponse>(),
  MapType<WorkflowFormFieldPermissionEntity,
      WorkflowFormFieldPermissionsResponse>(),
  // form

  MapType<WorkFlowFormEntity, WorkFlowFormResponse>(),
  MapType<WorkFlowFormFieldEntity, WorkFlowFormFieldResponse>(),
  MapType<WorkFlowFieldValuesEntity, WorkFlowFieldValuesResponse>(),
  MapType<WorkFlowFormOptionEntity, WorkFlowFormOptionResponse>(),
  MapType<WorkFlowFormSelectionEntity, WorkFlowFormSelectionResponse>(),
  MapType<WorkFlowFormColumnEntity, WorkFlowFormColumnResponse>(),
  // startNode
  MapType<WorkFlowStartNodeEntity, WorkFlowStartNodeResponse>(),
  // user
  MapType<WorkFlowUserInfoEntity, WorkFlowUserInfoResponse>(),
  MapType<WorkFlowDeparmentEntity, WorkFlowDeparmentResponse>(),
  // advanceSetting
  MapType<WorkflowAdvanceSettingEntity, WorkflowAdvanceSettingResponse>(),
  MapType<WorkflowAdvanceSettingAllowActionEntity,
      WorkflowAdvanceSettingAllowActionResponse>(),
  MapType<WorkflowAdvanceSettingAdminOptionEntity,
      WorkflowAdvanceSettingAdminOptionResponse>(),
  MapType<WorkflowAdvanceSettingAllowOptionEntity,
      WorkflowAdvanceSettingAllowOptionResponse>(),
  MapType<WorkflowAdvanceSettingRateOptionEntity,
      WorkflowAdvanceSettingRateOptionResponse>(),
  // ---------- Other ----------
  MapType<TicketOnHoldRequestResponse, TicketOnHoldRequestEntity>(),
  MapType<TicketReopenRequestResponse, TicketReopenRequestEntity>(),
  MapType<TicketSpamRequestResponse, TicketSpamRequestEntity>(),
  MapType<TicketSpamRequestEntity, TicketSpamRequestResponse>(),
  MapType<WorkflowTagEntity, WorkflowTagResponse>(),
  MapType<TicketIsTicketAssigneeResponse, TicketIsTicketAssigneeEntity>(),
  MapType<TicketIsTicketAssigneeEntity, TicketIsTicketAssigneeResponse>(),

  // ---------- WorkFlowGroup ----------
  MapType<WorkFlowGroupWrapperResponse, WorkFlowGroupWrapperEntity>(),
  MapType<WorkFlowGroupResponse, WorkFlowGroupEntity>(),
], includes: [
  AssigneeEntityMapper(),
  AttachmentEntityMapper(),
])
class TicketEntityMapper extends $TicketEntityMapper {
  const TicketEntityMapper();

  static dynamic mapId(TicketListResponse input) {
    return input.id;
  }

  static List<Comment> mapTicketCommentCommentReplies(
      TicketCommentResponse input) {
    final comments = input.comments?.data ?? [];
    final replies = const TicketEntityMapper()
        .convertList<TicketCommentResponse, TicketCommentEntity>(comments);
    return replies;
  }
}
