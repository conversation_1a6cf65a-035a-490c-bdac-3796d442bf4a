// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_post_comments_request_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketPostCommentsRequestParams {
  @JsonKey(name: 'comment_as')
  CommentAs? get commentAs;
  @JsonKey(name: 'data_source')
  int? get dataSource;
  @JsonKey(name: 'medias')
  List<Medias>? get medias;
  @JsonKey(name: 'text')
  String? get text;
  @JsonKey(name: 'mentions')
  List<Mentions>? get mentions;
  @JsonKey(name: 'parent_id')
  String? get parentId;
  @JsonKey(name: 'ticket_node_id')
  int? get ticketNodeId;
  @JsonKey(name: 'is_private_note')
  bool? get isPrivateNote;
  @JsonKey(name: 'target_type')
  String? get targetType;
  @JsonKey(name: 'target_id')
  String? get targetId;

  /// Serializes this TicketPostCommentsRequestParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketPostCommentsRequestParams &&
            (identical(other.commentAs, commentAs) ||
                other.commentAs == commentAs) &&
            (identical(other.dataSource, dataSource) ||
                other.dataSource == dataSource) &&
            const DeepCollectionEquality().equals(other.medias, medias) &&
            (identical(other.text, text) || other.text == text) &&
            const DeepCollectionEquality().equals(other.mentions, mentions) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.ticketNodeId, ticketNodeId) ||
                other.ticketNodeId == ticketNodeId) &&
            (identical(other.isPrivateNote, isPrivateNote) ||
                other.isPrivateNote == isPrivateNote) &&
            (identical(other.targetType, targetType) ||
                other.targetType == targetType) &&
            (identical(other.targetId, targetId) ||
                other.targetId == targetId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      commentAs,
      dataSource,
      const DeepCollectionEquality().hash(medias),
      text,
      const DeepCollectionEquality().hash(mentions),
      parentId,
      ticketNodeId,
      isPrivateNote,
      targetType,
      targetId);

  @override
  String toString() {
    return 'TicketPostCommentsRequestParams(commentAs: $commentAs, dataSource: $dataSource, medias: $medias, text: $text, mentions: $mentions, parentId: $parentId, ticketNodeId: $ticketNodeId, isPrivateNote: $isPrivateNote, targetType: $targetType, targetId: $targetId)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketPostCommentsRequestParams
    implements TicketPostCommentsRequestParams {
  _TicketPostCommentsRequestParams(
      {@JsonKey(name: 'comment_as') this.commentAs,
      @JsonKey(name: 'data_source') this.dataSource = 3,
      @JsonKey(name: 'medias') final List<Medias>? medias,
      @JsonKey(name: 'text') this.text,
      @JsonKey(name: 'mentions') final List<Mentions>? mentions,
      @JsonKey(name: 'parent_id') this.parentId = "",
      @JsonKey(name: 'ticket_node_id') this.ticketNodeId,
      @JsonKey(name: 'is_private_note') this.isPrivateNote = false,
      @JsonKey(name: 'target_type') this.targetType = "ticket",
      @JsonKey(name: 'target_id') this.targetId})
      : _medias = medias,
        _mentions = mentions;

  @override
  @JsonKey(name: 'comment_as')
  final CommentAs? commentAs;
  @override
  @JsonKey(name: 'data_source')
  final int? dataSource;
  final List<Medias>? _medias;
  @override
  @JsonKey(name: 'medias')
  List<Medias>? get medias {
    final value = _medias;
    if (value == null) return null;
    if (_medias is EqualUnmodifiableListView) return _medias;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'text')
  final String? text;
  final List<Mentions>? _mentions;
  @override
  @JsonKey(name: 'mentions')
  List<Mentions>? get mentions {
    final value = _mentions;
    if (value == null) return null;
    if (_mentions is EqualUnmodifiableListView) return _mentions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'parent_id')
  final String? parentId;
  @override
  @JsonKey(name: 'ticket_node_id')
  final int? ticketNodeId;
  @override
  @JsonKey(name: 'is_private_note')
  final bool? isPrivateNote;
  @override
  @JsonKey(name: 'target_type')
  final String? targetType;
  @override
  @JsonKey(name: 'target_id')
  final String? targetId;

  @override
  Map<String, dynamic> toJson() {
    return _$TicketPostCommentsRequestParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketPostCommentsRequestParams &&
            (identical(other.commentAs, commentAs) ||
                other.commentAs == commentAs) &&
            (identical(other.dataSource, dataSource) ||
                other.dataSource == dataSource) &&
            const DeepCollectionEquality().equals(other._medias, _medias) &&
            (identical(other.text, text) || other.text == text) &&
            const DeepCollectionEquality().equals(other._mentions, _mentions) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.ticketNodeId, ticketNodeId) ||
                other.ticketNodeId == ticketNodeId) &&
            (identical(other.isPrivateNote, isPrivateNote) ||
                other.isPrivateNote == isPrivateNote) &&
            (identical(other.targetType, targetType) ||
                other.targetType == targetType) &&
            (identical(other.targetId, targetId) ||
                other.targetId == targetId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      commentAs,
      dataSource,
      const DeepCollectionEquality().hash(_medias),
      text,
      const DeepCollectionEquality().hash(_mentions),
      parentId,
      ticketNodeId,
      isPrivateNote,
      targetType,
      targetId);

  @override
  String toString() {
    return 'TicketPostCommentsRequestParams(commentAs: $commentAs, dataSource: $dataSource, medias: $medias, text: $text, mentions: $mentions, parentId: $parentId, ticketNodeId: $ticketNodeId, isPrivateNote: $isPrivateNote, targetType: $targetType, targetId: $targetId)';
  }
}

// dart format on
