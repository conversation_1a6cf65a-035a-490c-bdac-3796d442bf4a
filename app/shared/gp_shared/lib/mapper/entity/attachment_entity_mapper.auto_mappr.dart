// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoMapprGenerator
// **************************************************************************

// ignore_for_file: type=lint, unnecessary_cast, unused_local_variable

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i1;
import 'package:gp_core/models/upload/upload_file_response_model.dart' as _i4;
import 'package:gp_core/models/upload/upload_file_url_response_model.dart'
    as _i2;
import 'package:gp_core/models/upload/upload_image_response_model.dart' as _i6;

import '../../data/model/upload/response/upload/upload_file_response_v2.dart'
    as _i5;
import '../../domain/entity/attachment/gp_attachment_file.entity.dart' as _i3;
import 'attachment_entity_mapper.dart' as _i7;

/// Available mappings:
/// - `UploadFileURLResponseModel` → `GpAttachmentFileUrlEntity`.
/// - `UploadFileResponseModel` → `GPAttachmentFileEntity`.
/// - `UploadFileResponseModelV2` → `GPAttachmentFileEntity`.
/// - `UploadImageResponseModel` → `GPAttachmentFileEntity`.
/// - `GpAttachmentFileUrlEntity` → `UploadFileURLResponseModel`.
/// - `GPAttachmentFileEntity` → `UploadFileResponseModel`.
/// - `GPAttachmentFileEntity` → `UploadFileResponseModelV2`.
class $AttachmentEntityMapper implements _i1.AutoMapprInterface {
  const $AttachmentEntityMapper();

  Type _typeOf<T>() => T;

  List<_i1.AutoMapprInterface> get _delegates => const [];

  
  
  @override
  bool canConvert<SOURCE, TARGET>({bool recursive = true}) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.UploadFileURLResponseModel>() ||
            sourceTypeOf == _typeOf<_i2.UploadFileURLResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i3.GpAttachmentFileUrlEntity>() ||
            targetTypeOf == _typeOf<_i3.GpAttachmentFileUrlEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i4.UploadFileResponseModel>() ||
            sourceTypeOf == _typeOf<_i4.UploadFileResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i5.UploadFileResponseModelV2>() ||
            sourceTypeOf == _typeOf<_i5.UploadFileResponseModelV2?>()) &&
        (targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i6.UploadImageResponseModel>() ||
            sourceTypeOf == _typeOf<_i6.UploadImageResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i3.GpAttachmentFileUrlEntity>() ||
            sourceTypeOf == _typeOf<_i3.GpAttachmentFileUrlEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.UploadFileURLResponseModel>() ||
            targetTypeOf == _typeOf<_i2.UploadFileURLResponseModel?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i3.GPAttachmentFileEntity>() ||
            sourceTypeOf == _typeOf<_i3.GPAttachmentFileEntity?>()) &&
        (targetTypeOf == _typeOf<_i4.UploadFileResponseModel>() ||
            targetTypeOf == _typeOf<_i4.UploadFileResponseModel?>())) {
      return true;
    }
    if ((sourceTypeOf == _typeOf<_i3.GPAttachmentFileEntity>() ||
            sourceTypeOf == _typeOf<_i3.GPAttachmentFileEntity?>()) &&
        (targetTypeOf == _typeOf<_i5.UploadFileResponseModelV2>() ||
            targetTypeOf == _typeOf<_i5.UploadFileResponseModelV2?>())) {
      return true;
    }
    if (recursive) {
      for (final mappr in _delegates) {
        if (mappr.canConvert<SOURCE, TARGET>()) {
          return true;
        }
      }
    }
    return false;
  }

  
  
  @override
  TARGET convert<SOURCE, TARGET>(SOURCE? model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _convert(model)!;
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convert(model)!;
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  TARGET? tryConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return _safeConvert(
        model,
        onMappingError: onMappingError,
      );
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvert(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    return null;
  }

  
  
  @override
  Iterable<TARGET> convertIterable<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET>((item) => _convert(item)!);
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertIterable(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Iterable<TARGET?> tryConvertIterable<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return model.map<TARGET?>(
          (item) => _safeConvert(item, onMappingError: onMappingError));
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertIterable(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  List<TARGET> convertList<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertList(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  List<TARGET?> tryConvertList<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toList();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertList(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  
  
  @override
  Set<TARGET> convertSet<SOURCE, TARGET>(Iterable<SOURCE?> model) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return convertIterable<SOURCE, TARGET>(model).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.convertSet(model);
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.
  ///
  /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null
  ///
  
  @override
  Set<TARGET?> tryConvertSet<SOURCE, TARGET>(
    Iterable<SOURCE?> model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (canConvert<SOURCE, TARGET>(recursive: false)) {
      return tryConvertIterable<SOURCE, TARGET>(
        model,
        onMappingError: onMappingError,
      ).toSet();
    }
    for (final mappr in _delegates) {
      if (mappr.canConvert<SOURCE, TARGET>()) {
        return mappr.tryConvertSet(
          model,
          onMappingError: onMappingError,
        );
      }
    }

    throw Exception('No ${_typeOf<SOURCE>()} -> ${_typeOf<TARGET>()} mapping.');
  }

  TARGET? _convert<SOURCE, TARGET>(
    SOURCE? model, {
    bool canReturnNull = false,
  }) {
    final sourceTypeOf = _typeOf<SOURCE>();
    final targetTypeOf = _typeOf<TARGET>();
    if ((sourceTypeOf == _typeOf<_i2.UploadFileURLResponseModel>() ||
            sourceTypeOf == _typeOf<_i2.UploadFileURLResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i3.GpAttachmentFileUrlEntity>() ||
            targetTypeOf == _typeOf<_i3.GpAttachmentFileUrlEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i2$UploadFileURLResponseModel_To__i3$GpAttachmentFileUrlEntity(
          (model as _i2.UploadFileURLResponseModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i4.UploadFileResponseModel>() ||
            sourceTypeOf == _typeOf<_i4.UploadFileResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i4$UploadFileResponseModel_To__i3$GPAttachmentFileEntity(
          (model as _i4.UploadFileResponseModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i5.UploadFileResponseModelV2>() ||
            sourceTypeOf == _typeOf<_i5.UploadFileResponseModelV2?>()) &&
        (targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i5$UploadFileResponseModelV2_To__i3$GPAttachmentFileEntity(
          (model as _i5.UploadFileResponseModelV2?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i6.UploadImageResponseModel>() ||
            sourceTypeOf == _typeOf<_i6.UploadImageResponseModel?>()) &&
        (targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity>() ||
            targetTypeOf == _typeOf<_i3.GPAttachmentFileEntity?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i6$UploadImageResponseModel_To__i3$GPAttachmentFileEntity(
          (model as _i6.UploadImageResponseModel?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i3.GpAttachmentFileUrlEntity>() ||
            sourceTypeOf == _typeOf<_i3.GpAttachmentFileUrlEntity?>()) &&
        (targetTypeOf == _typeOf<_i2.UploadFileURLResponseModel>() ||
            targetTypeOf == _typeOf<_i2.UploadFileURLResponseModel?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i3$GpAttachmentFileUrlEntity_To__i2$UploadFileURLResponseModel(
          (model as _i3.GpAttachmentFileUrlEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i3.GPAttachmentFileEntity>() ||
            sourceTypeOf == _typeOf<_i3.GPAttachmentFileEntity?>()) &&
        (targetTypeOf == _typeOf<_i4.UploadFileResponseModel>() ||
            targetTypeOf == _typeOf<_i4.UploadFileResponseModel?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i3$GPAttachmentFileEntity_To__i4$UploadFileResponseModel(
          (model as _i3.GPAttachmentFileEntity?)) as TARGET);
    }
    if ((sourceTypeOf == _typeOf<_i3.GPAttachmentFileEntity>() ||
            sourceTypeOf == _typeOf<_i3.GPAttachmentFileEntity?>()) &&
        (targetTypeOf == _typeOf<_i5.UploadFileResponseModelV2>() ||
            targetTypeOf == _typeOf<_i5.UploadFileResponseModelV2?>())) {
      if (canReturnNull && model == null) {
        return null;
      }
      return (_map__i3$GPAttachmentFileEntity_To__i5$UploadFileResponseModelV2(
          (model as _i3.GPAttachmentFileEntity?)) as TARGET);
    }
    throw Exception('No ${model.runtimeType} -> $targetTypeOf mapping.');
  }

  TARGET? _safeConvert<SOURCE, TARGET>(
    SOURCE? model, {
    void Function(Object error, StackTrace stackTrace, SOURCE? source)?
        onMappingError,
  }) {
    if (!useSafeMapping<SOURCE, TARGET>()) {
      return _convert(
        model,
        canReturnNull: true,
      );
    }
    try {
      return _convert(
        model,
        canReturnNull: true,
      );
    } catch (e, s) {
      onMappingError?.call(e, s, model);
      return null;
    }
  }

  
  
  @override
  bool useSafeMapping<SOURCE, TARGET>() {
    return false;
  }

  _i3.GpAttachmentFileUrlEntity
      _map__i2$UploadFileURLResponseModel_To__i3$GpAttachmentFileUrlEntity(
          _i2.UploadFileURLResponseModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping UploadFileURLResponseModel → GpAttachmentFileUrlEntity failed because UploadFileURLResponseModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<UploadFileURLResponseModel, GpAttachmentFileUrlEntity> to handle null values during mapping.');
    }
    return _i3.GpAttachmentFileUrlEntity(
      src: model.src,
      store: model.store,
    );
  }

  _i3.GPAttachmentFileEntity
      _map__i4$UploadFileResponseModel_To__i3$GPAttachmentFileEntity(
          _i4.UploadFileResponseModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping UploadFileResponseModel → GPAttachmentFileEntity failed because UploadFileResponseModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<UploadFileResponseModel, GPAttachmentFileEntity> to handle null values during mapping.');
    }
    return _i3.GPAttachmentFileEntity(
      uploadType: _i7.AttachmentEntityMapper.mapTypeFile(model),
      fileType: _i7.AttachmentEntityMapper.mapFileType(model),
      id: _i7.AttachmentEntityMapper.mapId(model),
      src: model.src,
      url:
          _map__i2$UploadFileURLResponseModel_To__i3$GpAttachmentFileUrlEntity_Nullable(
              model.url),
      thumbUrl:
          _map__i2$UploadFileURLResponseModel_To__i3$GpAttachmentFileUrlEntity_Nullable(
              model.thumbUrl),
      userId: _i7.AttachmentEntityMapper.mapUserId(model),
      source: model.source,
      quality: model.quality,
      srcThumbPattern: model.srcThumbPattern,
      name: _i7.AttachmentEntityMapper.mapFileName(model),
      fileLink: model.fileLink,
      type: null,
    )..size = model.size;
  }

  _i3.GPAttachmentFileEntity
      _map__i5$UploadFileResponseModelV2_To__i3$GPAttachmentFileEntity(
          _i5.UploadFileResponseModelV2? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping UploadFileResponseModelV2 → GPAttachmentFileEntity failed because UploadFileResponseModelV2 was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<UploadFileResponseModelV2, GPAttachmentFileEntity> to handle null values during mapping.');
    }
    return _i3.GPAttachmentFileEntity(
      fileType: _i7.AttachmentEntityMapper.mapFileType(model),
      id: model.id,
      src: model.src,
      url: _i7.AttachmentEntityMapper.mapUrlV2(model),
      thumbUrl: _i7.AttachmentEntityMapper.mapUrlV2(model),
      userId: model.userId,
      source: model.source,
      quality: model.quality,
      name: model.name,
      fileLink: model.fileLink,
      type: null,
    )..size = model.size;
  }

  _i3.GPAttachmentFileEntity
      _map__i6$UploadImageResponseModel_To__i3$GPAttachmentFileEntity(
          _i6.UploadImageResponseModel? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping UploadImageResponseModel → GPAttachmentFileEntity failed because UploadImageResponseModel was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<UploadImageResponseModel, GPAttachmentFileEntity> to handle null values during mapping.');
    }
    return _i3.GPAttachmentFileEntity(
      type: _i7.AttachmentEntityMapper.mapType(model),
      uploadType: _i7.AttachmentEntityMapper.mapType(model),
      fileType: _i7.AttachmentEntityMapper.mapFileType(model),
      id: _i7.AttachmentEntityMapper.mapId(model),
      src: model.src,
      url:
          _map__i2$UploadFileURLResponseModel_To__i3$GpAttachmentFileUrlEntity_Nullable(
              model.url),
      userId: _i7.AttachmentEntityMapper.mapUserId(model),
      source: model.source,
      quality: model.quality,
      category: model.category,
      width: model.width,
      height: model.height,
      fileLink: model.fileLink,
    )
      ..size = model.size
      ..fileName = model.fileName;
  }

  _i2.UploadFileURLResponseModel
      _map__i3$GpAttachmentFileUrlEntity_To__i2$UploadFileURLResponseModel(
          _i3.GpAttachmentFileUrlEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GpAttachmentFileUrlEntity → UploadFileURLResponseModel failed because GpAttachmentFileUrlEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GpAttachmentFileUrlEntity, UploadFileURLResponseModel> to handle null values during mapping.');
    }
    return _i2.UploadFileURLResponseModel(
      store: model.store,
      src: model.src,
    );
  }

  _i4.UploadFileResponseModel
      _map__i3$GPAttachmentFileEntity_To__i4$UploadFileResponseModel(
          _i3.GPAttachmentFileEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GPAttachmentFileEntity → UploadFileResponseModel failed because GPAttachmentFileEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GPAttachmentFileEntity, UploadFileResponseModel> to handle null values during mapping.');
    }
    return _i4.UploadFileResponseModel(
      id: _i7.AttachmentEntityMapper.mapToId(model),
      name: model.name,
      userId: _i7.AttachmentEntityMapper.mapToUserId(model),
      size: model.size,
      fileType: _i7.AttachmentEntityMapper.mapFileTypeEntity(model),
      url:
          _map__i3$GpAttachmentFileUrlEntity_To__i2$UploadFileURLResponseModel_Nullable(
              model.url),
      thumbUrl:
          _map__i3$GpAttachmentFileUrlEntity_To__i2$UploadFileURLResponseModel_Nullable(
              model.thumbUrl),
      fileLink: model.fileLink,
      quality: model.quality,
      source: model.source,
      uploadType: _i7.AttachmentEntityMapper.mapUploadTypeEntity(model),
    )
      ..src = model.src
      ..srcThumbPattern = model.srcThumbPattern;
  }

  _i5.UploadFileResponseModelV2
      _map__i3$GPAttachmentFileEntity_To__i5$UploadFileResponseModelV2(
          _i3.GPAttachmentFileEntity? input) {
    final model = input;
    if (model == null) {
      throw Exception(
          r'Mapping GPAttachmentFileEntity → UploadFileResponseModelV2 failed because GPAttachmentFileEntity was null, and no default value was provided. '
          r'Consider setting the whenSourceIsNull parameter on the MapType<GPAttachmentFileEntity, UploadFileResponseModelV2> to handle null values during mapping.');
    }
    return _i5.UploadFileResponseModelV2(
      id: model.id,
      name: model.name,
      userId: model.userId,
      size: model.size,
      fileType: _i7.AttachmentEntityMapper.mapFileTypeEntity(model),
      url: _i7.AttachmentEntityMapper.mapToUrlV2(model),
      thumbUrl: _i7.AttachmentEntityMapper.mapToUrlV2(model),
      fileLink: model.fileLink,
      quality: model.quality,
      source: model.source,
    )..src = model.src;
  }

  _i3.GpAttachmentFileUrlEntity?
      _map__i2$UploadFileURLResponseModel_To__i3$GpAttachmentFileUrlEntity_Nullable(
          _i2.UploadFileURLResponseModel? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i3.GpAttachmentFileUrlEntity(
      src: model.src,
      store: model.store,
    );
  }

  _i2.UploadFileURLResponseModel?
      _map__i3$GpAttachmentFileUrlEntity_To__i2$UploadFileURLResponseModel_Nullable(
          _i3.GpAttachmentFileUrlEntity? input) {
    final model = input;
    if (model == null) {
      return null;
    }
    return _i2.UploadFileURLResponseModel(
      store: model.store,
      src: model.src,
    );
  }
}
