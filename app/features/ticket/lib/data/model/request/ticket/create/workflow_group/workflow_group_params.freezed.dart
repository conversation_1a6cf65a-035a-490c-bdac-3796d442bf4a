// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'workflow_group_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkFlowGroupParams {
  String get q;

  ///
  int get limit;

  ///
  int get published;

  ///
  @JsonKey(name: 'include_grandchildren')
  bool get includeGrandchildren;

  ///
  @JsonKey(name: 'get_of_child_group')
  bool get getOfChildGroup;

  ///
  @JsonKey(name: 'next')
  String? get nextLink;
  @JsonKey(name: 'offset')
  int? get offset;

  /// Create a copy of WorkFlowGroupParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkFlowGroupParamsCopyWith<WorkFlowGroupParams> get copyWith =>
      _$WorkFlowGroupParamsCopyWithImpl<WorkFlowGroupParams>(
          this as WorkFlowGroupParams, _$identity);

  /// Serializes this WorkFlowGroupParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkFlowGroupParams &&
            (identical(other.q, q) || other.q == q) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.published, published) ||
                other.published == published) &&
            (identical(other.includeGrandchildren, includeGrandchildren) ||
                other.includeGrandchildren == includeGrandchildren) &&
            (identical(other.getOfChildGroup, getOfChildGroup) ||
                other.getOfChildGroup == getOfChildGroup) &&
            (identical(other.nextLink, nextLink) ||
                other.nextLink == nextLink) &&
            (identical(other.offset, offset) || other.offset == offset));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, q, limit, published,
      includeGrandchildren, getOfChildGroup, nextLink, offset);

  @override
  String toString() {
    return 'WorkFlowGroupParams(q: $q, limit: $limit, published: $published, includeGrandchildren: $includeGrandchildren, getOfChildGroup: $getOfChildGroup, nextLink: $nextLink, offset: $offset)';
  }
}

/// @nodoc
abstract mixin class $WorkFlowGroupParamsCopyWith<$Res> {
  factory $WorkFlowGroupParamsCopyWith(
          WorkFlowGroupParams value, $Res Function(WorkFlowGroupParams) _then) =
      _$WorkFlowGroupParamsCopyWithImpl;
  @useResult
  $Res call(
      {String q,
      int limit,
      int published,
      @JsonKey(name: 'include_grandchildren') bool includeGrandchildren,
      @JsonKey(name: 'get_of_child_group') bool getOfChildGroup,
      @JsonKey(name: 'next') String? nextLink,
      @JsonKey(name: 'offset') int? offset});
}

/// @nodoc
class _$WorkFlowGroupParamsCopyWithImpl<$Res>
    implements $WorkFlowGroupParamsCopyWith<$Res> {
  _$WorkFlowGroupParamsCopyWithImpl(this._self, this._then);

  final WorkFlowGroupParams _self;
  final $Res Function(WorkFlowGroupParams) _then;

  /// Create a copy of WorkFlowGroupParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? q = null,
    Object? limit = null,
    Object? published = null,
    Object? includeGrandchildren = null,
    Object? getOfChildGroup = null,
    Object? nextLink = freezed,
    Object? offset = freezed,
  }) {
    return _then(_self.copyWith(
      q: null == q
          ? _self.q
          : q // ignore: cast_nullable_to_non_nullable
              as String,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      published: null == published
          ? _self.published
          : published // ignore: cast_nullable_to_non_nullable
              as int,
      includeGrandchildren: null == includeGrandchildren
          ? _self.includeGrandchildren
          : includeGrandchildren // ignore: cast_nullable_to_non_nullable
              as bool,
      getOfChildGroup: null == getOfChildGroup
          ? _self.getOfChildGroup
          : getOfChildGroup // ignore: cast_nullable_to_non_nullable
              as bool,
      nextLink: freezed == nextLink
          ? _self.nextLink
          : nextLink // ignore: cast_nullable_to_non_nullable
              as String?,
      offset: freezed == offset
          ? _self.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _WorkFlowGroupParams implements WorkFlowGroupParams {
  const _WorkFlowGroupParams(
      {this.q = '',
      this.limit = 10,
      this.published = 1,
      @JsonKey(name: 'include_grandchildren') this.includeGrandchildren = true,
      @JsonKey(name: 'get_of_child_group') this.getOfChildGroup = false,
      @JsonKey(name: 'next') this.nextLink = '',
      @JsonKey(name: 'offset') this.offset = 0});

  @override
  @JsonKey()
  final String q;

  ///
  @override
  @JsonKey()
  final int limit;

  ///
  @override
  @JsonKey()
  final int published;

  ///
  @override
  @JsonKey(name: 'include_grandchildren')
  final bool includeGrandchildren;

  ///
  @override
  @JsonKey(name: 'get_of_child_group')
  final bool getOfChildGroup;

  ///
  @override
  @JsonKey(name: 'next')
  final String? nextLink;
  @override
  @JsonKey(name: 'offset')
  final int? offset;

  /// Create a copy of WorkFlowGroupParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkFlowGroupParamsCopyWith<_WorkFlowGroupParams> get copyWith =>
      __$WorkFlowGroupParamsCopyWithImpl<_WorkFlowGroupParams>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkFlowGroupParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkFlowGroupParams &&
            (identical(other.q, q) || other.q == q) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.published, published) ||
                other.published == published) &&
            (identical(other.includeGrandchildren, includeGrandchildren) ||
                other.includeGrandchildren == includeGrandchildren) &&
            (identical(other.getOfChildGroup, getOfChildGroup) ||
                other.getOfChildGroup == getOfChildGroup) &&
            (identical(other.nextLink, nextLink) ||
                other.nextLink == nextLink) &&
            (identical(other.offset, offset) || other.offset == offset));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, q, limit, published,
      includeGrandchildren, getOfChildGroup, nextLink, offset);

  @override
  String toString() {
    return 'WorkFlowGroupParams(q: $q, limit: $limit, published: $published, includeGrandchildren: $includeGrandchildren, getOfChildGroup: $getOfChildGroup, nextLink: $nextLink, offset: $offset)';
  }
}

/// @nodoc
abstract mixin class _$WorkFlowGroupParamsCopyWith<$Res>
    implements $WorkFlowGroupParamsCopyWith<$Res> {
  factory _$WorkFlowGroupParamsCopyWith(_WorkFlowGroupParams value,
          $Res Function(_WorkFlowGroupParams) _then) =
      __$WorkFlowGroupParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String q,
      int limit,
      int published,
      @JsonKey(name: 'include_grandchildren') bool includeGrandchildren,
      @JsonKey(name: 'get_of_child_group') bool getOfChildGroup,
      @JsonKey(name: 'next') String? nextLink,
      @JsonKey(name: 'offset') int? offset});
}

/// @nodoc
class __$WorkFlowGroupParamsCopyWithImpl<$Res>
    implements _$WorkFlowGroupParamsCopyWith<$Res> {
  __$WorkFlowGroupParamsCopyWithImpl(this._self, this._then);

  final _WorkFlowGroupParams _self;
  final $Res Function(_WorkFlowGroupParams) _then;

  /// Create a copy of WorkFlowGroupParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? q = null,
    Object? limit = null,
    Object? published = null,
    Object? includeGrandchildren = null,
    Object? getOfChildGroup = null,
    Object? nextLink = freezed,
    Object? offset = freezed,
  }) {
    return _then(_WorkFlowGroupParams(
      q: null == q
          ? _self.q
          : q // ignore: cast_nullable_to_non_nullable
              as String,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      published: null == published
          ? _self.published
          : published // ignore: cast_nullable_to_non_nullable
              as int,
      includeGrandchildren: null == includeGrandchildren
          ? _self.includeGrandchildren
          : includeGrandchildren // ignore: cast_nullable_to_non_nullable
              as bool,
      getOfChildGroup: null == getOfChildGroup
          ? _self.getOfChildGroup
          : getOfChildGroup // ignore: cast_nullable_to_non_nullable
              as bool,
      nextLink: freezed == nextLink
          ? _self.nextLink
          : nextLink // ignore: cast_nullable_to_non_nullable
              as String?,
      offset: freezed == offset
          ? _self.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

// dart format on
