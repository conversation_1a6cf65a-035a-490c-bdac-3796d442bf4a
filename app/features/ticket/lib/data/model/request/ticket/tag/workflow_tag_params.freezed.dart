// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'workflow_tag_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WorkflowTagListParams {
  int get limit;
  @JsonKey(name: 'q')
  String get search;
  @JsonKey(name: 'offset')
  int? get offset;
  @JsonKey(name: 'workflow_id')
  String get workflowId;

  /// Create a copy of WorkflowTagListParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkflowTagListParamsCopyWith<WorkflowTagListParams> get copyWith =>
      _$WorkflowTagListParamsCopyWithImpl<WorkflowTagListParams>(
          this as WorkflowTagListParams, _$identity);

  /// Serializes this WorkflowTagListParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkflowTagListParams &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.search, search) || other.search == search) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.workflowId, workflowId) ||
                other.workflowId == workflowId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, limit, search, offset, workflowId);

  @override
  String toString() {
    return 'WorkflowTagListParams(limit: $limit, search: $search, offset: $offset, workflowId: $workflowId)';
  }
}

/// @nodoc
abstract mixin class $WorkflowTagListParamsCopyWith<$Res> {
  factory $WorkflowTagListParamsCopyWith(WorkflowTagListParams value,
          $Res Function(WorkflowTagListParams) _then) =
      _$WorkflowTagListParamsCopyWithImpl;
  @useResult
  $Res call(
      {int limit,
      @JsonKey(name: 'q') String search,
      @JsonKey(name: 'offset') int? offset,
      @JsonKey(name: 'workflow_id') String workflowId});
}

/// @nodoc
class _$WorkflowTagListParamsCopyWithImpl<$Res>
    implements $WorkflowTagListParamsCopyWith<$Res> {
  _$WorkflowTagListParamsCopyWithImpl(this._self, this._then);

  final WorkflowTagListParams _self;
  final $Res Function(WorkflowTagListParams) _then;

  /// Create a copy of WorkflowTagListParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? limit = null,
    Object? search = null,
    Object? offset = freezed,
    Object? workflowId = null,
  }) {
    return _then(_self.copyWith(
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      search: null == search
          ? _self.search
          : search // ignore: cast_nullable_to_non_nullable
              as String,
      offset: freezed == offset
          ? _self.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      workflowId: null == workflowId
          ? _self.workflowId
          : workflowId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _WorkflowTagListParams implements WorkflowTagListParams {
  _WorkflowTagListParams(
      {this.limit = 10,
      @JsonKey(name: 'q') this.search = '',
      @JsonKey(name: 'offset') this.offset = 0,
      @JsonKey(name: 'workflow_id') required this.workflowId});

  @override
  @JsonKey()
  final int limit;
  @override
  @JsonKey(name: 'q')
  final String search;
  @override
  @JsonKey(name: 'offset')
  final int? offset;
  @override
  @JsonKey(name: 'workflow_id')
  final String workflowId;

  /// Create a copy of WorkflowTagListParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkflowTagListParamsCopyWith<_WorkflowTagListParams> get copyWith =>
      __$WorkflowTagListParamsCopyWithImpl<_WorkflowTagListParams>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkflowTagListParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkflowTagListParams &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.search, search) || other.search == search) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.workflowId, workflowId) ||
                other.workflowId == workflowId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, limit, search, offset, workflowId);

  @override
  String toString() {
    return 'WorkflowTagListParams(limit: $limit, search: $search, offset: $offset, workflowId: $workflowId)';
  }
}

/// @nodoc
abstract mixin class _$WorkflowTagListParamsCopyWith<$Res>
    implements $WorkflowTagListParamsCopyWith<$Res> {
  factory _$WorkflowTagListParamsCopyWith(_WorkflowTagListParams value,
          $Res Function(_WorkflowTagListParams) _then) =
      __$WorkflowTagListParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int limit,
      @JsonKey(name: 'q') String search,
      @JsonKey(name: 'offset') int? offset,
      @JsonKey(name: 'workflow_id') String workflowId});
}

/// @nodoc
class __$WorkflowTagListParamsCopyWithImpl<$Res>
    implements _$WorkflowTagListParamsCopyWith<$Res> {
  __$WorkflowTagListParamsCopyWithImpl(this._self, this._then);

  final _WorkflowTagListParams _self;
  final $Res Function(_WorkflowTagListParams) _then;

  /// Create a copy of WorkflowTagListParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? limit = null,
    Object? search = null,
    Object? offset = freezed,
    Object? workflowId = null,
  }) {
    return _then(_WorkflowTagListParams(
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      search: null == search
          ? _self.search
          : search // ignore: cast_nullable_to_non_nullable
              as String,
      offset: freezed == offset
          ? _self.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      workflowId: null == workflowId
          ? _self.workflowId
          : workflowId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
