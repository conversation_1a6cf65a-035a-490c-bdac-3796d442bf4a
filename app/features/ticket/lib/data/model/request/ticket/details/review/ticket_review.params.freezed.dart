// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_review.params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketReviewParams {
  @JsonKey(name: 'review')
  String get review;
  @JsonKey(name: 'rating_point')
  int get ratingPoint;

  /// Serializes this TicketReviewParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketReviewParams &&
            (identical(other.review, review) || other.review == review) &&
            (identical(other.ratingPoint, ratingPoint) ||
                other.ratingPoint == ratingPoint));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, review, ratingPoint);

  @override
  String toString() {
    return 'TicketReviewParams(review: $review, ratingPoint: $ratingPoint)';
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketReviewParams implements TicketReviewParams {
  _TicketReviewParams(
      {@JsonKey(name: 'review') required this.review,
      @JsonKey(name: 'rating_point') required this.ratingPoint});

  @override
  @JsonKey(name: 'review')
  final String review;
  @override
  @JsonKey(name: 'rating_point')
  final int ratingPoint;

  @override
  Map<String, dynamic> toJson() {
    return _$TicketReviewParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketReviewParams &&
            (identical(other.review, review) || other.review == review) &&
            (identical(other.ratingPoint, ratingPoint) ||
                other.ratingPoint == ratingPoint));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, review, ratingPoint);

  @override
  String toString() {
    return 'TicketReviewParams(review: $review, ratingPoint: $ratingPoint)';
  }
}

// dart format on
