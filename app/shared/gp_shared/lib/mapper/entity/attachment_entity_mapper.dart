/*
 * Created Date: Tuesday, 20th August 2024, 15:08:11
 * Author: gapo
 * -----
 * Last Modified: Monday, 14th July 2025 09:51:12
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:gp_core/core.dart';

import '../../data/model/model.dart';
import '../../domain/entity/entity.dart';
import 'attachment_entity_mapper.auto_mappr.dart';


@AutoMappr(
  [
    MapType<UploadFileURLResponseModel, GpAttachmentFileUrlEntity>(),
    MapType<UploadFileResponseModel, GPAttachmentFileEntity>(fields: [
      Field('id', custom: AttachmentEntityMapper.mapId),
      Field('userId', custom: AttachmentEntityMapper.mapUserId),
      Field('fileType', custom: AttachmentEntityMapper.mapFileType),
      Field('uploadType', custom: AttachmentEntityMapper.mapTypeFile),
      <PERSON>('name', custom: AttachmentEntityMapper.mapFileName),
    ]),
    MapType<UploadFileResponseModelV2, GPAttachmentFileEntity>(fields: [
      Field('fileType', custom: AttachmentEntityMapper.mapFileType),
      Field('url', custom: AttachmentEntityMapper.mapUrlV2),
      Field('thumbUrl', custom: AttachmentEntityMapper.mapUrlV2),
    ]),
    MapType<UploadImageResponseModel, GPAttachmentFileEntity>(
      fields: [
        Field('id', custom: AttachmentEntityMapper.mapId),
        Field('userId', custom: AttachmentEntityMapper.mapUserId),
        Field('type', custom: AttachmentEntityMapper.mapType),
        Field('uploadType', custom: AttachmentEntityMapper.mapType),
        Field('fileType', custom: AttachmentEntityMapper.mapFileType),
      ],
    ),

    /// entity -> response
    MapType<GpAttachmentFileUrlEntity, UploadFileURLResponseModel>(),
    MapType<GPAttachmentFileEntity, UploadFileResponseModel>(fields: [
      Field('id', custom: AttachmentEntityMapper.mapToId),
      Field('userId', custom: AttachmentEntityMapper.mapToUserId),
      Field('fileType', custom: AttachmentEntityMapper.mapFileTypeEntity),
      Field('uploadType', custom: AttachmentEntityMapper.mapUploadTypeEntity),
    ]),
    MapType<GPAttachmentFileEntity, UploadFileResponseModelV2>(fields: [
      Field('fileType', custom: AttachmentEntityMapper.mapFileTypeEntity),
      Field('url', custom: AttachmentEntityMapper.mapToUrlV2),
      Field('thumbUrl', custom: AttachmentEntityMapper.mapToUrlV2),
    ]),
  ],
)
class AttachmentEntityMapper extends $AttachmentEntityMapper {
  const AttachmentEntityMapper();

  static dynamic mapId(dynamic input) {
    return input.id;
  }

  static dynamic mapUserId(dynamic input) {
    return input.userId;
  }

  static dynamic mapToId(GPAttachmentFileEntity input) {
    return input.id.toString();
  }

  static dynamic mapToUserId(GPAttachmentFileEntity input) {
    return input.userId.toString();
  }

  static GPAttachmentFileUploadType convertFileTypeToGPAttachmentFileUploadType(
    FileType fileType,
    String fileExtension,
  ) {
    switch (fileType) {
      case FileType.image:
      case FileType.media:
        if (kPhotoFileExtensions.contains(fileExtension.replaceAll('.', ''))) {
          return GPAttachmentFileUploadType.image;
        } else {
          return GPAttachmentFileUploadType.video;
        }
      case FileType.video:
        return GPAttachmentFileUploadType.video;
      default:
        return GPAttachmentFileUploadType.file;
    }
  }

  static GPApiUploadType convertFromGPAttachmentFileUploadType(
      GPAttachmentFileUploadType input) {
    switch (input) {
      case GPAttachmentFileUploadType.file:
        return GPApiUploadType.files;
      case GPAttachmentFileUploadType.image:
        return GPApiUploadType.image;
      case GPAttachmentFileUploadType.video:
        return GPApiUploadType.video;
      case GPAttachmentFileUploadType.audio:
        return GPApiUploadType.audio;
    }
  }

  static GPAttachmentFileUploadType convertFromGPAttachmentFileType(
      GPAttachmentFileType input) {
    if (input.isImage) {
      return GPAttachmentFileUploadType.image;
    } else if (input.isVideo) {
      return GPAttachmentFileUploadType.video;
    } else if (input.isAudio) {
      return GPAttachmentFileUploadType.audio;
    } else {
      return GPAttachmentFileUploadType.file;
    }
  }

  static GPAttachmentFileUploadType mapType(UploadImageResponseModel input) {
    if (input.type != null) {
      return GPAttachmentFileUploadType.values.byName(input.type!);
    }

    return GPAttachmentFileUploadType.image;
  }

  static GPAttachmentFileUploadType? mapTypeFile(
      UploadFileResponseModel input) {
    if (input.uploadType?.isEmpty ?? true) return null;
    return GPAttachmentFileUploadType.values.byName(input.uploadType!);
  }

  static String? mapFileName(UploadFileResponseModel input) {
    if (input.uploadType == null) return null;
    return input.name;
  }

  static GPAttachmentFileType mapFileType(dynamic input) {
    if (input is UploadFileResponseModel ||
        input is UploadFileResponseModelV2) {
      if (input.fileType != null) {
        try {
          return GPAttachmentFileType.values.byName(input.fileType!);
        } catch (e) {
          return GPAttachmentFileType.unknow;
        }
      }
    }

    if (input is UploadImageResponseModel ||
        input is UploadImageResponseModelV2) {
      if (input.fileType != null) {
        return GPAttachmentFileType.values.byName(input.fileType!);
      }
    }

    return GPAttachmentFileType.unknow;
  }

  static String mapFileTypeEntity(GPAttachmentFileEntity input) {
    return input.fileType.name;
  }

  static String? mapUploadTypeEntity(GPAttachmentFileEntity input) {
    return input.uploadType?.type;
  }

  static GpAttachmentFileUrlEntity? mapUrlV2(UploadFileResponseModelV2 input) {
    if (input.url == null) return null;
    return GpAttachmentFileUrlEntity(
      src: input.url,
    );
  }

  static String? mapToUrlV2(GPAttachmentFileEntity input) {
    return 'https://${input.url?.store}${input.url?.src}';
  }
}
