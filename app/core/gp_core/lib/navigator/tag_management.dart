import 'package:gp_core/routes/router_name.dart';
import 'package:gp_shared_dep/gp_shared_dep.dart';

class TagController extends GetxService {
  final Map<String, List<String>> _tags = {};

  Map<String, List<String>> get tags => _tags;

  void putTag(String route) {
    route = GPTagUtils().getRoute(route);
    _tags[route] ??= [];
    _tags[route]!.add(DateTime.now().millisecondsSinceEpoch.toString());
  }

  void removeTag(String route) {
    final List<String>? currentTags = _tags[route];
    if (currentTags?.isNotEmpty ?? false) {
      currentTags?.remove(currentTags.last);
    }
  }

  String getTagByRoute(String route) {
    route = GPTagUtils().getRoute(route);

    assert(_tags[route] != null);
    assert(_tags[route]!.isNotEmpty);

    return _tags[route]!.last;
  }

  T? controllerByRoute<T extends GetxController>(String route) {
    if (Get.find<TagController>().tags.contains<PERSON>ey(route)) {
      final tag = Get.find<TagController>().getTagByRoute(route);
      return GPTagUtils().findControllerByTag<T>(tag);
    }

    return null;
  }
}

abstract class GPGetView<T extends GetxController> extends GetView<T> {
  GPGetView({this.tagName, super.key});

  final String? tagName;

  /// sử dụng biến lưu trữ lại tagName,
  /// tránh trường hợp chuyển màn, `Get.currentRoute` update khiến tag bị sai
  final List<String> _tags = [];

  @override
  String get tag {
    if (_tags.isEmpty) {
      _tags.add(
          tagName ?? Get.find<TagController>().getTagByRoute(Get.currentRoute));
    }
    return _tags.first;
  }

  @override
  T get controller {
    return Get.find<T>(tag: tag);
  }
}

/// Lưu ý: generic is required
abstract class GPBindings<T extends GetxController> extends Bindings {
  T dep(String tag);

  @override
  void dependencies() {
    final String tag =
        Get.find<TagController>().getTagByRoute(Get.currentRoute);
    Get.lazyPut(
      () => dep(tag),
      tag: tag,
    );
  }
}

class GPTagUtils {
  static final GPTagUtils _instance = GPTagUtils._();

  factory GPTagUtils() => _instance;

  GPTagUtils._();

  String getRoute(String route) {
    if (route.isEmpty) return route;

    if (route.startsWith(RouterName.loading)) {
      route = route.replaceAll(RouterName.loading, "").replaceAll("?next=", "");
    } else if (route.contains("?")) {
      return route.split("?").first;
    }

    return route.replaceAll("Root", "");
  }

  T? findControllerByTag<T>(String parentTag) {
    bool isRegistered = Get.isRegistered<T>(tag: parentTag);
    if (isRegistered) {
      return Get.find<T>(tag: parentTag);
    }

    assert(isRegistered, "${T.runtimeType} is not registered!!!");

    return null;
  }
}
